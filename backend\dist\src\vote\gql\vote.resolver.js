"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VoteResolver = void 0;
const graphql_1 = require("@nestjs/graphql");
const zod_1 = require("../../zod");
const vote_service_1 = require("../vote.service");
const Dto = require("./dto");
const current_user_decorator_1 = require("../../auth/gql/current-user.decorator");
let VoteResolver = class VoteResolver {
    constructor(voteService) {
        this.voteService = voteService;
    }
    async createVote(input, user) {
        await this.voteService.create({
            userId: user.id,
            votingId: input.votingId,
            optionId: input.optionId,
        });
        return true;
    }
};
exports.VoteResolver = VoteResolver;
__decorate([
    (0, graphql_1.Mutation)("createVote"),
    __param(0, (0, graphql_1.Args)("input", new zod_1.ZodPipe(Dto.CreateVoteInput))),
    __param(1, (0, current_user_decorator_1.GqlCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], VoteResolver.prototype, "createVote", null);
exports.VoteResolver = VoteResolver = __decorate([
    (0, graphql_1.Resolver)(),
    __metadata("design:paramtypes", [vote_service_1.VoteService])
], VoteResolver);
//# sourceMappingURL=vote.resolver.js.map