import {
    createParamDecorator,
    ExecutionContext,
    UnauthorizedException,
} from "@nestjs/common";
import { HTTP_CONTEXT_USER_SYMBOL } from "src/consts";
import { CurrentUser } from "../types";

export const HttpCurrentUser = createParamDecorator(
    (_: unknown, ctx: ExecutionContext) => {
        const request = ctx.switchToHttp().getRequest();

        const user: CurrentUser | undefined = request.user;

        if (!user) {
            throw new UnauthorizedException();
        }

        request[HTTP_CONTEXT_USER_SYMBOL] = user;

        return user;
    },
);
