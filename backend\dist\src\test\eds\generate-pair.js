"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const promises_1 = require("node:fs/promises");
const node_crypto_1 = require("node:crypto");
const PRIVATE_KEY_PASSPHRASE = "secret";
const { publicKey, privateKey } = (0, node_crypto_1.generateKeyPairSync)("rsa", {
    modulusLength: 4096,
    publicKeyEncoding: {
        type: "spki",
        format: "pem",
    },
    privateKeyEncoding: {
        type: "pkcs8",
        format: "pem",
        cipher: "aes-256-cbc",
        passphrase: PRIVATE_KEY_PASSPHRASE,
    },
});
(async () => {
    const name = process.argv[2] ?? String(Date.now());
    await promises_1.default.writeFile(`./src/test/eds/${name}-public.pem`, publicKey);
    await promises_1.default.writeFile(`./src/test/eds/${name}-private.pem`, privateKey);
})();
//# sourceMappingURL=generate-pair.js.map