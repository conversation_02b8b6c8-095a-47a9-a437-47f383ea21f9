import { Injectable, InternalServerErrorException } from "@nestjs/common";
import prisma, { Prisma } from "@prisma/client";
import { CurrentUser } from "src/auth/types";
import { BaseService } from "src/common/base-service";
import { getError } from "src/common/errors";
import { PrismaService } from "src/prisma/prisma.service";
import { toPrismaPagination } from "src/utils";

@Injectable()
export class CommuneMemberService extends BaseService {
    constructor(private readonly prisma: PrismaService) {
        super("commune-member");
    }

    async canGet(id: string, user: CurrentUser): Promise<true> {
        await this.getOneOrThrow(id);

        return true;
    }

    async check(ids: string[]) {
        return await this._check(
            ids,
            this.prisma.communeMember,
            "commune-member",
        );
    }

    private async getFullMember(member: prisma.CommuneMember) {
        if (member.actorType === "user") {
            await this.prisma.user.findUniqueOrThrow({
                where: { id: member.actorId },
            });

            const user = await this.prisma.user.findUniqueOrThrow({
                where: { id: member.actorId },
                include: {
                    name: true,
                    images: true,
                },
            });

            return {
                ...member,
                name: user.name,
                images: user.images,
            };
        }

        if (member.actorType === "commune") {
            const commune = await this.prisma.commune.findUniqueOrThrow({
                where: { id: member.actorId },
                include: {
                    name: true,
                    images: true,
                },
            });

            return {
                ...member,
                name: commune.name,
                images: commune.images,
            };
        }

        throw new InternalServerErrorException(
            ...getError("commune_member_actor_type_wrong"),
        );
    }

    async getOne(id: string) {
        const member = await this.prisma.communeMember.findUnique({
            where: { id, deletedAt: null },
        });

        return member && (await this.getFullMember(member));
    }

    async getOneOrThrow(id: string) {
        const communeMember = await this.getOne(id);

        if (!communeMember) {
            throw this.createNotFoundException();
        }

        return communeMember;
    }

    async getMany(
        where: Prisma.CommuneMemberWhereInput,
        pagination?: { page: number; size: number },
    ) {
        const members = await this.prisma.communeMember.findMany({
            ...toPrismaPagination(pagination),
            where: {
                ...where,
                deletedAt: null,
            },
        });

        return await Promise.all(
            members.map((member) => this.getFullMember(member)),
        );
    }

    async createOne(data: Prisma.CommuneMemberCreateInput) {
        const member = await this.prisma.communeMember.create({ data });

        return await this.getFullMember(member);
    }

    async createMany(data: Prisma.CommuneMemberCreateManyInput[]) {
        return await this.prisma.communeMember.createMany({ data });
    }

    async updateOne(id: string, data: Prisma.CommuneMemberUpdateInput) {
        const member = await this.prisma.communeMember.update({
            where: {
                id,
                deletedAt: null,
            },
            data,
        });

        return await this.getFullMember(member);
    }

    async updateMany(
        where: Prisma.CommuneMemberWhereInput,
        data: Prisma.CommuneMemberUpdateInput,
    ) {
        return await this.prisma.communeMember.updateMany({
            where: {
                ...where,
                deletedAt: null,
            },
            data,
        });
    }

    async softDeleteOne(id: string) {
        return await this.updateOne(id, { deletedAt: new Date() });
    }

    async softDeleteMany(where: Prisma.CommuneMemberWhereInput) {
        return await this.updateMany(
            { ...where, deletedAt: null },
            { deletedAt: new Date() },
        );
    }

    async deleteOne(id: string) {
        return await this.prisma.communeMember.delete({ where: { id } });
    }

    async deleteMany(where: Prisma.CommuneMemberWhereInput) {
        return await this.prisma.communeMember.deleteMany({ where });
    }
}
