import { z } from "zod";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "src/zod";
export declare const votesRequired: z.<PERSON>;
export declare const votingOptionTypename = "VotingOption";
export type VotingOption = ZodHelper.Infer<typeof VotingOption>;
export declare const VotingOption: z.ZodObject<{
    __typename: z.Zod<PERSON>efault<z.ZodL<PERSON>al<"VotingOption">>;
} & {
    id: z.ZodString;
    title: z.<PERSON><z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodType<PERSON>ny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    createdAt: z.<PERSON><z.ZodU<PERSON>n<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    updatedAt: z.<PERSON><PERSON><PERSON><PERSON>e<z.<PERSON><[z.<PERSON><PERSON>, z.<PERSON>, z.<PERSON>]>, z.<PERSON>>;
}, z.<PERSON>, z.<PERSON>, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    __typename: "VotingOption";
    title: {
        locale: "en" | "ru";
        value: string;
    }[];
}, {
    id: string;
    createdAt: string | number | Date;
    updatedAt: string | number | Date;
    title: {
        locale: "en" | "ru";
        value: string;
    }[];
    __typename?: "VotingOption" | undefined;
}>;
export declare const VotingOptions: z.ZodArray<z.ZodObject<{
    __typename: z.ZodDefault<z.ZodLiteral<"VotingOption">>;
} & {
    id: z.ZodString;
    title: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
}, z.UnknownKeysParam, z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    __typename: "VotingOption";
    title: {
        locale: "en" | "ru";
        value: string;
    }[];
}, {
    id: string;
    createdAt: string | number | Date;
    updatedAt: string | number | Date;
    title: {
        locale: "en" | "ru";
        value: string;
    }[];
    __typename?: "VotingOption" | undefined;
}>, "many">;
export declare const typename = "Voting";
export type Voting = ZodHelper.Infer<typeof Voting>;
export declare const Voting: z.ZodObject<{
    __typename: z.ZodDefault<z.ZodLiteral<"Voting">>;
} & {
    id: z.ZodString;
    votesRequired: z.ZodNumber;
    endsAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    title: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    options: z.ZodArray<z.ZodObject<{
        __typename: z.ZodDefault<z.ZodLiteral<"VotingOption">>;
    } & {
        id: z.ZodString;
        title: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            locale: "en" | "ru";
            value: string;
        }, {
            locale: "en" | "ru";
            value: string;
        }>, "many">;
        createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
        updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    }, z.UnknownKeysParam, z.ZodTypeAny, {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        __typename: "VotingOption";
        title: {
            locale: "en" | "ru";
            value: string;
        }[];
    }, {
        id: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
        title: {
            locale: "en" | "ru";
            value: string;
        }[];
        __typename?: "VotingOption" | undefined;
    }>, "many">;
    createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
}, z.UnknownKeysParam, z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    options: {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        __typename: "VotingOption";
        title: {
            locale: "en" | "ru";
            value: string;
        }[];
    }[];
    __typename: "Voting";
    votesRequired: number;
    endsAt: Date;
    title: {
        locale: "en" | "ru";
        value: string;
    }[];
}, {
    id: string;
    createdAt: string | number | Date;
    updatedAt: string | number | Date;
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    options: {
        id: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
        title: {
            locale: "en" | "ru";
            value: string;
        }[];
        __typename?: "VotingOption" | undefined;
    }[];
    votesRequired: number;
    endsAt: string | number | Date;
    title: {
        locale: "en" | "ru";
        value: string;
    }[];
    __typename?: "Voting" | undefined;
}>;
export declare const Votings: z.ZodArray<z.ZodObject<{
    __typename: z.ZodDefault<z.ZodLiteral<"Voting">>;
} & {
    id: z.ZodString;
    votesRequired: z.ZodNumber;
    endsAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    title: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    options: z.ZodArray<z.ZodObject<{
        __typename: z.ZodDefault<z.ZodLiteral<"VotingOption">>;
    } & {
        id: z.ZodString;
        title: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            locale: "en" | "ru";
            value: string;
        }, {
            locale: "en" | "ru";
            value: string;
        }>, "many">;
        createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
        updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    }, z.UnknownKeysParam, z.ZodTypeAny, {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        __typename: "VotingOption";
        title: {
            locale: "en" | "ru";
            value: string;
        }[];
    }, {
        id: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
        title: {
            locale: "en" | "ru";
            value: string;
        }[];
        __typename?: "VotingOption" | undefined;
    }>, "many">;
    createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
}, z.UnknownKeysParam, z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    options: {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        __typename: "VotingOption";
        title: {
            locale: "en" | "ru";
            value: string;
        }[];
    }[];
    __typename: "Voting";
    votesRequired: number;
    endsAt: Date;
    title: {
        locale: "en" | "ru";
        value: string;
    }[];
}, {
    id: string;
    createdAt: string | number | Date;
    updatedAt: string | number | Date;
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    options: {
        id: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
        title: {
            locale: "en" | "ru";
            value: string;
        }[];
        __typename?: "VotingOption" | undefined;
    }[];
    votesRequired: number;
    endsAt: string | number | Date;
    title: {
        locale: "en" | "ru";
        value: string;
    }[];
    __typename?: "Voting" | undefined;
}>, "many">;
export type CreateVotingOptionInput = ZodHelper.Infer<typeof CreateVotingOptionInput>;
export declare const CreateVotingOptionInput: z.ZodObject<{
    title: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    title: {
        locale: "en" | "ru";
        value: string;
    }[];
}, {
    title: {
        locale: "en" | "ru";
        value: string;
    }[];
}>;
export type CreateVotingInput = ZodHelper.Infer<typeof CreateVotingInput>;
export declare const CreateVotingInput: z.ZodObject<{
    votesRequired: z.ZodNumber;
    endsAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    title: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    options: z.ZodArray<z.ZodObject<{
        title: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            locale: "en" | "ru";
            value: string;
        }, {
            locale: "en" | "ru";
            value: string;
        }>, "many">;
    }, "strip", z.ZodTypeAny, {
        title: {
            locale: "en" | "ru";
            value: string;
        }[];
    }, {
        title: {
            locale: "en" | "ru";
            value: string;
        }[];
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    options: {
        title: {
            locale: "en" | "ru";
            value: string;
        }[];
    }[];
    votesRequired: number;
    endsAt: Date;
    title: {
        locale: "en" | "ru";
        value: string;
    }[];
}, {
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    options: {
        title: {
            locale: "en" | "ru";
            value: string;
        }[];
    }[];
    votesRequired: number;
    endsAt: string | number | Date;
    title: {
        locale: "en" | "ru";
        value: string;
    }[];
}>;
