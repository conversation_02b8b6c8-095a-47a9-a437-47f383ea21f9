"use client";

import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";

export default function Breadcrumbs() {
  const pathname = usePathname();

  // Skip rendering breadcrumbs on home page
  if (pathname === "/") {
    return null;
  }

  // Split path into segments
  const segments = pathname
    .split("/")
    .filter(segment => segment !== "");

  // Generate breadcrumb items
  const breadcrumbItems = [
    { label: "Home", href: "/" },
    ...segments.map((segment, index) => {
      const href = `/${segments.slice(0, index + 1).join("/")}`;
      // Format the label - capitalize and replace hyphens with spaces
      const label = segment
        .split("-")
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ");

      return { label, href };
    }),
  ];

  return (
    <nav aria-label="breadcrumb" className="mb-4">
      <ol className="breadcrumb">
        {breadcrumbItems.map((item, index) => (
          <li
            key={item.href}
            className={`breadcrumb-item ${index === breadcrumbItems.length - 1 ? "active" : ""}`}
            {...(index === breadcrumbItems.length - 1 ? { "aria-current": "page" } : {})}
          >
            {index === breadcrumbItems.length - 1 ? (
              <span>{item.label}</span>
            ) : (
              <Link
                href={item.href}
              >
                {item.label}
              </Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
}
