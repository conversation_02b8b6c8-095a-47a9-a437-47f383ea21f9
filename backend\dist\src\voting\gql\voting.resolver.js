"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VotingResolver = void 0;
const graphql_1 = require("@nestjs/graphql");
const zod_1 = require("../../zod");
const voting_service_1 = require("../voting.service");
const Dto = require("./dto");
let VotingResolver = class VotingResolver {
    constructor(votingService) {
        this.votingService = votingService;
    }
    async getVotings() {
        const votings = await this.votingService.getMany({});
        return zod_1.ZodHelper.parseInput(Dto.Votings, votings);
    }
    async getVoting(id) {
        const voting = await this.votingService.getOneOrThrow(id);
        return zod_1.ZodHelper.parseInput(Dto.Voting, voting);
    }
    async createVoting(input) {
        const voting = await this.votingService.create(input);
        return zod_1.ZodHelper.parseInput(Dto.Voting, {
            ...voting,
            endsAt: voting.endsAt.toISOString(),
        });
    }
};
exports.VotingResolver = VotingResolver;
__decorate([
    (0, graphql_1.Query)("getVotings"),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], VotingResolver.prototype, "getVotings", null);
__decorate([
    (0, graphql_1.Query)("getVoting"),
    __param(0, (0, graphql_1.Args)("id", new zod_1.ZodPipe(zod_1.ZodHelper.Uuid))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], VotingResolver.prototype, "getVoting", null);
__decorate([
    (0, graphql_1.Mutation)("createVoting"),
    __param(0, (0, graphql_1.Args)("input", new zod_1.ZodPipe(Dto.CreateVotingInput))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], VotingResolver.prototype, "createVoting", null);
exports.VotingResolver = VotingResolver = __decorate([
    (0, graphql_1.Resolver)(),
    __metadata("design:paramtypes", [voting_service_1.VotingService])
], VotingResolver);
//# sourceMappingURL=voting.resolver.js.map