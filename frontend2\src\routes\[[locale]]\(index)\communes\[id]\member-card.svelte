<script lang="ts">
  import type { Image, Locale, Localization } from "$lib";

  import { findLocalizationForUserLocales, fetchWithAuth } from "$lib";
  import CommuneImageCarousel from "../commune-image-carousel.svelte";

  type CommuneMemberType = "user" | "commune";

  interface Props {
    id: string;
    actorType: CommuneMemberType;
    actorId: string;
    name: Localization[];
    isHead: boolean;
    joinedAt: string;
    communeId: string;
    isCurrentUserHead: boolean;
    locale: Locale;
    onMemberRemoved: () => void;
    images?: Image[];
  }

  const i18n = {
    en: {
      head: "Head",
      joined: "Joined",
      remove: "Remove",
      removing: "Removing...",
      confirmRemoval: "Confirm Removal",
      confirmRemoveMessage: "Are you sure you want to remove",
      fromCommune: "from this commune?",
      cannotUndo: "This action cannot be undone.",
      cancel: "Cancel",
      removeMember: "Remove Member",
      errorRemovingMember: "Failed to remove member",
      errorOccurred: "An error occurred while removing member",
      dateFormatLocale: "en-US",
    },
    ru: {
      head: "Глава",
      joined: "Присоединился",
      remove: "Удалить",
      removing: "Удаление...",
      confirmRemoval: "Подтвердите удаление",
      confirmRemoveMessage: "Вы уверены, что хотите удалить",
      fromCommune: "из этой коммуны?",
      cannotUndo: "Это действие нельзя отменить.",
      cancel: "Отмена",
      removeMember: "Удалить участника",
      errorRemovingMember: "Не удалось удалить участника",
      errorOccurred: "Произошла ошибка при удалении участника",
      dateFormatLocale: "ru-RU",
    },
  };

  const {
    id,
    actorType,
    actorId,
    name,
    isHead,
    joinedAt,
    communeId,
    isCurrentUserHead,
    locale,
    onMemberRemoved,
    images = [],
  }: Props = $props();

  const t = i18n[locale];

  let showConfirmModal = $state(false);
  let isRemoving = $state(false);
  let error = $state<string | null>(null);

  const memberName = findLocalizationForUserLocales(name);
  const profileUrl = actorType === "user" ? `/users/${actorId}` : `/communes/${actorId}`;

  // Format join date
  const joinDate = new Date(joinedAt);
  const formattedDate = joinDate.toLocaleDateString(t.dateFormatLocale, {
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  // Don't allow removing the head member
  const canRemove = isCurrentUserHead && !isHead;

  function handleRemoveClick(e: Event) {
    e.preventDefault();
    e.stopPropagation();
    showConfirmModal = true;
  }

  async function handleConfirmRemove() {
    isRemoving = true;
    error = null;

    try {
      const response = await fetchWithAuth(`/api/commune?id=${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || t.errorRemovingMember);
      }

      showConfirmModal = false;
      onMemberRemoved();
    } catch (err) {
      error = err instanceof Error ? err.message : t.errorOccurred;
      console.error(err);
    } finally {
      isRemoving = false;
    }
  }

  function closeModal() {
    showConfirmModal = false;
  }
</script>

<div class={`card h-100 shadow-sm ${isHead ? "head-member" : ""}`}>
  <CommuneImageCarousel {images} communeId={actorId} {locale} />
  <a href={profileUrl} class="text-decoration-none text-black">
    <div class="card-body d-flex flex-column">
      <h5 class="card-title fs-5 text-truncate">
        {memberName}
      </h5>
      <div class="mt-auto d-flex justify-content-between align-items-center">
        <small class="text-muted">
          {t.joined}
          {formattedDate}
        </small>
        {#if isHead}
          <span class="badge bg-warning text-dark">{t.head}</span>
        {/if}
      </div>

      {#if canRemove}
        <div class="mt-2 text-end">
          <button class="btn btn-outline-danger btn-sm w-100" onclick={handleRemoveClick}>
            {t.remove}
          </button>
        </div>
      {/if}
    </div>
  </a>
</div>

<!-- Confirmation Modal -->
{#if showConfirmModal}
  <div
    class="modal fade show"
    style="display: block;"
    tabindex="-1"
    aria-modal="true"
    role="dialog"
  >
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">{t.confirmRemoval}</h5>
          <button type="button" class="btn-close" aria-label="Close" onclick={closeModal}></button>
        </div>
        <div class="modal-body">
          {#if error}
            <div class="alert alert-danger">{error}</div>
          {/if}
          <p>{t.confirmRemoveMessage} <strong>{memberName}</strong> {t.fromCommune}</p>
          <p class="text-muted small">{t.cannotUndo}</p>
        </div>
        <div class="modal-footer">
          <button
            type="button"
            class="btn btn-secondary"
            onclick={closeModal}
            disabled={isRemoving}
          >
            {t.cancel}
          </button>
          <button
            type="button"
            class="btn btn-danger"
            onclick={handleConfirmRemove}
            disabled={isRemoving}
          >
            {isRemoving ? t.removing : t.removeMember}
          </button>
        </div>
      </div>
    </div>
    <div class="modal-backdrop fade show"></div>
  </div>
{/if}

<style>
  .head-member {
    border: 2px solid #ffc107;
    box-shadow: 0 0.25rem 0.75rem rgba(255, 193, 7, 0.15) !important;
  }
</style>
