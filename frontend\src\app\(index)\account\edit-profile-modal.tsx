"use client";

import { useState, useEffect } from "react";
import { Modal, Button, Form, Alert } from "react-bootstrap";
import { Locale, Localization } from "@/utils/find-localization";
import { useHttpRequest } from "@/app/hooks/use-http-request";
import { LocalizedInput } from "@/components/localized-input";
import { LocalizedTextarea } from "@/components/localized-textarea";
import { useLocale } from "next-intl";

interface EditProfileModalProps {
  show: boolean;
  onHide: () => void;
  userData: {
    id: string;
    name: Localization[];
    description: Localization[];
  } | null;
  onProfileUpdated: () => void;
}

const i18n = {
  en: {
    editProfile: "Edit Profile",
    name: {
      label: "Name",
      placeholder: "Enter your name",
    },
    description: {
      label: "Description (optional)",
      placeholder: "Tell us about yourself",
    },
    saveChanges: "Save Changes",
    cancel: "Cancel",
    saving: "Saving...",
    nameRequired: "Name is required",
    failedToUpdateProfile: "Failed to update profile",
    profileUpdatedSuccessfully: "Profile updated successfully",
    errorOccurred: "An error occurred while updating profile",
  },

  ru: {
    editProfile: "Редактировать профиль",
    name: {
      label: "Имя",
      placeholder: "Введите ваше имя",
    },
    description: {
      label: "Описание (необязательно)",
      placeholder: "Расскажите о себе",
    },
    saveChanges: "Сохранить изменения",
    cancel: "Отменить",
    saving: "Сохранение...",
    nameRequired: "Имя обязательно",
    failedToUpdateProfile: "Не удалось обновить профиль",
    profileUpdatedSuccessfully: "Профиль обновлен успешно",
    errorOccurred: "Произошла ошибка при обновлении профиля",
  },
};

export function EditProfileModal({ show, onHide, userData, onProfileUpdated }: EditProfileModalProps) {
  const locale = useLocale() as Locale;
  const t = i18n[locale];

  const httpRequest = useHttpRequest();

  const [name, setName] = useState<Localization[]>([]);
  const [description, setDescription] = useState<Localization[]>([]);
  const [error, setError] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  // Initialize form with user data when modal opens or userData changes
  useEffect(() => {
    if (userData) {
      setName(userData.name || []);
      setDescription(userData.description || []);
    }
  }, [userData, show]);

  const handleSubmit = async () => {
    if (!name.some(item => item.value.trim().length)) {
      setError(t.nameRequired);
      return;
    }

    setIsSubmitting(true);
    setError("");

    try {
      const response = await httpRequest(`/api/user/${userData?.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name,
          description,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || t.failedToUpdateProfile);
      }

      setSubmitSuccess(true);
      onProfileUpdated();

      // Close modal after a short delay
      setTimeout(() => {
        handleClose();
      }, 1500);
    } catch (err) {
      setError(err instanceof Error ? err.message : t.errorOccurred);
      console.error(err);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    // Reset form state
    if (userData) {
      setName(userData.name || []);
      setDescription(userData.description || []);
    }
    setError("");
    setSubmitSuccess(false);
    onHide();
  };

  return (
    <Modal show={show} onHide={handleClose} centered>
      <Modal.Header closeButton>
        <Modal.Title>{t.editProfile}</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        {submitSuccess && (
          <Alert variant="success" className="mb-3">
            {t.profileUpdatedSuccessfully}
          </Alert>
        )}

        {error && (
          <Alert variant="danger" className="mb-3">
            {error}
          </Alert>
        )}

        <Form onSubmit={(e) => { e.preventDefault(); handleSubmit(); }}>
          <LocalizedInput
            id="profileName"
            label={t.name.label}
            placeholder={t.name.placeholder}
            required={true}
            value={name}
            onChange={setName}
          />

          <LocalizedTextarea
            id="profileDescription"
            label={t.description.label}
            placeholder={t.description.placeholder}
            rows={4}
            value={description}
            onChange={setDescription}
          />
        </Form>
      </Modal.Body>
      <Modal.Footer>
        <Button
          variant="secondary"
          onClick={handleClose}
          disabled={isSubmitting}
        >
          {t.cancel}
        </Button>
        <Button
          variant="primary"
          disabled={!name.some(item => item.value.trim().length) || isSubmitting}
          onClick={handleSubmit}
        >
          {isSubmitting ? t.saving : t.saveChanges}
        </Button>
      </Modal.Footer>
    </Modal>
  );
}
