"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Pagination = exports.pagination = exports.positiveIntSchema = exports.intSchema = exports.Image = exports.Email = exports.Uuid = exports.Localizations = exports.Localization = exports.Locales = exports.Locale = exports.ToDateTime = void 0;
exports.Typename = Typename;
exports.JsonToObject = JsonToObject;
exports.FormDataToObject = FormDataToObject;
exports.GqlObject = GqlObject;
exports.parseInput = parseInput;
exports.parseUnknown = parseUnknown;
const zod_1 = require("zod");
function Typename(name) {
    return zod_1.z.literal(name).default(name);
}
exports.ToDateTime = zod_1.z
    .union([zod_1.z.number(), zod_1.z.string(), zod_1.z.date()])
    .pipe(zod_1.z.coerce.date());
function JsonToObject(schema) {
    return zod_1.z
        .string()
        .transform((value) => JSON.parse(value))
        .pipe(zod_1.z.object(schema));
}
function FormDataToObject(schema) {
    return zod_1.z.object({
        data: JsonToObject(schema),
    });
}
exports.Locale = zod_1.z.enum(["en", "ru"]).describe("Locale");
exports.Locales = zod_1.z.array(exports.Locale).min(1);
exports.Localization = zod_1.z.object({
    locale: exports.Locale,
    value: zod_1.z.string().nonempty(),
});
exports.Localizations = zod_1.z.array(exports.Localization);
exports.Uuid = zod_1.z.string().uuid().describe("UUID");
exports.Email = zod_1.z.string().email().describe("Email");
exports.Image = zod_1.z.object({
    id: exports.Uuid,
    url: zod_1.z.string(),
    createdAt: exports.ToDateTime,
    updatedAt: exports.ToDateTime,
});
exports.intSchema = zod_1.z.coerce.number().int();
exports.positiveIntSchema = exports.intSchema.positive();
exports.pagination = {
    offset: exports.intSchema.default(0).describe("Offset"),
    limit: exports.positiveIntSchema.max(100).default(20).describe("Limit"),
    page: exports.positiveIntSchema.default(1).describe("Page number"),
    size: exports.positiveIntSchema.max(100).default(20).describe("Page size"),
};
exports.Pagination = zod_1.z
    .union([
    zod_1.z.object({
        page: exports.pagination.page,
        size: exports.pagination.size,
    }),
    zod_1.z.object({
        limit: exports.pagination.limit,
        offset: exports.pagination.offset,
    }),
])
    .optional();
function GqlObject(typename, shape) {
    return zod_1.z.object({
        __typename: zod_1.z.literal(typename).default(typename),
        ...shape,
    });
}
function parseInput(schema, value) {
    return schema.parse(value);
}
function parseUnknown(schema, value) {
    return schema.parse(value);
}
//# sourceMappingURL=helper.js.map