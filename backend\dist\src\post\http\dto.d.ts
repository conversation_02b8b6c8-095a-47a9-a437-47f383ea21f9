import { z, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "src/zod";
export type PostStatus = ZodHelper.Infer<typeof PostStatus>;
export declare const PostStatus: z.Zod<PERSON><PERSON>Enum<{
    draft: "draft";
    published: "published";
    archived: "archived";
}>;
export type Post = ZodHelper.Infer<typeof Post>;
export declare const Post: z.ZodObject<{
    id: z.ZodString;
    title: z.<PERSON><z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodType<PERSON>ny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    description: z.<PERSON><z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    status: z.Z<PERSON><{
        draft: "draft";
        published: "published";
        archived: "archived";
    }>;
    publishedAt: z.ZodNullable<z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>>;
    images: z.ZodOptional<z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        url: z.ZodString;
        createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
        updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        url: string;
    }, {
        id: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
        url: string;
    }>, "many">>;
    createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    status: "draft" | "published" | "archived";
    title: {
        locale: "en" | "ru";
        value: string;
    }[];
    publishedAt: Date | null;
    images?: {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        url: string;
    }[] | undefined;
}, {
    id: string;
    createdAt: string | number | Date;
    updatedAt: string | number | Date;
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    status: "draft" | "published" | "archived";
    title: {
        locale: "en" | "ru";
        value: string;
    }[];
    publishedAt: string | number | Date | null;
    images?: {
        id: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
        url: string;
    }[] | undefined;
}>;
export declare const Posts: z.ZodArray<z.ZodObject<{
    id: z.ZodString;
    title: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    status: z.ZodNativeEnum<{
        draft: "draft";
        published: "published";
        archived: "archived";
    }>;
    publishedAt: z.ZodNullable<z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>>;
    images: z.ZodOptional<z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        url: z.ZodString;
        createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
        updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        url: string;
    }, {
        id: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
        url: string;
    }>, "many">>;
    createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    status: "draft" | "published" | "archived";
    title: {
        locale: "en" | "ru";
        value: string;
    }[];
    publishedAt: Date | null;
    images?: {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        url: string;
    }[] | undefined;
}, {
    id: string;
    createdAt: string | number | Date;
    updatedAt: string | number | Date;
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    status: "draft" | "published" | "archived";
    title: {
        locale: "en" | "ru";
        value: string;
    }[];
    publishedAt: string | number | Date | null;
    images?: {
        id: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
        url: string;
    }[] | undefined;
}>, "many">;
export type CreatePostInput = ZodHelper.Infer<typeof CreatePostInput>;
export declare const CreatePostInput: z.ZodPipeline<z.ZodEffects<z.ZodString, any, string>, z.ZodObject<{
    title: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    status: z.ZodNativeEnum<{
        draft: "draft";
        published: "published";
        archived: "archived";
    }>;
    publishedAt: z.ZodNullable<z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>>;
}, "strip", z.ZodTypeAny, {
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    status: "draft" | "published" | "archived";
    title: {
        locale: "en" | "ru";
        value: string;
    }[];
    publishedAt: Date | null;
}, {
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    status: "draft" | "published" | "archived";
    title: {
        locale: "en" | "ru";
        value: string;
    }[];
    publishedAt: string | number | Date | null;
}>>;
export type UpdatePostInput = ZodHelper.Infer<typeof UpdatePostInput>;
export declare const UpdatePostInput: z.ZodObject<{
    title: z.ZodOptional<z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">>;
    description: z.ZodOptional<z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">>;
    status: z.ZodOptional<z.ZodNativeEnum<{
        draft: "draft";
        published: "published";
        archived: "archived";
    }>>;
}, "strip", z.ZodTypeAny, {
    description?: {
        locale: "en" | "ru";
        value: string;
    }[] | undefined;
    status?: "draft" | "published" | "archived" | undefined;
    title?: {
        locale: "en" | "ru";
        value: string;
    }[] | undefined;
}, {
    description?: {
        locale: "en" | "ru";
        value: string;
    }[] | undefined;
    status?: "draft" | "published" | "archived" | undefined;
    title?: {
        locale: "en" | "ru";
        value: string;
    }[] | undefined;
}>;
