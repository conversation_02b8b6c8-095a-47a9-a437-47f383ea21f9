import { Locale } from "@/app/types";

const i18n = {
  en: {
    title: "About Us",
    description: {
      1: "We are Digital Society «Commune», dedicated to improving the world through building worldwide friendly infrastructure and making life easier through technological innovation.",
      2: "Our mission is to create positive change by investing in impactful projects that address real-world challenges. We believe in the power of technology to connect communities, streamline processes, and create opportunities for growth and development.",
      3: "Through strategic investments, educational initiatives, and collaborative partnerships, we aim to build a more accessible, efficient, and equitable world. Our commune brings together forward-thinking individuals who share a vision of technological progress as a means to enhance human well-being.",
    },
    ourVision: {
      title: "Our Vision",
      description: "To create a global network of technological solutions that empower communities, foster innovation, and build a more connected and efficient world.",
    },
    ourApproach: {
      title: "Our Approach",
      description: "We combine technological expertise with community-driven values to develop sustainable solutions, educational programs, and infrastructure projects that make a lasting impact.",
    },
  },

  ru: {
    title: "О нас",
    description: {
      1: "Мы — Цифровое Сообщество «Коммуна», мы стремимся улучшать мир, создавая дружественную инфраструктуру по всему миру и упрощая жизнь с помощью технологических инноваций.",
      2: "Наша миссия — создавать положительные изменения, инвестируя в значимые проекты, решающие реальные проблемы. Мы верим в силу технологий, способных объединять сообщества, упрощать процессы и открывать возможности для роста и развития.",
      3: "Посредством стратегических инвестиций, образовательных инициатив и партнёрств мы стремимся построить более доступный, эффективный и справедливый мир. Наша коммуна объединяет людей с прогрессивным мышлением, которые видят в технологическом прогрессе способ улучшить благополучие человека.",
    },
    ourVision: {
      title: "Наше видение",
      description: "Создание глобальной сети технологических решений, которые укрепляют сообщества, способствуют инновациям и формируют более связанный и эффективный мир.",
    },
    ourApproach: {
      title: "Наш подход",
      description: "Мы объединяем технологическую экспертизу с ценностями, ориентированными на сообщество, чтобы разрабатывать устойчивые решения, образовательные программы и инфраструктурные проекты, создающие долговременный эффект.",
    },
  },

};

type Props = {
  locale: Locale;
}

export async function AboutUsSection({ locale }: Props) {
  const t = i18n[locale];

  return (
    <section className="py-5 py-lg-6 bg-white" id="about">
      <div className="container">
        <div className="row justify-content-center">
          <div className="col-lg-8 text-center mb-4">
            <h2 className="display-5 fw-bold mb-4">{t.title}</h2>
          </div>
        </div>
        <div className="row g-4 align-items-center">
          <div className="col-lg-6">
            <p className="fs-5 mb-4">
              {t.description[1]}
            </p>
            <p className="mb-4">
              {t.description[2]}
            </p>
            <p className="mb-4">
              {t.description[3]}
            </p>
          </div>
          <div className="col-lg-6">
            <div className="card border-0 shadow-lg rounded-4 overflow-hidden">
              <div className="card-body p-5 bg-light">
                <div className="d-flex align-items-center mb-4">
                  <div className="bg-primary rounded-circle p-3 me-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="white" className="bi bi-globe" viewBox="0 0 16 16">
                      <path d="M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8zm7.5-6.923c-.67.204-1.335.82-1.887 1.855A7.97 7.97 0 0 0 5.145 4H7.5V1.077zM4.09 4a9.267 9.267 0 0 1 .64-1.539 6.7 6.7 0 0 1 .597-.933A7.025 7.025 0 0 0 2.255 4H4.09zm-.582 3.5c.03-.877.138-1.718.312-2.5H1.674a6.958 6.958 0 0 0-.656 2.5h2.49zM4.847 5a12.5 12.5 0 0 0-.338 2.5H7.5V5H4.847zM8.5 5v2.5h2.99a12.495 12.495 0 0 0-.337-2.5H8.5zM4.51 8.5a12.5 12.5 0 0 0 .337 2.5H7.5V8.5H4.51zm3.99 0V11h2.653c.187-.765.306-1.608.338-2.5H8.5zM5.145 12c.138.386.295.744.468 1.068.552 1.035 1.218 1.65 1.887 1.855V12H5.145zm.182 2.472a6.696 6.696 0 0 1-.597-.933A9.268 9.268 0 0 1 4.09 12H2.255a7.024 7.024 0 0 0 3.072 2.472zM3.82 11a13.652 13.652 0 0 1-.312-2.5h-2.49c.062.89.291 1.733.656 2.5H3.82zm6.853 3.472A7.024 7.024 0 0 0 13.745 12H11.91a9.27 9.27 0 0 1-.64 1.539 6.688 6.688 0 0 1-.597.933zM8.5 12v2.923c.67-.204 1.335-.82 1.887-1.855.173-.324.33-.682.468-1.068H8.5zm3.68-1h2.146c.365-.767.594-1.61.656-2.5h-2.49a13.65 13.65 0 0 1-.312 2.5zm2.802-3.5a6.959 6.959 0 0 0-.656-2.5H12.18c.174.782.282 1.623.312 2.5h2.49zM11.27 2.461c.247.464.462.98.64 1.539h1.835a7.024 7.024 0 0 0-3.072-2.472c.218.284.418.598.597.933zM10.855 4a7.966 7.966 0 0 0-.468-1.068C9.835 1.897 9.17 1.282 8.5 1.077V4h2.355z"/>
                    </svg>
                  </div>
                  <h3 className="h4 fw-bold mb-0">{t.ourVision.title}</h3>
                </div>
                <p className="mb-0">{t.ourVision.description}</p>
              </div>
              <div className="card-body p-5">
                <div className="d-flex align-items-center mb-4">
                  <div className="bg-primary rounded-circle p-3 me-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" fill="white" className="bi bi-lightbulb" viewBox="0 0 16 16">
                      <path d="M2 6a6 6 0 1 1 10.174 4.31c-.203.196-.359.4-.453.619l-.762 1.769A.5.5 0 0 1 10.5 13a.5.5 0 0 1 0 1 .5.5 0 0 1 0 1l-.224.447a1 1 0 0 1-.894.553H6.618a1 1 0 0 1-.894-.553L5.5 15a.5.5 0 0 1 0-1 .5.5 0 0 1 0-1 .5.5 0 0 1-.46-.302l-.761-1.77a1.964 1.964 0 0 0-.453-.618A5.984 5.984 0 0 1 2 6zm6-5a5 5 0 0 0-3.479 8.592c.263.254.514.564.676.941L5.83 12h4.342l.632-1.467c.162-.377.413-.687.676-.941A5 5 0 0 0 8 1z"/>
                    </svg>
                  </div>
                  <h3 className="h4 fw-bold mb-0">{t.ourApproach.title}</h3>
                </div>
                <p className="mb-0">{t.ourApproach.description}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
