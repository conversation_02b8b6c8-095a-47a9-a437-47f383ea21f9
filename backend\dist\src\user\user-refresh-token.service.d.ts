import { PrismaService } from "src/prisma/prisma.service";
type CreateUserRefreshTokenDto = {
    userId: string;
    token: string;
    expiresAt: Date;
    ipAddress: string | null;
    userAgent: string | null;
};
type RevokeUserRefreshTokenDto = {
    id: string;
    reason: string | null;
};
export declare class UserRefreshTokenService {
    private readonly prisma;
    constructor(prisma: PrismaService);
    create(createUserRefreshTokenDto: CreateUserRefreshTokenDto): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        userId: string;
        token: string;
        expiresAt: Date;
        ipAddress: string | null;
        userAgent: string | null;
        revokedAt: Date | null;
        revokeReason: string | null;
    }>;
    findAll(dto: {
        params: {
            userId: string;
        };
    }): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        userId: string;
        token: string;
        expiresAt: Date;
        ipAddress: string | null;
        userAgent: string | null;
        revokedAt: Date | null;
        revokeReason: string | null;
    }[]>;
    findByValue(value: string): Promise<({
        user: {
            id: string;
            referrerId: string | null;
            email: string;
            role: import(".prisma/client").$Enums.UserRole;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
        };
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        userId: string;
        token: string;
        expiresAt: Date;
        ipAddress: string | null;
        userAgent: string | null;
        revokedAt: Date | null;
        revokeReason: string | null;
    }) | null>;
    softDelete(id: string): Promise<boolean>;
    delete(id: string): Promise<boolean>;
    revoke(dto: RevokeUserRefreshTokenDto): Promise<boolean>;
}
export {};
