.carouselContainer {
  position: relative;
  overflow: hidden;
  border-radius: 0.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.carousel {
  width: 100%;
}

.carouselItem {
  height: 300px;
}

.imageContainer {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
}

.carouselImage {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* Semi-transparent navigation arrows */
.carousel :global(.carousel-control-prev),
.carousel :global(.carousel-control-next) {
  background-color: rgba(0, 0, 0, 0.3);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  top: 50%;
  transform: translateY(-50%);
  margin: 0 15px;
}

/* Indicator dots */
.carousel :global(.carousel-indicators) {
  margin-bottom: 0.5rem;
}

.carousel :global(.carousel-indicators button) {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  margin: 0 3px;
}

.carousel :global(.carousel-indicators button.active) {
  background-color: white;
}
