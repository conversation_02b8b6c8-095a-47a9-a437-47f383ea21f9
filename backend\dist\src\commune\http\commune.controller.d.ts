import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "src/zod";
import { CurrentUser } from "src/auth/types";
import { CommuneService } from "../commune.service";
import * as Dto from "./dto";
import { CommuneMemberService } from "../commune-member.service";
import { UserService } from "src/user/user.service";
export declare class CommuneController {
    private readonly communeService;
    private readonly communeMemberService;
    private readonly userService;
    constructor(communeService: CommuneService, communeMemberService: CommuneMemberService, userService: UserService);
    private getCommuneToReturn;
    getCommunes(page: number, size: number): Promise<Dto.Commune[]>;
    createCommune(body: Dto.CreateCommuneInput, user: CurrentUser, files?: Array<Express.Multer.File>): Promise<Dto.Commune>;
    uploadCommuneImages(id: string, user: CurrentUser, files: Array<Express.Multer.File>): Promise<ZodHelper.Image[]>;
    getCommune(id: string): Promise<Dto.Commune>;
    updateCommune(id: string, body: Dto.UpdateCommuneInput, user: CurrentUser): Promise<Dto.Commune>;
    getCommuneMembers(page: number, size: number, id: string): Promise<Dto.CommuneMember[]>;
    getCommuneMember(memberId: string): Promise<Dto.CommuneMember>;
    createCommuneMember(id: string, createCommuneMemberInput: Dto.CreateCommuneMemberInput): Promise<Dto.CommuneMember>;
    updateCommuneMember(id: string, memberId: string, updateCommuneMemberInput: Dto.UpdateCommuneMemberInput): Promise<Dto.CommuneMember>;
    deleteCommuneMember(id: string): Promise<boolean>;
}
