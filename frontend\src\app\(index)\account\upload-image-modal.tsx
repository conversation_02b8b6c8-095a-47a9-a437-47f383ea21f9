"use client";

import { useState, useRef, ChangeEvent } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Al<PERSON>, Spinner } from "react-bootstrap";
import { useHttpRequest } from "@/app/hooks/use-http-request";
import { useLocale } from "next-intl";
import { Locale } from "@/app/types";

interface UploadImageModalProps {
  show: boolean;
  onHide: () => void;
  userId: string;
  onImageUploaded: () => void;
}

// Constants for file upload validation
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ALLOWED_FILE_TYPES = ["image/jpeg", "image/png", "image/webp"];

const MAX_FILE_SIZE_MB = MAX_FILE_SIZE / (1024 * 1024);

const i18n = {
  en: {
    uploadImage: "Upload Profile Image",
    upload: "Upload",
    cancel: "Cancel",
    uploading: "Uploading...",
    imageUploadedSuccessfully: "Image uploaded successfully!",
    pleaseSelectImage: "Please select an image to upload",
    invalidFileTypeError: "Invalid file type. Please upload a JPG, PNG, or WebP image.",
    fileTooLarge: `File is too large. Maximum size is ${MAX_FILE_SIZE_MB}MB.`,
    failedToUploadImage: "Failed to upload image",
    errorOccurred: "An error occurred while uploading the image",
    uploadImageMaxSize: `Upload an image (JPG, PNG, WebP), max ${MAX_FILE_SIZE_MB}MB.`,
  },

  ru: {
    uploadImage: "Загрузить изображение профиля",
    upload: "Загрузить",
    cancel: "Отменить",
    uploading: "Загрузка...",
    imageUploadedSuccessfully: "Изображение загружено успешно!",
    pleaseSelectImage: "Пожалуйста, выберите изображение для загрузки",
    invalidFileTypeError: "Неверный тип файла. Пожалуйста, загрузите JPG, PNG или WebP изображение.",
    fileTooLarge: `Файл слишком большой. Максимальный размер - ${MAX_FILE_SIZE_MB}MB.`,
    failedToUploadImage: "Не удалось загрузить изображение",
    errorOccurred: "Произошла ошибка при загрузке изображения",
    uploadImageMaxSize: `Загрузите изображение (JPG, PNG, WebP), максимальный размер - ${MAX_FILE_SIZE_MB}MB.`,
  },
};

export function UploadImageModal({ show, onHide, userId, onImageUploaded }: UploadImageModalProps) {
  const locale = useLocale() as Locale;
  const t = i18n[locale];

  const httpRequest = useHttpRequest();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [error, setError] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    setError("");

    if (!files || files.length === 0) {
      setSelectedFile(null);
      setPreviewUrl(null);
      return;
    }

    const file = files[0];

    // Validate file type
    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      setError(t.invalidFileTypeError);
      setSelectedFile(null);
      setPreviewUrl(null);
      return;
    }

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      setError(t.fileTooLarge);
      setSelectedFile(null);
      setPreviewUrl(null);
      return;
    }

    setSelectedFile(file);

    // Create preview URL
    const objectUrl = URL.createObjectURL(file);
    setPreviewUrl(objectUrl);

    return () => {
      URL.revokeObjectURL(objectUrl);
    };
  };

  const handleSubmit = async () => {
    if (!selectedFile) {
      setError(t.pleaseSelectImage);
      return;
    }

    setIsSubmitting(true);
    setError("");

    try {
      const formData = new FormData();
      formData.append("image", selectedFile);

      const response = await httpRequest(`/api/user/${userId}/image`, {
        method: "POST",
        body: formData,
        // Don't set Content-Type header, it will be set automatically with the boundary
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || t.failedToUploadImage);
      }

      setSubmitSuccess(true);
      onImageUploaded();

      // Close modal after a short delay
      setTimeout(() => {
        handleClose();
      }, 1500);
    } catch (err) {
      setError(err instanceof Error ? err.message : t.errorOccurred);
      console.error(err);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setSelectedFile(null);
    setPreviewUrl(null);
    setError("");
    setSubmitSuccess(false);
    onHide();

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  return (
    <Modal show={show} onHide={handleClose} centered>
      <Modal.Header closeButton>
        <Modal.Title>{t.uploadImage}</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        {submitSuccess && (
          <Alert variant="success" className="mb-3">
            {t.imageUploadedSuccessfully}
          </Alert>
        )}

        {error && (
          <Alert variant="danger" className="mb-3">
            {error}
          </Alert>
        )}

        <Form>
          <Form.Group className="mb-3" controlId="profileImage">
            <Form.Label>{t.pleaseSelectImage}</Form.Label>
            <Form.Control
              type="file"
              accept=".jpg,.jpeg,.png,.webp"
              onChange={handleFileChange}
              ref={fileInputRef}
              disabled={isSubmitting}
            />
            <Form.Text className="text-muted">
              {t.uploadImageMaxSize}
            </Form.Text>

            {previewUrl && (
              <div className="mt-3 text-center">
                <img
                  src={previewUrl}
                  alt="Preview"
                  className="img-thumbnail"
                  style={{ maxHeight: "200px" }}
                />
              </div>
            )}
          </Form.Group>
        </Form>
      </Modal.Body>
      <Modal.Footer>
        <Button
          variant="secondary"
          onClick={handleClose}
          disabled={isSubmitting}
        >
          {t.cancel}
        </Button>
        <Button
          variant="primary"
          onClick={handleSubmit}
          disabled={!selectedFile || isSubmitting}
        >
          {isSubmitting ? (
            <>
              <Spinner
                as="span"
                animation="border"
                size="sm"
                role="status"
                aria-hidden="true"
                className="me-2"
              />
              {t.uploading}
            </>
          ) : (
            t.upload
          )}
        </Button>
      </Modal.Footer>
    </Modal>
  );
}
