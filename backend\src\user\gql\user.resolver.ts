import { NotFoundException } from "@nestjs/common";
import { Resolver, Query, Args } from "@nestjs/graphql";
import * as Gql from "src/graphql";
import { Zod<PERSON><PERSON><PERSON>, ZodPipe } from "src/zod";
import { getError } from "src/common/errors";
import { UserService } from "../user.service";
import * as Dto from "./dto";

export type IUserResolver = Gql.IResolver<"getUser" | "getUsers", never>;

@Resolver("User")
export class UserResolver implements IUserResolver {
    constructor(private readonly userService: UserService) {}

    @Query("getUser")
    async getUser(
        @Args("id", new ZodPipe(ZodHelper.Uuid)) id: string,
    ): Promise<Gql.User> {
        const user = await this.userService.getOne(id);

        if (!user) {
            throw new NotFoundException(...getError("user_not_found"));
        }

        return ZodHelper.parseInput(Dto.User, user);
    }

    @Query("getUsers")
    async getUsers(
        @Args("page" /*, new ZodPipe(Dto.pagination.page) */) page: number,
        @Args("size" /*, new ZodPipe(Dto.pagination.size) */) size: number,
    ): Promise<Gql.User[]> {
        console.log({ page, size });

        const users = await this.userService.getMany({}, { page, size });

        console.log(users);

        const result = ZodHelper.parseInput(Dto.Users, users);

        console.log(result);

        return result;
    }
}
