import { z } from "zod";
import { Zod<PERSON><PERSON>per } from "src/zod";
export declare const votesRequired: z.<PERSON>;
export type Voting = ZodHelper.Infer<typeof Voting>;
export declare const Voting: z.ZodObject<{
    id: z.ZodString;
    votesRequired: z.<PERSON>;
    endsAt: z.<PERSON><z.ZodU<PERSON><[z.<PERSON>, z.ZodString, z.ZodDate]>, z.Zod<PERSON>ate>;
    title: z.<PERSON><z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodType<PERSON>ny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    description: z.<PERSON><z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.<PERSON><PERSON><PERSON><PERSON>, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    options: z.<PERSON>y<z.ZodObject<{
        id: z.ZodString;
        title: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            locale: "en" | "ru";
            value: string;
        }, {
            locale: "en" | "ru";
            value: string;
        }>, "many">;
    }, "strip", z.ZodTypeAny, {
        id: string;
        title: {
            locale: "en" | "ru";
            value: string;
        }[];
    }, {
        id: string;
        title: {
            locale: "en" | "ru";
            value: string;
        }[];
    }>, "many">;
    createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    options: {
        id: string;
        title: {
            locale: "en" | "ru";
            value: string;
        }[];
    }[];
    votesRequired: number;
    endsAt: Date;
    title: {
        locale: "en" | "ru";
        value: string;
    }[];
}, {
    id: string;
    createdAt: string | number | Date;
    updatedAt: string | number | Date;
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    options: {
        id: string;
        title: {
            locale: "en" | "ru";
            value: string;
        }[];
    }[];
    votesRequired: number;
    endsAt: string | number | Date;
    title: {
        locale: "en" | "ru";
        value: string;
    }[];
}>;
export declare const Votings: z.ZodArray<z.ZodObject<{
    id: z.ZodString;
    votesRequired: z.ZodNumber;
    endsAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    title: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    options: z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        title: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            locale: "en" | "ru";
            value: string;
        }, {
            locale: "en" | "ru";
            value: string;
        }>, "many">;
    }, "strip", z.ZodTypeAny, {
        id: string;
        title: {
            locale: "en" | "ru";
            value: string;
        }[];
    }, {
        id: string;
        title: {
            locale: "en" | "ru";
            value: string;
        }[];
    }>, "many">;
    createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    options: {
        id: string;
        title: {
            locale: "en" | "ru";
            value: string;
        }[];
    }[];
    votesRequired: number;
    endsAt: Date;
    title: {
        locale: "en" | "ru";
        value: string;
    }[];
}, {
    id: string;
    createdAt: string | number | Date;
    updatedAt: string | number | Date;
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    options: {
        id: string;
        title: {
            locale: "en" | "ru";
            value: string;
        }[];
    }[];
    votesRequired: number;
    endsAt: string | number | Date;
    title: {
        locale: "en" | "ru";
        value: string;
    }[];
}>, "many">;
export type CreateVoting = ZodHelper.Infer<typeof CreateVoting>;
export declare const CreateVoting: z.ZodObject<{
    votesRequired: z.ZodNumber;
    endsAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    title: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    options: z.ZodArray<z.ZodObject<{
        title: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            locale: "en" | "ru";
            value: string;
        }, {
            locale: "en" | "ru";
            value: string;
        }>, "many">;
    }, "strip", z.ZodTypeAny, {
        title: {
            locale: "en" | "ru";
            value: string;
        }[];
    }, {
        title: {
            locale: "en" | "ru";
            value: string;
        }[];
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    options: {
        title: {
            locale: "en" | "ru";
            value: string;
        }[];
    }[];
    votesRequired: number;
    endsAt: Date;
    title: {
        locale: "en" | "ru";
        value: string;
    }[];
}, {
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    options: {
        title: {
            locale: "en" | "ru";
            value: string;
        }[];
    }[];
    votesRequired: number;
    endsAt: string | number | Date;
    title: {
        locale: "en" | "ru";
        value: string;
    }[];
}>;
