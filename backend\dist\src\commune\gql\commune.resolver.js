"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommuneResolver = void 0;
const graphql_1 = require("@nestjs/graphql");
const common_1 = require("@nestjs/common");
const errors_1 = require("../../common/errors");
const zod_1 = require("../../zod");
const jwt_auth_guard_1 = require("../../auth/gql/jwt-auth.guard");
const current_user_decorator_1 = require("../../auth/gql/current-user.decorator");
const commune_service_1 = require("../commune.service");
const commune_member_service_1 = require("../commune-member.service");
const Dto = require("./dto");
let CommuneResolver = class CommuneResolver {
    constructor(communeService, communeMemberService) {
        this.communeService = communeService;
        this.communeMemberService = communeMemberService;
    }
    async getCommune(id) {
        const commune = await this.communeService.getOne(id);
        if (!commune) {
            throw new common_1.NotFoundException(...(0, errors_1.getError)("commune_not_found"));
        }
        return zod_1.ZodHelper.parseInput(Dto.Commune, commune);
    }
    async getCommunes() {
        const communes = await this.communeService.getMany({
            deletedAt: null,
        });
        return zod_1.ZodHelper.parseInput(Dto.Communes, communes);
    }
    async createCommune(input, user) {
        console.dir({
            "CommuneResolver.createCommune.params": {
                input,
                user,
            },
        }, { depth: null });
        const commune = await this.communeService.create(input, user);
        return zod_1.ZodHelper.parseInput(Dto.Commune, commune);
    }
    async updateCommune(input, user) {
        const { id, ...data } = input;
        const commune = await this.communeService.update(id, data, user);
        return zod_1.ZodHelper.parseInput(Dto.Commune, commune);
    }
    async deleteCommune(id, user) {
        await this.communeService.softDeleteOneCascade(id, user);
        return true;
    }
    async getCommuneMember(id) {
        const communeMember = await this.communeMemberService.getOne(id);
        if (!communeMember) {
            throw new common_1.NotFoundException(...(0, errors_1.getError)("commune_member_not_found"));
        }
        const result = Dto.CommuneMember.parse(communeMember);
        return result;
    }
    async getCommuneMembers(communeId) {
        const communeMembers = await this.communeMemberService.getMany({
            communeId,
            deletedAt: null,
        });
        const result = Dto.CommuneMembers.parse(communeMembers);
        return result;
    }
    async createCommuneMember(createCommuneMemberInput) {
        const communeMember = await this.communeMemberService.createOne({
            commune: {
                connect: {
                    id: createCommuneMemberInput.communeId,
                },
            },
            actorType: createCommuneMemberInput.actorType,
            actorId: createCommuneMemberInput.actorId,
        });
        const result = Dto.CommuneMember.parse(communeMember);
        return result;
    }
    async updateCommuneMember(updateCommuneMemberInput) {
        const { id, ...data } = updateCommuneMemberInput;
        const communeMember = await this.communeMemberService.updateOne(id, data);
        const result = Dto.CommuneMember.parse(communeMember);
        return result;
    }
    async deleteCommuneMember(id) {
        await this.communeMemberService.softDeleteOne(id);
        return true;
    }
};
exports.CommuneResolver = CommuneResolver;
__decorate([
    (0, graphql_1.Query)("getCommune"),
    __param(0, (0, graphql_1.Args)("id", new zod_1.ZodPipe(zod_1.ZodHelper.Uuid))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CommuneResolver.prototype, "getCommune", null);
__decorate([
    (0, graphql_1.Query)("getCommunes"),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CommuneResolver.prototype, "getCommunes", null);
__decorate([
    (0, graphql_1.Mutation)("createCommune"),
    __param(0, (0, graphql_1.Args)("input", new zod_1.ZodPipe(Dto.CreateCommuneInput))),
    __param(1, (0, current_user_decorator_1.GqlCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], CommuneResolver.prototype, "createCommune", null);
__decorate([
    (0, graphql_1.Mutation)("updateCommune"),
    __param(0, (0, graphql_1.Args)("input", new zod_1.ZodPipe(Dto.UpdateCommuneInput))),
    __param(1, (0, current_user_decorator_1.GqlCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], CommuneResolver.prototype, "updateCommune", null);
__decorate([
    (0, graphql_1.Mutation)("deleteCommune"),
    __param(0, (0, graphql_1.Args)("id", new zod_1.ZodPipe(zod_1.ZodHelper.Uuid))),
    __param(1, (0, current_user_decorator_1.GqlCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CommuneResolver.prototype, "deleteCommune", null);
__decorate([
    (0, graphql_1.Query)("getCommuneMember"),
    __param(0, (0, graphql_1.Args)("id", new zod_1.ZodPipe(zod_1.ZodHelper.Uuid))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CommuneResolver.prototype, "getCommuneMember", null);
__decorate([
    (0, graphql_1.Query)("getCommuneMembers"),
    __param(0, (0, graphql_1.Args)("communeId", new zod_1.ZodPipe(zod_1.ZodHelper.Uuid))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CommuneResolver.prototype, "getCommuneMembers", null);
__decorate([
    (0, graphql_1.Mutation)("createCommuneMember"),
    __param(0, (0, graphql_1.Args)("createCommuneMemberInput", new zod_1.ZodPipe(Dto.CreateCommuneMemberInput))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CommuneResolver.prototype, "createCommuneMember", null);
__decorate([
    (0, graphql_1.Mutation)("updateCommuneMember"),
    __param(0, (0, graphql_1.Args)("updateCommuneMemberInput", new zod_1.ZodPipe(Dto.UpdateCommuneMemberInput))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CommuneResolver.prototype, "updateCommuneMember", null);
__decorate([
    (0, graphql_1.Mutation)("deleteCommuneMember"),
    __param(0, (0, graphql_1.Args)("id", new zod_1.ZodPipe(zod_1.ZodHelper.Uuid))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CommuneResolver.prototype, "deleteCommuneMember", null);
exports.CommuneResolver = CommuneResolver = __decorate([
    (0, graphql_1.Resolver)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.GqlJwtAuthGuard),
    __metadata("design:paramtypes", [commune_service_1.CommuneService,
        commune_member_service_1.CommuneMemberService])
], CommuneResolver);
//# sourceMappingURL=commune.resolver.js.map