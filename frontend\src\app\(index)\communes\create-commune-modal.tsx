import { useState, useRef, ChangeEvent } from "react";
import { <PERSON><PERSON>, Button, Form, Alert, Image } from "react-bootstrap";
import { Localization } from "@/utils/find-localization";
import { useHttpRequest } from "@/app/hooks/use-http-request";
import { LocalizedInput } from "@/components/localized-input";
import { LocalizedTextarea } from "@/components/localized-textarea";
import { useLocale } from "next-intl";
import { Locale } from "@/app/types";

interface CreateCommuneModalProps {
  show: boolean;
  onHide: () => void;
}

// Constants for file upload validation
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const MAX_FILES_COUNT = 10;
const ALLOWED_FILE_TYPES = ["image/jpeg", "image/png", "image/webp"];

const i18n = {
  en: {
    createNewCommune: "Create New Commune",
    communeCreatedSuccess: "Commune created successfully!",
    images: "Images (optional)",
    uploadInfo: "Upload up to 10 images (JPG, PNG, WebP), max 5MB each.",
    name: "Name",
    enterCommuneName: "Enter commune name",
    description: "Description (optional)",
    enterCommuneDescription: "Enter commune description",
    cancel: "Cancel",
    create: "Create",
    creating: "Creating...",
    provideName: "Please provide a name for the commune.",
    tooManyFiles: "You can upload a maximum of {count} images.",
    unsupportedFormat: "File '{name}' has an unsupported format. Only JPG, PNG, and WebP are allowed.",
    fileTooLarge: "File '{name}' exceeds the maximum size of 5MB.",
    failedToCreate: "Failed to create commune",
    unexpectedError: "An unexpected error occurred. Please try again.",
  },

  ru: {
    createNewCommune: "Создать новую коммуну",
    communeCreatedSuccess: "Коммуна успешно создана!",
    images: "Изображения (опционально)",
    uploadInfo: "Загрузите до 10 изображений (JPG, PNG, WebP), макс. 5МБ каждое.",
    name: "Название",
    enterCommuneName: "Введите название коммуны",
    description: "Описание (опционально)",
    enterCommuneDescription: "Введите описание коммуны",
    cancel: "Отмена",
    create: "Создать",
    creating: "Создание...",
    provideName: "Пожалуйста, укажите название коммуны.",
    tooManyFiles: "Вы можете загрузить максимум {count} изображений.",
    unsupportedFormat: "Файл '{name}' имеет неподдерживаемый формат. Разрешены только JPG, PNG и WebP.",
    fileTooLarge: "Файл '{name}' превышает максимальный размер 5МБ.",
    failedToCreate: "Не удалось создать коммуну",
    unexpectedError: "Произошла непредвиденная ошибка. Пожалуйста, попробуйте снова.",
  },
};

export function CreateCommuneModal({ show, onHide }: CreateCommuneModalProps) {
  const locale = useLocale() as Locale;
  const t = i18n[locale];

  const httpRequest = useHttpRequest();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [communeName, setCommuneName] = useState<Localization[]>([]);
  const [communeDescription, setCommuneDescription] = useState<Localization[]>([]);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [previewUrls, setPreviewUrls] = useState<string[]>([]);
  const [error, setError] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  const validateFiles = (files: File[]): boolean => {
    // Check if too many files
    if (files.length > MAX_FILES_COUNT) {
      setError(t.tooManyFiles.replace("{count}", MAX_FILES_COUNT.toString()));
      return false;
    }

    // Check file types and sizes
    for (const file of files) {
      if (!ALLOWED_FILE_TYPES.includes(file.type)) {
        setError(t.unsupportedFormat.replace("{name}", file.name));
        return false;
      }

      if (file.size > MAX_FILE_SIZE) {
        setError(t.fileTooLarge.replace("{name}", file.name));
        return false;
      }
    }

    return true;
  };

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    setError("");
    const files = Array.from(e.target.files || []);

    if (files.length === 0) return;

    if (!validateFiles(files)) {
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
      return;
    }

    // Create preview URLs
    const newPreviewUrls = files.map(file => URL.createObjectURL(file));

    // Store files and previews
    setSelectedFiles(files);
    setPreviewUrls(newPreviewUrls);
  };

  const removeFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));

    // Revoke the URL to prevent memory leaks
    URL.revokeObjectURL(previewUrls[index]);
    setPreviewUrls(prev => prev.filter((_, i) => i !== index));
  };

  const handleClose = () => {
    // Clean up preview URLs
    previewUrls.forEach(url => URL.revokeObjectURL(url));

    // Reset form values
    onHide();
    setCommuneName([]);
    setCommuneDescription([]);
    setSelectedFiles([]);
    setPreviewUrls([]);
    setError("");
    setSubmitSuccess(false);

    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleSubmit = async () => {
    setError("");
    setIsSubmitting(true);

    try {
      // Validate required fields
      if (!communeName.some(item => item.value.trim().length)) {
        setError(t.provideName);
        setIsSubmitting(false);
        return;
      }

      // Create FormData for file upload
      const formData = new FormData();

      formData.append("data", JSON.stringify({
        name: communeName,
        description: communeDescription,
      }));

      // Add files
      selectedFiles.forEach(file => {
        formData.append("images", file);
      });

      const response = await httpRequest("/api/commune", {
        method: "POST",
        body: formData,
        // Don't set Content-Type header, browser will set it with boundary for FormData
      });

      if (response.ok) {
        const result = await response.json();
        console.log("Commune created:", result);
        setSubmitSuccess(true);

        // Close modal after a short delay
        setTimeout(() => {
          handleClose();
          // Optionally refresh the communes list
          window.location.reload();
        }, 1500);
      } else {
        const errorData = await response.json().catch(() => ({ message: t.failedToCreate }));
        setError(errorData.message || t.failedToCreate);
      }
    } catch (err) {
      console.error("Error creating commune:", err);
      setError(t.unexpectedError);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal show={show} onHide={handleClose} centered>
      <Modal.Header closeButton>
        <Modal.Title>{t.createNewCommune}</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        {submitSuccess && (
          <Alert variant="success" className="mb-3">
            {t.communeCreatedSuccess}
          </Alert>
        )}

        {error && (
          <Alert variant="danger" className="mb-3">
            {error}
          </Alert>
        )}

        <Form onSubmit={(e) => { e.preventDefault(); handleSubmit(); }}>
          <Form.Group className="mb-3" controlId="communeImages">
            <Form.Label>{t.images}</Form.Label>
            <Form.Control
              type="file"
              multiple
              accept=".jpg,.jpeg,.png,.webp"
              onChange={handleFileChange}
              ref={fileInputRef}
            />
            <Form.Text className="text-muted">
              {t.uploadInfo}
            </Form.Text>

            {previewUrls.length > 0 && (
              <div className="mt-3 d-flex flex-wrap gap-2">
                {previewUrls.map((url, index) => (
                  <div key={index} className="position-relative" style={{ width: "100px", height: "100px" }}>
                    <Image
                      src={url}
                      alt={`Preview ${index + 1}`}
                      thumbnail
                      style={{ width: "100%", height: "100%", objectFit: "cover" }}
                    />
                    <Button
                      variant="danger"
                      size="sm"
                      className="position-absolute top-0 end-0"
                      onClick={() => removeFile(index)}
                      style={{ fontSize: "0.7rem", padding: "0.1rem 0.3rem" }}
                    >
                      ×
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </Form.Group>

          <LocalizedInput
            id="communeName"
            label={t.name}
            placeholder={t.enterCommuneName}
            required={true}
            value={communeName}
            onChange={setCommuneName}
          />

          <LocalizedTextarea
            id="communeDescription"
            label={t.description}
            placeholder={t.enterCommuneDescription}
            rows={4}
            value={communeDescription}
            onChange={setCommuneDescription}
          />
        </Form>
      </Modal.Body>
      <Modal.Footer>
        <Button
          variant="secondary"
          onClick={handleClose}
          disabled={isSubmitting}
        >
          {t.cancel}
        </Button>
        <Button
          variant="primary"
          disabled={!communeName.some(item => item.value.trim().length) || isSubmitting}
          onClick={handleSubmit}
        >
          {isSubmitting ? t.creating : t.create}
        </Button>
      </Modal.Footer>
    </Modal>
  );
}
