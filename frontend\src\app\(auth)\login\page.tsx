"use client";

import { useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import superagent from "superagent";

const enum OtpStatus {
  None = "none",
  Pending = "pending",
  Sent = "sent",
  SendingDisabledByServer = "sending-disabled-by-server",
  Error = "error",
}

const enum SubmitStatus {
  None = "none",
  Pending = "pending",
  Error = "error",
}

export default function LoginPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const isDevMode = Boolean(process.env.NEXT_PUBLIC_IS_DEV_MODE);

  const [activeTab, setActiveTab] = useState<"login" | "register">("login");
  const [email, setEmail] = useState(isDevMode ? "<EMAIL>" : "");
  const [otp, setOtp] = useState(isDevMode ? "123456" : "");
  const [otpStatus, setOtpStatus] = useState<OtpStatus>(OtpStatus.None);
  const [submitStatus, setSubmitStatus] = useState<SubmitStatus>(SubmitStatus.None);
  const [submitErrorMessage, setSubmitErrorMessage] = useState<string | null>(null);

  const isSubmitting = otpStatus === OtpStatus.Pending || submitStatus === SubmitStatus.Pending;

  const handleSendOtp = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email) return;

    setOtpStatus(OtpStatus.Pending);

    try {
      const { isSent } = await superagent
        .post("/api/auth/otp")
        .send({ email })
        .then<{ isSent: boolean }>(r => r.body);

      setOtpStatus(isSent ? OtpStatus.Sent : OtpStatus.SendingDisabledByServer);
    }
    catch (error) {
      console.error("Failed to send OTP:", error);

      setOtpStatus(OtpStatus.Error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email || !otp) return;

    setSubmitStatus(SubmitStatus.Pending);

    console.log("submitting");

    try {
      if (activeTab === "login") {
        await superagent
          .post("/api/auth/login")
          .send({ email, otp });

        router.push(searchParams.get("redirectFrom") ?? "/");
      }
      else {
        await superagent
          .post("/api/auth/register")
          .send({ email, otp });

        router.push(searchParams.get("redirectFrom") ?? "/");
      }
    }
    catch (error) {
      console.error(error);

      // Handle 401 response or other errors
      const errorMessage = (error as any).response?.body?.error || (error as Error).message;

      setSubmitStatus(SubmitStatus.Error);
      setSubmitErrorMessage(errorMessage);

      return; // Return early to prevent further execution
    }
  };

  return (
    <div className="container min-vh-100 d-flex align-items-center justify-content-center">
      <div className="card shadow-lg border-0" style={{ maxWidth: "400px", width: "100%" }}>
        <div className="card-header bg-white border-0 pt-4 pb-0">
          <div className="position-relative">
            <ul className="nav nav-tabs border-0 card-header-tabs">
              <li className="nav-item flex-grow-1 text-center">
                <button
                  className={`nav-link border-0 w-100 ${activeTab === "login" ? "active" : ""}`}
                  onClick={() => setActiveTab("login")}
                >
                  Login
                </button>
              </li>
              <li className="nav-item flex-grow-1 text-center">
                <button
                  className={`nav-link border-0 w-100 ${activeTab === "register" ? "active" : ""}`}
                  onClick={() => setActiveTab("register")}
                >
                  Register
                </button>
              </li>
            </ul>
            <div
              className="position-absolute bottom-0 bg-primary"
              style={{
                height: "3px",
                width: "50%",
                left: activeTab === "login" ? "0" : "50%",
                transition: "left 0.3s ease-in-out",
                borderRadius: "3px 3px 0 0",
              }}
            ></div>
          </div>
        </div>
        <div className="card-body p-4">
          <h5
            className="card-title mb-4"
            style={{
              transition: "opacity 0.3s ease-in-out",
              animation: "fadeIn 0.5s",
            }}
          >
            {activeTab === "login" ? "Welcome Back!" : "Create an Account"}
          </h5>
          <form onSubmit={handleSubmit}>
            <div className="mb-3">
              <label htmlFor="email" className="form-label">Email address</label>
              <input
                type="email"
                autoComplete="email"
                className="form-control"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
                required
              />
            </div>
            <div className="mb-3">
              <button
                type="button"
                className="btn btn-outline-primary w-100"
                onClick={handleSendOtp}
                disabled={!email || isSubmitting}
              >
                {otpStatus === OtpStatus.Pending ? (
                  <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                ) : null}
                Send OTP
              </button>
            </div>
            <div className="mb-3">
              <div className="d-flex justify-content-between align-items-center mb-2">
                <label htmlFor="otp" className="form-label mb-0">OTP Verification Code</label>

                {otpStatus === OtpStatus.Sent && (
                  <span className="badge bg-success-subtle text-success px-2 py-1 rounded-pill">
                    OTP Sent
                  </span>
                )}

                {otpStatus === OtpStatus.SendingDisabledByServer && (
                  <span className="badge bg-warning-subtle text-warning px-2 py-1 rounded-pill">
                    OTP sending is disabled by the server
                  </span>
                )}
              </div>
              <input
                type="text"
                className="form-control"
                id="otp"
                value={otp}
                onChange={(e) => {
                  // Only allow numbers and limit to 6 digits
                  const value = e.target.value.replace(/\D/g, "");
                  if (value.length <= 6) setOtp(value);
                }}
                placeholder="6-digit code"
                pattern="[0-9]{6}"
                maxLength={6}
                aria-describedby="otpHelp"
              />
              <div id="otpHelp" className="form-text">
                Enter the 6-digit code sent to your email.
              </div>
            </div>
            <div className="d-grid gap-2">
              <button
                type="submit"
                className="btn btn-primary"
                disabled={!email || !otp || isSubmitting}
              >
                {
                  submitStatus === SubmitStatus.Pending && otp
                    ? (<span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>)
                    : null
                }
                { activeTab === "login" && "Log In" }
                { activeTab === "register" && "Create Account" }
              </button>

              {submitStatus === SubmitStatus.Error && (
                <span className="text-danger">
                  Failed to submit. Please try again.
                  <br />
                  {submitErrorMessage}
                </span>
              )}
            </div>
          </form>
        </div>
      </div>

      <style jsx>{`
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(10px); }
          to { opacity: 1; transform: translateY(0); }
        }

        .nav-link {
          padding: 0.75rem 1.25rem;
          font-weight: 500;
          color: #6c757d;
          transition: all 0.3s ease;
        }

        .nav-link.active {
          color: #0d6efd;
          font-weight: 600;
          background-color: transparent;
        }

        .nav-link:hover:not(.active) {
          color: #0d6efd;
          background-color: rgba(13, 110, 253, 0.05);
        }
      `}</style>
    </div>
  );
}
