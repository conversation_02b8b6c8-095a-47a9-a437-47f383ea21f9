"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isPrismaUniqueConstraintError = isPrismaUniqueConstraintError;
const library_1 = require("@prisma/client/runtime/library");
function isPrismaUniqueConstraintError(error) {
    return (error instanceof library_1.PrismaClientKnownRequestError &&
        error.code === "P2002" &&
        error.message.toLowerCase().includes("unique constraint"));
}
//# sourceMappingURL=is-prisma-unique-constraint-error.js.map