"use client";

import { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import { useParams } from "next/navigation";
import { useHttpRequest } from "@/app/hooks/use-http-request";
import { findLocalizationForUserLocales, Localization } from "@/utils/find-localization";
import { DetailImageCarousel } from "./detail-image-carousel";
import { MemberCard } from "./member-card";
import { AddMemberModal } from "./add-member-modal";
import { EditCommuneModal } from "./edit-commune-modal";
import { useLocale } from "next-intl";
import { Locale } from "@/app/types";
import { formatDate } from "@/utils/format-date";

type CommuneMemberType = "user" | "commune";

interface CommuneImage {
  id: string;
  url: string;
  source: string;
}

interface CommuneMember {
  id: string;
  communeId: string;
  actorType: CommuneMemberType;
  actorId: string;
  isHead: boolean;
  joinedAt: string;
  leftAt: string | null;
  name?: Localization[];
  images?: CommuneImage[];
}

interface CurrentUser {
  id: string;
  email: string;
  role: "user" | "admin";
  name: Localization[];
  description: Localization[];
  joinedAt: string;
}

interface Commune {
  id: string;
  name: Localization[];
  description: Localization[];
  memberCount: number;
  headMember: {
    actorType: CommuneMemberType;
    actorId: string;
    name: Localization[];
  };
  images?: CommuneImage[];
  createdAt: string;
  updatedAt: string;
}

const i18n = {
  en: {
    loading: "Loading commune details...",
    communeNotFound: "Commune not found",
    noDescription: "No description available",
    communeDetails: "Commune Details",
    edit: "Edit",
    members: "Members",
    member: "member",
    members_plural: "members",
    headMember: "Head Member",
    created: "Created",
    addMember: "Add Member",
    noMembers: "No members found",
    errorFetchingCommune: "Failed to fetch commune",
    errorFetchingMembers: "Failed to fetch members",
    errorOccurred: "An error occurred while fetching data",
    dateFormatLocale: "en-US",
  },

  ru: {
    loading: "Загрузка данных коммуны...",
    communeNotFound: "Коммуна не найдена",
    noDescription: "Нет описания",
    communeDetails: "Информация о коммуне",
    edit: "Редактировать",
    members: "Участники",
    member: "участник",
    members_plural: "участников",
    headMember: "Глава",
    created: "Создана",
    addMember: "Добавить участника",
    noMembers: "Участники не найдены",
    errorFetchingCommune: "Не удалось загрузить коммуну",
    errorFetchingMembers: "Не удалось загрузить участников",
    errorOccurred: "Произошла ошибка при загрузке данных",
    dateFormatLocale: "ru-RU",
  },
};

export default function CommuneDetailPage() {
  const locale = useLocale() as Locale;
  const t = i18n[locale];

  const params = useParams();
  const communeId = params.id as string;

  const [commune, setCommune] = useState<Commune | null>(null);
  const [members, setMembers] = useState<CommuneMember[]>([]);
  const [currentUser, setCurrentUser] = useState<CurrentUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddMemberModal, setShowAddMemberModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);

  const httpRequest = useHttpRequest();

  // Function to refresh member data
  const refreshMembers = async () => {
    try {
      const membersResponse = await httpRequest(`/api/commune/${communeId}/member`);

      if (!membersResponse.ok) {
        throw new Error(`${t.errorFetchingMembers}: ${membersResponse.statusText}`);
      }

      const membersData: CommuneMember[] = await membersResponse.json();

      // Sort members by joinedAt date
      const sortedMembers = membersData
        .filter(member => !member.leftAt) // Only show active members
        .sort((a, b) => a.joinedAt.localeCompare(b.joinedAt));

      setMembers(sortedMembers);
    } catch (err) {
      console.error("Error refreshing members:", err);
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch current user data
        const userResponse = await httpRequest("/api/auth/me");
        if (userResponse.ok) {
          const userData = await userResponse.json();
          setCurrentUser(userData);
        }

        // Fetch commune details
        const communeResponse = await httpRequest(`/api/commune/${communeId}`);

        if (!communeResponse.ok) {
          throw new Error(`${t.errorFetchingCommune}: ${communeResponse.statusText}`);
        }

        const communeData: Commune = await communeResponse.json();
        setCommune(communeData);

        // Fetch commune members
        await refreshMembers();
      } catch (err) {
        setError(err instanceof Error ? err.message : t.errorOccurred);
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    if (communeId) {
      fetchData();
    }
  }, [communeId, httpRequest]);

  if (loading) {
    return (
      <Container className="py-5 text-center">
        <Spinner animation="border" role="status" className="me-2" />
        <span>{t.loading}</span>
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="py-5">
        <Alert variant="danger">
          {error}
        </Alert>
      </Container>
    );
  }

  if (!commune) {
    return (
      <Container className="py-5">
        <Alert variant="warning">
          {t.communeNotFound}
        </Alert>
      </Container>
    );
  }

  const communeName = findLocalizationForUserLocales(commune.name);
  const communeDescription = findLocalizationForUserLocales(commune.description);

  // Find the head member
  const headMember = members.find(member =>
    member.actorType === commune.headMember.actorType &&
    member.actorId === commune.headMember.actorId,
  );

  // Check if current user is the head member
  const isCurrentUserHead = !!(currentUser && headMember &&
    headMember.actorType === "user" &&
    headMember.actorId === currentUser.id);

  const handleAddMemberClick = () => {
    setShowAddMemberModal(true);
  };

  const handleAddMemberModalClose = () => {
    setShowAddMemberModal(false);
  };

  const handleMemberAdded = () => {
    refreshMembers();
  };

  const handleMemberRemoved = () => {
    refreshMembers();
  };

  const handleEditClick = () => {
    setShowEditModal(true);
  };

  const handleEditModalClose = () => {
    setShowEditModal(false);
  };

  const handleCommuneUpdated = async () => {
    try {
      // Fetch updated commune details
      const communeResponse = await httpRequest(`/api/commune/${communeId}`);

      if (!communeResponse.ok) {
        throw new Error(`Failed to fetch updated commune: ${communeResponse.statusText}`);
      }

      const communeData: Commune = await communeResponse.json();
      setCommune(communeData);
    } catch (err) {
      console.error("Error refreshing commune data:", err);
    }
  };

  return (
    <Container className="py-4">
      <Row>
        <Col lg={8}>
          {/* Image Carousel */}
          <DetailImageCarousel images={commune.images || []} />

          {/* Commune Information */}
          <div className="mb-4">
            <div className="d-flex justify-content-between align-items-center mb-3">
              <h2 className="mb-0">{communeName}</h2>
            </div>
            <p className="lead text-muted">{communeDescription || t.noDescription}</p>
          </div>
        </Col>

        <Col lg={4}>
          <Card className="shadow-sm mb-4">
            <Card.Body>
              <div className="d-flex justify-content-between align-items-center mb-2">
                <Card.Title className="mb-0">{t.communeDetails}</Card.Title>
                {isCurrentUserHead && (
                  <Button variant="outline-primary" size="sm" onClick={handleEditClick}>
                    {t.edit}
                  </Button>
                )}
              </div>
              <hr />
              <div className="d-flex justify-content-between mb-2">
                <span>{t.members}:</span>
                <Badge bg="primary">{commune.memberCount}</Badge>
              </div>
              <div className="d-flex justify-content-between mb-2">
                <span>{t.headMember}:</span>
                <span className="text-muted">
                  {findLocalizationForUserLocales(commune.headMember.name)}
                </span>
              </div>
              <div className="d-flex justify-content-between">
                <span>{t.created}:</span>
                <span className="text-muted">
                  {formatDate(new Date(commune.createdAt), t.dateFormatLocale)}
                </span>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Members Section */}
      <div className="d-flex justify-content-between align-items-center mt-5 mb-4">
        <h3 className="mb-0">{t.members} ({ members.length })</h3>
        {isCurrentUserHead && (
          <Button variant="primary" onClick={handleAddMemberClick}>
            {t.addMember}
          </Button>
        )}
      </div>

      {members.length === 0 ? (
        <Alert variant="info">{t.noMembers}</Alert>
      ) : (
        <Row xs={1} sm={2} md={3} lg={4} className="g-4">
          {members.map((member) => (
            <Col key={member.id}>
              <MemberCard
                id={member.id}
                actorType={member.actorType}
                actorId={member.actorId}
                name={member.name ?? []}
                isHead={member.actorType === commune.headMember.actorType && member.actorId === commune.headMember.actorId}
                joinedAt={member.joinedAt}
                communeId={communeId}
                isCurrentUserHead={isCurrentUserHead}
                onMemberRemoved={handleMemberRemoved}
                images={member.images || []}
              />
            </Col>
          ))}
        </Row>
      )}

      {/* Add Member Modal */}
      <AddMemberModal
        show={showAddMemberModal}
        onHide={handleAddMemberModalClose}
        communeId={communeId}
        onMemberAdded={handleMemberAdded}
      />

      {/* Edit Commune Modal */}
      <EditCommuneModal
        show={showEditModal}
        onHide={handleEditModalClose}
        communeData={commune}
        onCommuneUpdated={handleCommuneUpdated}
      />
    </Container>
  );
}
