import { useState, useRef, ChangeEvent } from "react";
import { <PERSON>dal, Button, Form, Alert, Image, Row, Col } from "react-bootstrap";
import { Localization } from "@/utils/find-localization";
import { useHttpRequest } from "@/app/hooks/use-http-request";
import { LocalizedInput } from "@/components/localized-input";
import { LocalizedTextarea } from "@/components/localized-textarea";
import { useLocale } from "next-intl";
import { Locale } from "@/app/types";

interface CreatePostModalProps {
  show: boolean;
  onHide: () => void;
  onPostCreated?: () => void;
}

// Constants for file upload validation
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const MAX_FILES_COUNT = 10;
const ALLOWED_FILE_TYPES = ["image/jpeg", "image/png", "image/webp"];

const i18n = {
  en: {
    createNewPost: "Create New Post",
    postCreatedSuccess: "Post created successfully!",
    provideTitle: "Please provide a title for the post.",
    title: "Title",
    enterTitle: "Enter post title",
    description: "Description",
    enterDescription: "Enter post description",
    status: "Status",
    draft: "Draft",
    published: "Published",
    archived: "Archived",
    publishDate: "Publish Date",
    leaveEmpty: "Leave empty to publish immediately",
    images: "Images (optional)",
    uploadInfo: "Upload up to 10 images (JPG, PNG, WebP), max 5MB each.",
    selectedImages: "Selected Images:",
    cancel: "Cancel",
    create: "Create",
    creating: "Creating...",
    maxImagesError: "You can upload a maximum of 10 images.",
    fileSizeError: "File \"{0}\" exceeds the maximum size of 5MB.",
    fileTypeError: "File \"{0}\" is not a supported image type.",
    failedToCreate: "Failed to create post: {0}",
    errorCreatingPost: "An error occurred while creating the post",
  },

  ru: {
    createNewPost: "Создать новую публикацию",
    postCreatedSuccess: "Публикация успешно создана!",
    provideTitle: "Пожалуйста, укажите заголовок публикации.",
    title: "Заголовок",
    enterTitle: "Введите заголовок публикации",
    description: "Описание",
    enterDescription: "Введите описание публикации",
    status: "Статус",
    draft: "Черновик",
    published: "Опубликовано",
    archived: "Архивировано",
    publishDate: "Дата публикации",
    leaveEmpty: "Оставьте пустым для немедленной публикации",
    images: "Изображения (опционально)",
    uploadInfo: "Загрузите до 10 изображений (JPG, PNG, WebP), макс. 5МБ каждое.",
    selectedImages: "Выбранные изображения:",
    cancel: "Отмена",
    create: "Создать",
    creating: "Создание...",
    maxImagesError: "Вы можете загрузить максимум 10 изображений.",
    fileSizeError: "Файл \"{0}\" превышает максимальный размер 5МБ.",
    fileTypeError: "Файл \"{0}\" не является поддерживаемым типом изображения.",
    failedToCreate: "Не удалось создать публикацию: {0}",
    errorCreatingPost: "Произошла ошибка при создании публикации",
  },
};

export function CreatePostModal({ show, onHide, onPostCreated }: CreatePostModalProps) {
  const locale = useLocale() as Locale;
  const t = i18n[locale];

  const httpRequest = useHttpRequest();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [postTitle, setPostTitle] = useState<Localization[]>([]);
  const [postDescription, setPostDescription] = useState<Localization[]>([]);
  const [postStatus, setPostStatus] = useState<string>("draft");
  const [publishedAt, setPublishedAt] = useState<string>("");
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [previewUrls, setPreviewUrls] = useState<string[]>([]);
  const [error, setError] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  const handleClose = () => {
    // Reset form state
    setPostTitle([]);
    setPostDescription([]);
    setPostStatus("draft");
    setPublishedAt("");
    setSelectedFiles([]);
    setPreviewUrls([]);
    setError("");
    setIsSubmitting(false);
    setSubmitSuccess(false);

    // Close the modal
    onHide();
  };

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files) return;

    // Validate file count
    if (files.length > MAX_FILES_COUNT) {
      setError(t.maxImagesError);
      return;
    }

    const newSelectedFiles: File[] = [];
    const newPreviewUrls: string[] = [];
    let hasError = false;

    // Validate each file
    Array.from(files).forEach(file => {
      // Check file size
      if (file.size > MAX_FILE_SIZE) {
        setError(t.fileSizeError.replace("{0}", file.name));
        hasError = true;
        return;
      }

      // Check file type
      if (!ALLOWED_FILE_TYPES.includes(file.type)) {
        setError(t.fileTypeError.replace("{0}", file.name));
        hasError = true;
        return;
      }

      newSelectedFiles.push(file);
      newPreviewUrls.push(URL.createObjectURL(file));
    });

    if (hasError) return;

    setSelectedFiles(newSelectedFiles);
    setPreviewUrls(newPreviewUrls);
    setError("");
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    setError("");

    try {
      // Validate required fields
      if (!postTitle.some(item => item.value.trim().length)) {
        setError(t.provideTitle);
        setIsSubmitting(false);
        return;
      }

      // Create FormData for file upload
      const formData = new FormData();

      // Prepare post data
      const postData: any = {
        title: postTitle,
        description: postDescription,
        status: postStatus,
      };

      // Add publishedAt if provided and status is published
      if (publishedAt && postStatus === "published") {
        postData.publishedAt = new Date(publishedAt).toISOString();
      }
      else {
        postData.publishedAt = null;
      }

      formData.append("data", JSON.stringify(postData));

      // Add files
      selectedFiles.forEach(file => {
        console.log(file.name, file.size);
        formData.append("images", file);
      });

      const response = await httpRequest("/api/post", {
        method: "POST",
        body: formData,
        // Don't set Content-Type header, browser will set it with boundary for FormData
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || t.failedToCreate.replace("{0}", response.statusText));
      }

      setSubmitSuccess(true);

      // Notify parent component that a post was created
      if (onPostCreated) {
        onPostCreated();
      }

      setTimeout(() => {
        handleClose();
      }, 1500);
    } catch (err) {
      console.error("Error creating post:", err);
      setError(err instanceof Error ? err.message : t.errorCreatingPost);
    } finally {
      setIsSubmitting(false);
    }
  };

  const removePreview = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
    setPreviewUrls(prev => prev.filter((_, i) => i !== index));
  };

  return (
    <Modal show={show} onHide={handleClose} centered size="lg">
      <Modal.Header closeButton>
        <Modal.Title>{t.createNewPost}</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        {submitSuccess && (
          <Alert variant="success" className="mb-3">
            {t.postCreatedSuccess}
          </Alert>
        )}

        {error && (
          <Alert variant="danger" className="mb-3">
            {error}
          </Alert>
        )}

        <Form onSubmit={(e) => { e.preventDefault(); handleSubmit(); }}>
          <LocalizedInput
            id="postTitle"
            label={t.title}
            placeholder={t.enterTitle}
            required={true}
            value={postTitle}
            onChange={setPostTitle}
          />

          <LocalizedTextarea
            id="postDescription"
            label={t.description}
            placeholder={t.enterDescription}
            rows={4}
            value={postDescription}
            onChange={setPostDescription}
          />

          <Row className="mb-3">
            <Col md={6}>
              <Form.Group controlId="postStatus">
                <Form.Label>{t.status}</Form.Label>
                <Form.Select
                  value={postStatus}
                  onChange={(e) => setPostStatus(e.target.value)}
                >
                  <option value="draft">{t.draft}</option>
                  <option value="published">{t.published}</option>
                  <option value="archived">{t.archived}</option>
                </Form.Select>
              </Form.Group>
            </Col>

            {postStatus === "published" && (
              <Col md={6}>
                <Form.Group controlId="publishedAt">
                  <Form.Label>{t.publishDate}</Form.Label>
                  <Form.Control
                    type="datetime-local"
                    value={publishedAt}
                    onChange={(e) => setPublishedAt(e.target.value)}
                  />
                  <Form.Text className="text-muted">
                    {t.leaveEmpty}
                  </Form.Text>
                </Form.Group>
              </Col>
            )}
          </Row>

          <Form.Group className="mb-3" controlId="postImages">
            <Form.Label>{t.images}</Form.Label>
            <Form.Control
              type="file"
              multiple
              accept=".jpg,.jpeg,.png,.webp"
              onChange={handleFileChange}
              ref={fileInputRef}
            />
            <Form.Text className="text-muted">
              {t.uploadInfo}
            </Form.Text>
          </Form.Group>

          {/* Image Previews */}
          {previewUrls.length > 0 && (
            <div className="mb-3">
              <p className="mb-2">{t.selectedImages}</p>
              <div className="d-flex flex-wrap gap-2">
                {previewUrls.map((url, index) => (
                  <div key={index} className="position-relative" style={{ width: "100px" }}>
                    <Image
                      src={url}
                      alt={`Preview ${index + 1}`}
                      style={{ width: "100px", height: "75px", objectFit: "cover" }}
                      thumbnail
                    />
                    <Button
                      variant="danger"
                      size="sm"
                      className="position-absolute top-0 end-0"
                      style={{ fontSize: "0.7rem", padding: "0.1rem 0.3rem" }}
                      onClick={() => removePreview(index)}
                    >
                      ×
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </Form>
      </Modal.Body>
      <Modal.Footer>
        <Button
          variant="secondary"
          onClick={handleClose}
          disabled={isSubmitting}
        >
          {t.cancel}
        </Button>
        <Button
          variant="primary"
          disabled={!postTitle.some(item => item.value.trim().length) || isSubmitting}
          onClick={handleSubmit}
        >
          {isSubmitting ? t.creating : t.create}
        </Button>
      </Modal.Footer>
    </Modal>
  );
}
