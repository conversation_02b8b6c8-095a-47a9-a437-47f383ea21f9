name: 'commune'

services:
  # nodebb:
  #   image: ghcr.io/nodebb/nodebb:latest
  #   container_name: nodebb
  #   restart: unless-stopped
  #   depends_on:
  #     - postgres
  #   ports:
  #     - "4567:4567"
  #   volumes:
  #     # - ./nodebb/build:/usr/src/app/build
  #     - ./nodebb/uploads:/usr/src/app/public/uploads
  #     - ./nodebb/config:/opt/config
  #   # environment:
  #   #   # PostgreSQL configuration
  #   #   database: postgres
  #   #   DB_HOST: postgres
  #   #   DB_PORT: 5432
  #   #   DB_USER: nodebb
  #   #   DB_PASSWORD: nodebb
  #   #   DB_NAME: nodebb
  #   networks:
  #     - commune

  # postgres:
  #   image: postgres:17
  #   container_name: postgres
  #   restart: unless-stopped
  #   environment:
  #     POSTGRES_USER: nodebb
  #     POSTGRES_PASSWORD: nodebb
  #     POSTGRES_DB: nodebb
  #   volumes:
  #     - ./postgres/data:/var/lib/postgresql/data
  #     # - postgres-data:/usr/commune/postgres-data/data
  #   networks:
  #     - commune
  #   # healthcheck:
  #   #   test: ["CMD", "pg_isready", "-U", "nodebb"]
  #   #   interval: 10s
  #   #   timeout: 5s
  #   #   retries: 5

  # posteio:
  #   image: analogic/poste.io
  #   container_name: posteio
  #   restart: unless-stopped
  #   network_mode: host
  #   environment:
  #     TZ: Europe/Moscow
  #     HTTPS: "OFF"
  #     HTTP_PORT: 3010
  #     HTTPS_PORT: 3011
  #   volumes:
  #     - /posteio/data:/data
  #   hostname: mail.dev.commune.my

  minio:
    image: minio/minio:latest
    container_name: minio
    restart: unless-stopped
    ports:
      - "9000:9000"  # API port
      - "9001:9001"  # Console port
    environment:
      MINIO_ROOT_USER: admin
      MINIO_ROOT_PASSWORD: password
    volumes:
      - ./minio/data:/data
    command: server /data --console-address ":9001"
    networks:
      - commune
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # frontend:
  #   image: commune-frontend:0.2.1
  #   container_name: frontend
  #   restart: unless-stopped
  #   ports:
  #     - "3000:3000"
  #   environment:
  #   networks:
  #     - commune

networks:
  commune:
    driver: bridge
