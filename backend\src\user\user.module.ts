import { Module } from "@nestjs/common";
import { UserService } from "./user.service";
import { UserController } from "./http/user.controller";
import { UserRefreshTokenService } from "./user-refresh-token.service";
import { UserTitleService } from "./user-title.service";
import { MinioModule } from "src/minio/minio.module";

@Module({
    imports: [MinioModule],
    controllers: [UserController],
    providers: [UserService, UserRefreshTokenService, UserTitleService],
    exports: [UserService, UserRefreshTokenService, UserTitleService],
})
export class UserModule {}
