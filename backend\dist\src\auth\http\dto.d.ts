import { z, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "src/zod";
export type Otp = Zod<PERSON>el<PERSON>.Infer<typeof Otp>;
export declare const Otp: z.ZodObject<{
    email: z.ZodString;
}, "strip", z.ZodTypeAny, {
    email: string;
}, {
    email: string;
}>;
export type Me = ZodHelper.Infer<typeof Me>;
export declare const Me: z.ZodObject<{
    id: z.ZodString;
    email: z.ZodString;
    role: z.ZodN<PERSON>num<{
        admin: "admin";
        moderator: "moderator";
        user: "user";
    }>;
    name: z.<PERSON><PERSON>y<z.ZodObject<{
        locale: z.Zod<PERSON>num<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodType<PERSON>ny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    description: z.<PERSON>od<PERSON>y<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.<PERSON>ype<PERSON>ny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    images: z.ZodOptional<z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        url: z.ZodString;
        createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
        updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        url: string;
    }, {
        id: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
        url: string;
    }>, "many">>;
    joinedAt: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    id: string;
    email: string;
    role: "user" | "admin" | "moderator";
    name: {
        locale: "en" | "ru";
        value: string;
    }[];
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    joinedAt: Date;
    images?: {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        url: string;
    }[] | undefined;
}, {
    id: string;
    email: string;
    role: "user" | "admin" | "moderator";
    name: {
        locale: "en" | "ru";
        value: string;
    }[];
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    joinedAt: Date;
    images?: {
        id: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
        url: string;
    }[] | undefined;
}>;
export type Register = ZodHelper.Infer<typeof Register>;
export declare const Register: z.ZodObject<{
    referrerId: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    email: z.ZodString;
    otp: z.ZodString;
}, "strip", z.ZodTypeAny, {
    email: string;
    otp: string;
    referrerId?: string | null | undefined;
}, {
    email: string;
    otp: string;
    referrerId?: string | null | undefined;
}>;
export type Login = ZodHelper.Infer<typeof Login>;
export declare const Login: z.ZodObject<{
    email: z.ZodString;
    otp: z.ZodString;
}, "strip", z.ZodTypeAny, {
    email: string;
    otp: string;
}, {
    email: string;
    otp: string;
}>;
export type Logout = ZodHelper.Infer<typeof Logout>;
export declare const Logout: z.ZodObject<{
    refreshToken: z.ZodString;
}, "strip", z.ZodTypeAny, {
    refreshToken: string;
}, {
    refreshToken: string;
}>;
