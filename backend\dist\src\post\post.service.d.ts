import { Prisma, PostStatus } from "@prisma/client";
import { BaseService } from "src/common/base-service";
import { PrismaService } from "src/prisma/prisma.service";
import { MinioService, FileInfo } from "src/minio/minio.service";
import { CurrentUser } from "src/auth/types";
import { ZodHelper } from "src/zod";
export declare class PostService extends BaseService {
    private readonly prisma;
    private readonly minioService;
    constructor(prisma: PrismaService, minioService: MinioService);
    canGet(id: string, user: CurrentUser): Promise<true>;
    check(ids: string[]): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        status: import(".prisma/client").$Enums.PostStatus;
        publishedAt: Date | null;
    }[]>;
    getOne(id: string): Promise<({
        images: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        }[];
        description: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
        title: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        status: import(".prisma/client").$Enums.PostStatus;
        publishedAt: Date | null;
    }) | null>;
    getOneOrThrow(id: string): Promise<{
        images: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        }[];
        description: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
        title: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        status: import(".prisma/client").$Enums.PostStatus;
        publishedAt: Date | null;
    }>;
    getOneWithRole(id: string, user?: CurrentUser): Promise<({
        images: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        }[];
        description: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
        title: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        status: import(".prisma/client").$Enums.PostStatus;
        publishedAt: Date | null;
    }) | null>;
    getMany(where: Prisma.PostWhereInput, pagination?: {
        page: number;
        size: number;
    }, user?: CurrentUser): Promise<({
        images: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        }[];
        description: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
        title: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        status: import(".prisma/client").$Enums.PostStatus;
        publishedAt: Date | null;
    })[]>;
    create(data: {
        title: ZodHelper.Localization[];
        description: ZodHelper.Localization[];
        status: PostStatus;
        publishedAt: Date | null;
    }, user: CurrentUser): Promise<{
        images: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        }[];
        description: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
        title: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        status: import(".prisma/client").$Enums.PostStatus;
        publishedAt: Date | null;
    }>;
    update(id: string, data: Prisma.PostUpdateInput, user: CurrentUser): Promise<{
        images: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        }[];
        description: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
        title: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        status: import(".prisma/client").$Enums.PostStatus;
        publishedAt: Date | null;
    }>;
    updateOne(id: string, data: Prisma.PostUpdateInput): Promise<{
        images: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        }[];
        description: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
        title: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        status: import(".prisma/client").$Enums.PostStatus;
        publishedAt: Date | null;
    }>;
    uploadPostImages(postId: string, files: FileInfo[]): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        url: string;
    }[]>;
    softDeleteOne(id: string): Promise<{
        images: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        }[];
        description: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
        title: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        status: import(".prisma/client").$Enums.PostStatus;
        publishedAt: Date | null;
    }>;
}
