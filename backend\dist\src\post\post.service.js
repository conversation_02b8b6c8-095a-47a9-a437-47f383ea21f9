"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PostService = void 0;
const common_1 = require("@nestjs/common");
const base_service_1 = require("../common/base-service");
const prisma_service_1 = require("../prisma/prisma.service");
const minio_service_1 = require("../minio/minio.service");
const utils_1 = require("../utils");
const errors_1 = require("../common/errors");
let PostService = class PostService extends base_service_1.BaseService {
    constructor(prisma, minioService) {
        super("post");
        this.prisma = prisma;
        this.minioService = minioService;
    }
    async canGet(id, user) {
        await this.getOneOrThrow(id);
        return true;
    }
    async check(ids) {
        return await this._check(ids, this.prisma.post, this.entityType);
    }
    async getOne(id) {
        const post = await this.prisma.post.findUnique({
            where: { id, deletedAt: null },
            include: {
                title: true,
                description: true,
                images: true,
            },
        });
        return post;
    }
    async getOneOrThrow(id) {
        const post = await this.getOne(id);
        if (!post) {
            throw this.createNotFoundException();
        }
        return post;
    }
    async getOneWithRole(id, user) {
        const post = await this.prisma.post.findUnique({
            where: { id, deletedAt: null },
            include: {
                title: true,
                description: true,
                images: true,
            },
        });
        if (!post || (user && user.role === "admin")) {
            return post;
        }
        if (post.status !== "published") {
            return null;
        }
        return post;
    }
    async getMany(where, pagination, user) {
        const finalWhere = { ...where };
        if (!user || user.role !== "admin") {
            finalWhere.status = "published";
            finalWhere.OR = [
                {
                    publishedAt: {
                        lte: new Date(),
                    },
                },
                {
                    publishedAt: null,
                },
            ];
        }
        return await this.prisma.post.findMany({
            ...(0, utils_1.toPrismaPagination)(pagination),
            where: finalWhere,
            include: {
                title: true,
                description: true,
                images: true,
            },
        });
    }
    async create(data, user) {
        if (user.role !== "admin") {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin"));
        }
        const post = await this.prisma.post.create({
            data: {
                title: {
                    create: data.title.map((item) => ({
                        ...item,
                        key: "title",
                    })),
                },
                description: {
                    create: data.description.map((item) => ({
                        ...item,
                        key: "description",
                    })),
                },
                status: data.status,
                publishedAt: data.publishedAt,
            },
            include: {
                title: true,
                description: true,
                images: true,
            },
        });
        console.log("created post", post.id);
        return post;
    }
    async update(id, data, user) {
        await this.canChange(id, user);
        return await this.updateOne(id, data);
    }
    async updateOne(id, data) {
        return await this.prisma.post.update({
            where: { id, deletedAt: null },
            data,
            include: {
                title: true,
                description: true,
                images: true,
            },
        });
    }
    async uploadPostImages(postId, files) {
        await this.getOneOrThrow(postId);
        const images = await Promise.all(files.map(async (file, index) => {
            const imageUrl = await this.minioService.uploadPostImage(file, postId, index);
            const image = await this.prisma.image.create({
                data: {
                    url: imageUrl,
                },
            });
            return image;
        }));
        await this.prisma.post.update({
            where: { id: postId },
            data: {
                images: {
                    connect: images.map((image) => ({ id: image.id })),
                },
            },
        });
        return images;
    }
    async softDeleteOne(id) {
        return await this.updateOne(id, { deletedAt: new Date() });
    }
};
exports.PostService = PostService;
exports.PostService = PostService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        minio_service_1.MinioService])
], PostService);
//# sourceMappingURL=post.service.js.map