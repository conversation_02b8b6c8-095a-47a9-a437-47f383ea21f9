<script lang="ts">
  import type { Locale } from "$lib";

  interface Props {
    locale: Locale;
  }

  const i18n = {
    en: {
      lens: "Lens",
      lensing: "Lensing",
      editLens: "Edit lens",
      before: "Before",
      from: "From",
      now: "now",
      showRead: "Show read",
      toggleLensing: "Toggle lensing",
      toggleDateRange: "Toggle date range",
    },
    ru: {
      lens: "Линза",
      lensing: "Линзирование",
      editLens: "Редактировать линзу",
      before: "До",
      from: "От",
      now: "сейчас",
      showRead: "Прочитанное",
      toggleLensing: "Переключить линзирование",
      toggleDateRange: "Переключить диапазон дат",
    },
  };

  const { locale }: Props = $props();

  const t = i18n[locale];

  // State
  let isCollapsed = $state(true);
  let selectedLens = $state("default");
  let showDateRange = $state(false);
  let beforeDate = $state(t.now);
  let fromDate = $state("");
  let showRead = $state(false);

  // Available lenses
  const lenses = [{ id: "default", name: "Default" }];

  function toggleCollapse() {
    isCollapsed = !isCollapsed;
  }

  function toggleDateRange() {
    showDateRange = !showDateRange;
  }
</script>

<div class="left-menu">
  <div class="left-menu-header d-flex justify-content-between align-items-center p-3 bg-light">
    <h6 class="mb-0">{t.lensing}</h6>
    <button
      class="btn btn-sm btn-link p-0"
      onclick={toggleCollapse}
      aria-label={t.toggleLensing}
      title={t.toggleLensing}
    >
      <i class="bi bi-chevron-{isCollapsed ? 'down' : 'up'}"></i>
    </button>
  </div>

  {#if !isCollapsed}
    <div class="left-menu-content p-3">
      <div class="mb-4">
        <div class="d-flex align-items-center mb-2">
          <select id="lensSelect" class="form-select form-select-sm" bind:value={selectedLens}>
            {#each lenses as lens}
              <option value={lens.id}>{lens.name}</option>
            {/each}
          </select>
          <button
            class="btn btn-sm btn-outline-secondary ms-2"
            title={t.editLens}
            aria-label={t.editLens}
          >
            <i class="bi bi-pencil"></i>
          </button>
        </div>

        <div class="date-filter mb-2">
          {#if showDateRange}
            <div class="d-flex align-items-center mb-2">
              <label class="form-label mb-0 me-2" for="beforeDateInput">{t.before}:</label>
              <input
                id="beforeDateInput"
                type="text"
                class="form-control form-control-sm"
                bind:value={beforeDate}
              />
              <button
                class="btn btn-sm btn-outline-secondary ms-2"
                onclick={toggleDateRange}
                aria-label={t.toggleDateRange}
                title={t.toggleDateRange}
              >
                <i class="bi bi-chevron-{showDateRange ? 'up' : 'down'}"></i>
              </button>
            </div>

            <div class="d-flex align-items-center mb-2">
              <label class="form-label mb-0 me-2" for="fromDateInput">{t.from}:</label>
              <input
                id="fromDateInput"
                type="text"
                class="form-control form-control-sm"
                bind:value={fromDate}
              />
            </div>
          {:else}
            <div class="d-flex align-items-center mb-2">
              <input
                id="beforeDateInputCollapsed"
                type="text"
                class="form-control form-control-sm"
                bind:value={beforeDate}
                aria-label={t.before}
              />
              <button
                class="btn btn-sm btn-outline-secondary ms-2"
                onclick={toggleDateRange}
                aria-label={t.toggleDateRange}
                title={t.toggleDateRange}
              >
                <i class="bi bi-chevron-{showDateRange ? 'up' : 'down'}"></i>
              </button>
            </div>
          {/if}
        </div>

        <div class="form-check">
          <input
            class="form-check-input"
            type="checkbox"
            id="showReadCheck"
            bind:checked={showRead}
          />
          <label class="form-check-label" for="showReadCheck">
            {t.showRead}
          </label>
        </div>
      </div>
    </div>
  {/if}
</div>

<style>
  .left-menu {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    overflow: hidden;
  }

  .left-menu-header {
    border-bottom: 1px solid #dee2e6;
  }

  .form-label {
    font-weight: 500;
  }
</style>
