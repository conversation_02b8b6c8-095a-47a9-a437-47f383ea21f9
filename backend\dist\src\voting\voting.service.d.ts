import { Prisma } from "@prisma/client";
import { CurrentUser } from "src/auth/types";
import { Localization } from "src/zod/helper";
import { BaseService } from "src/common/base-service";
import { PrismaService } from "src/prisma/prisma.service";
type CreateDto = {
    votesRequired: number;
    endsAt: Date;
    title: Localization[];
    description: Localization[];
    options: {
        title: Localization[];
    }[];
};
export declare class VotingService extends BaseService {
    private readonly prisma;
    constructor(prisma: PrismaService);
    canGet(id: string, user: CurrentUser): Promise<true>;
    check(ids: string[]): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        votesRequired: number;
        endsAt: Date;
    }[]>;
    getOne(id: string): Promise<({
        description: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
        options: ({
            title: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                key: string;
                locale: import(".prisma/client").$Enums.Locale;
                value: string;
            }[];
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            votingId: string;
        })[];
        title: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        votesRequired: number;
        endsAt: Date;
    }) | null>;
    getOneOrThrow(id: string): Promise<{
        description: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
        options: ({
            title: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                key: string;
                locale: import(".prisma/client").$Enums.Locale;
                value: string;
            }[];
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            votingId: string;
        })[];
        title: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        votesRequired: number;
        endsAt: Date;
    }>;
    getMany(where: Prisma.VotingWhereInput, pagination?: {
        page: number;
        size: number;
    }): Promise<({
        description: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
        options: ({
            title: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                key: string;
                locale: import(".prisma/client").$Enums.Locale;
                value: string;
            }[];
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            votingId: string;
        })[];
        title: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        votesRequired: number;
        endsAt: Date;
    })[]>;
    createOne(data: Prisma.VotingCreateInput): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        votesRequired: number;
        endsAt: Date;
    }>;
    createMany(data: Prisma.VotingCreateManyInput[]): Promise<Prisma.BatchPayload>;
    create(data: CreateDto): Promise<{
        description: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
        options: ({
            title: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                key: string;
                locale: import(".prisma/client").$Enums.Locale;
                value: string;
            }[];
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            votingId: string;
        })[];
        title: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        votesRequired: number;
        endsAt: Date;
    }>;
    updateOne(id: string, data: Prisma.VotingUpdateInput): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        votesRequired: number;
        endsAt: Date;
    }>;
    updateMany(where: Prisma.VotingWhereInput, data: Prisma.VotingUpdateInput): Promise<Prisma.BatchPayload>;
    softDeleteOne(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        votesRequired: number;
        endsAt: Date;
    }>;
    softDeleteMany(where: Prisma.VotingWhereInput): Promise<Prisma.BatchPayload>;
    deleteOne(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        votesRequired: number;
        endsAt: Date;
    }>;
    deleteMany(where: Prisma.VotingWhereInput): Promise<Prisma.BatchPayload>;
}
export {};
