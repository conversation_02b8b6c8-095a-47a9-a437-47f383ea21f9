"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Logout = exports.Login = exports.Register = exports.Me = exports.Otp = void 0;
const prisma = require("@prisma/client");
const zod_1 = require("../../zod");
const otp = zod_1.z.string().nonempty().length(6);
exports.Otp = zod_1.z.object({
    email: zod_1.z.string().email(),
});
exports.Me = zod_1.z.object({
    id: zod_1.ZodHelper.Uuid,
    email: zod_1.z.string().email(),
    role: zod_1.z.nativeEnum(prisma.UserRole),
    name: zod_1.Zod<PERSON>el<PERSON>.Localizations,
    description: zod_1.ZodHelper.Localizations,
    images: zod_1.z.array(zod_1.ZodHelper.Image).optional(),
    joinedAt: zod_1.z.date(),
});
exports.Register = zod_1.z.object({
    referrerId: zod_1.z.string().uuid().nullable().optional(),
    email: zod_1.z.string().email(),
    otp,
});
exports.Login = zod_1.z.object({
    email: zod_1.z.string().email(),
    otp,
});
exports.Logout = zod_1.z.object({
    refreshToken: zod_1.z.string().nonempty(),
});
//# sourceMappingURL=dto.js.map