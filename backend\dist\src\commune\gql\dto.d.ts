import { z, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "src/zod";
export declare const communeMemberTypename = "CommuneMember";
export type CommuneMemberType = ZodHelper.Infer<typeof CommuneMemberType>;
export declare const CommuneMemberType: z.<PERSON>od<PERSON>num<{
    commune: "commune";
    user: "user";
}>;
export type CommuneMember = ZodHelper.Infer<typeof CommuneMember>;
export declare const CommuneMember: z.ZodObject<{
    __typename: z.ZodDefault<z.ZodLiteral<"CommuneMember">>;
    id: z.ZodString;
    actorType: z.Zod<PERSON>num<{
        commune: "commune";
        user: "user";
    }>;
    actorId: z.ZodString;
    joinedAt: z.Zod<PERSON>eline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    leftAt: z.ZodNullable<z.ZodPipeline<z.ZodUnion<[z.<PERSON>od<PERSON>, z.<PERSON>od<PERSON>, z.ZodDate]>, z.<PERSON>>>;
    createdAt: z.Zod<PERSON><PERSON><PERSON>e<z.<PERSON>od<PERSON>n<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    actorType: "user" | "commune";
    actorId: string;
    joinedAt: Date;
    leftAt: Date | null;
    __typename: "CommuneMember";
}, {
    id: string;
    createdAt: string | number | Date;
    updatedAt: string | number | Date;
    actorType: "user" | "commune";
    actorId: string;
    joinedAt: string | number | Date;
    leftAt: string | number | Date | null;
    __typename?: "CommuneMember" | undefined;
}>;
export declare const CommuneMembers: z.ZodArray<z.ZodObject<{
    __typename: z.ZodDefault<z.ZodLiteral<"CommuneMember">>;
    id: z.ZodString;
    actorType: z.ZodNativeEnum<{
        commune: "commune";
        user: "user";
    }>;
    actorId: z.ZodString;
    joinedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    leftAt: z.ZodNullable<z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>>;
    createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    actorType: "user" | "commune";
    actorId: string;
    joinedAt: Date;
    leftAt: Date | null;
    __typename: "CommuneMember";
}, {
    id: string;
    createdAt: string | number | Date;
    updatedAt: string | number | Date;
    actorType: "user" | "commune";
    actorId: string;
    joinedAt: string | number | Date;
    leftAt: string | number | Date | null;
    __typename?: "CommuneMember" | undefined;
}>, "many">;
export declare const communeTypename = "Commune";
export type Commune = ZodHelper.Infer<typeof Commune>;
export declare const Commune: z.ZodObject<{
    __typename: z.ZodDefault<z.ZodLiteral<"Commune">>;
    id: z.ZodString;
    members: z.ZodArray<z.ZodObject<{
        __typename: z.ZodDefault<z.ZodLiteral<"CommuneMember">>;
        id: z.ZodString;
        actorType: z.ZodNativeEnum<{
            commune: "commune";
            user: "user";
        }>;
        actorId: z.ZodString;
        joinedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
        leftAt: z.ZodNullable<z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>>;
        createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
        updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        actorType: "user" | "commune";
        actorId: string;
        joinedAt: Date;
        leftAt: Date | null;
        __typename: "CommuneMember";
    }, {
        id: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
        actorType: "user" | "commune";
        actorId: string;
        joinedAt: string | number | Date;
        leftAt: string | number | Date | null;
        __typename?: "CommuneMember" | undefined;
    }>, "many">;
    createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    members: {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        actorType: "user" | "commune";
        actorId: string;
        joinedAt: Date;
        leftAt: Date | null;
        __typename: "CommuneMember";
    }[];
    __typename: "Commune";
}, {
    id: string;
    createdAt: string | number | Date;
    updatedAt: string | number | Date;
    members: {
        id: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
        actorType: "user" | "commune";
        actorId: string;
        joinedAt: string | number | Date;
        leftAt: string | number | Date | null;
        __typename?: "CommuneMember" | undefined;
    }[];
    __typename?: "Commune" | undefined;
}>;
export declare const Communes: z.ZodArray<z.ZodObject<{
    __typename: z.ZodDefault<z.ZodLiteral<"Commune">>;
    id: z.ZodString;
    members: z.ZodArray<z.ZodObject<{
        __typename: z.ZodDefault<z.ZodLiteral<"CommuneMember">>;
        id: z.ZodString;
        actorType: z.ZodNativeEnum<{
            commune: "commune";
            user: "user";
        }>;
        actorId: z.ZodString;
        joinedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
        leftAt: z.ZodNullable<z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>>;
        createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
        updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        actorType: "user" | "commune";
        actorId: string;
        joinedAt: Date;
        leftAt: Date | null;
        __typename: "CommuneMember";
    }, {
        id: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
        actorType: "user" | "commune";
        actorId: string;
        joinedAt: string | number | Date;
        leftAt: string | number | Date | null;
        __typename?: "CommuneMember" | undefined;
    }>, "many">;
    createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    members: {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        actorType: "user" | "commune";
        actorId: string;
        joinedAt: Date;
        leftAt: Date | null;
        __typename: "CommuneMember";
    }[];
    __typename: "Commune";
}, {
    id: string;
    createdAt: string | number | Date;
    updatedAt: string | number | Date;
    members: {
        id: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
        actorType: "user" | "commune";
        actorId: string;
        joinedAt: string | number | Date;
        leftAt: string | number | Date | null;
        __typename?: "CommuneMember" | undefined;
    }[];
    __typename?: "Commune" | undefined;
}>, "many">;
export type CreateCommuneInput = ZodHelper.Infer<typeof CreateCommuneInput>;
export declare const CreateCommuneInput: z.ZodObject<{
    headUserId: z.ZodString;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    name: {
        locale: "en" | "ru";
        value: string;
    }[];
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    headUserId: string;
}, {
    name: {
        locale: "en" | "ru";
        value: string;
    }[];
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    headUserId: string;
}>;
export type UpdateCommuneInput = ZodHelper.Infer<typeof UpdateCommuneInput>;
export declare const UpdateCommuneInput: z.ZodObject<{
    id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    id: string;
}, {
    id: string;
}>;
export type CreateCommuneMemberInput = ZodHelper.Infer<typeof CreateCommuneMemberInput>;
export declare const CreateCommuneMemberInput: z.ZodObject<{
    communeId: z.ZodString;
    actorType: z.ZodNativeEnum<{
        commune: "commune";
        user: "user";
    }>;
    actorId: z.ZodString;
}, "strip", z.ZodTypeAny, {
    actorType: "user" | "commune";
    actorId: string;
    communeId: string;
}, {
    actorType: "user" | "commune";
    actorId: string;
    communeId: string;
}>;
export type UpdateCommuneMemberInput = ZodHelper.Infer<typeof UpdateCommuneMemberInput>;
export declare const UpdateCommuneMemberInput: z.ZodObject<{
    id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    id: string;
}, {
    id: string;
}>;
