<script lang="ts">
  import type { Localization } from "$lib";

  import { onMount } from "svelte";
  import { findLocalizationForUserLocales, fetchWithAuth } from "$lib";
  import CommuneImageCarousel from "./commune-image-carousel.svelte";
  import CreateCommuneModal from "./create-commune-modal.svelte";

  type CommuneMemberType = "user" | "commune";

  interface Commune {
    id: string;
    name: Localization[];
    description: Localization[];
    memberCount: number;
    headMember: {
      actorType: CommuneMemberType;
      actorId: string;
      name: Localization[];
    };
    images?: {
      id: string;
      url: string;
      source: string;
    }[];
  }

  const i18n = {
    en: {
      communes: "Communes",
      create: "Create",
      loading: "Loading...",
      noCommunes: "No communes found",
      noDescription: "No description",
      member: "member",
      members: "members",
      headMember: "Head Member",
      errorFetchingCommunes: "Failed to fetch communes",
      errorOccurred: "An error occurred while fetching communes",
      first: "First",
      previous: "Previous",
      next: "Next",
      last: "Last",
      page: "Page",
    },
    ru: {
      communes: "Коммуны",
      create: "Создать",
      loading: "Загрузка...",
      noCommunes: "Коммуны не найдены",
      noDescription: "Нет описания",
      member: "участник",
      members: "участников",
      headMember: "Глава",
      errorFetchingCommunes: "Не удалось загрузить коммуны",
      errorOccurred: "Произошла ошибка при загрузке коммун",
      first: "Первая",
      previous: "Предыдущая",
      next: "Следующая",
      last: "Последняя",
      page: "Страница",
    },
  };

  // Get the locale from the page data
  const { data } = $props();

  const t = i18n[data.locale];

  // State using runes
  let communes = $state<Commune[]>([]);
  let currentPage = $state(1);
  let totalPages = $state(1);
  let loading = $state(true);
  let error = $state<string | null>(null);
  let showCreateModal = $state(false);

  const pageSize = 20;

  // Function to fetch communes
  async function fetchCommunes() {
    loading = true;

    try {
      const response = await fetchWithAuth(`/api/commune?page=${currentPage}&size=${pageSize}`);

      if (!response.ok) {
        throw new Error(`${t.errorFetchingCommunes}: ${response.statusText}`);
      }

      const data: Commune[] = await response.json();
      communes = data;

      // Set total pages based on data length
      totalPages = Math.ceil(data.length / pageSize) || 1;
    } catch (err) {
      error = err instanceof Error ? err.message : t.errorOccurred;
      console.error(err);
    } finally {
      loading = false;
    }
  }

  // Handle page change
  function handlePageChange(page: number) {
    currentPage = page;
  }

  // Generate pagination array
  function getPaginationArray() {
    return Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
      // Show pagination centered on current page
      let pageNum;
      if (totalPages <= 5) {
        pageNum = i + 1;
      } else {
        const start = Math.max(1, Math.min(currentPage - 2, totalPages - 4));
        pageNum = start + i;
      }
      return pageNum;
    });
  }

  // Modal handlers
  function handleCreateModalOpen() {
    showCreateModal = true;
  }

  function handleCreateModalClose() {
    showCreateModal = false;
  }

  // $effect(() => {
  //   if (currentPage) {
  //     fetchCommunes();
  //   }
  // });

  onMount(() => {
    fetchCommunes();
  });
</script>

<div class="container my-4 mb-5">
  <div class="d-flex justify-content-between align-items-center my-4">
    <h1>{t.communes}</h1>
    <button class="btn btn-primary" onclick={handleCreateModalOpen}>
      {t.create}
    </button>
  </div>

  {#if loading}
    <div class="text-center py-5">
      <div class="spinner-border" role="status">
        <span class="visually-hidden">{t.loading}</span>
      </div>
    </div>
  {:else if error}
    <div class="alert alert-danger" role="alert">
      {error}
    </div>
  {:else if communes.length === 0}
    <div class="text-center py-5">
      <p class="text-muted">{t.noCommunes}</p>
    </div>
  {:else}
    <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-5 g-4">
      {#each communes as commune (commune.id)}
        <div class="col">
          <div class="card h-100 shadow-sm hover-card">
            <!-- Commune image carousel -->
            <CommuneImageCarousel
              images={commune.images || []}
              communeId={commune.id}
              locale={data.locale}
            />
            <a href={`/communes/${commune.id}`} class="text-decoration-none text-black">
              <div class="card-body d-flex flex-column">
                <h5 class="card-title fs-5 text-truncate">
                  {findLocalizationForUserLocales(commune.name)}
                </h5>

                <p class="card-text text-muted small" style="height: 3rem; overflow: hidden">
                  {findLocalizationForUserLocales(commune.description) || t.noDescription}
                </p>

                <div class="mt-auto">
                  <span class="badge bg-primary mb-2">
                    {commune.memberCount}
                    {commune.memberCount === 1 ? t.member : t.members}
                  </span>

                  <div class="small text-muted">
                    <div>{t.headMember} ({commune.headMember.actorType}):</div>
                    <div class="d-flex flex-column">
                      {findLocalizationForUserLocales(commune.headMember.name)}
                    </div>
                  </div>
                </div>
              </div>
            </a>
          </div>
        </div>
      {/each}
    </div>

    <!-- Pagination -->
    {#if totalPages > 1}
      <div class="d-flex justify-content-center mt-4">
        <nav aria-label="Commune pagination">
          <ul class="pagination">
            <li class={`page-item ${currentPage === 1 ? "disabled" : ""}`}>
              <button class="page-link" aria-label={t.first} onclick={() => handlePageChange(1)}>
                <span aria-hidden="true">&laquo;&laquo;</span>
                <span class="visually-hidden">{t.first}</span>
              </button>
            </li>
            <li class={`page-item ${currentPage === 1 ? "disabled" : ""}`}>
              <button
                class="page-link"
                aria-label={t.previous}
                onclick={() => handlePageChange(currentPage - 1)}
              >
                <span aria-hidden="true">&laquo;</span>
                <span class="visually-hidden">{t.previous}</span>
              </button>
            </li>

            <!-- Show current page and adjacent pages -->
            {#each getPaginationArray() as pageNum}
              <li class={`page-item ${pageNum === currentPage ? "active" : ""}`}>
                <button
                  class="page-link"
                  onclick={() => handlePageChange(pageNum)}
                  aria-label={`${t.page} ${pageNum}`}
                  aria-current={pageNum === currentPage ? "page" : undefined}
                >
                  {pageNum}
                </button>
              </li>
            {/each}

            <li class={`page-item ${currentPage === totalPages ? "disabled" : ""}`}>
              <button
                class="page-link"
                aria-label={t.next}
                onclick={() => handlePageChange(currentPage + 1)}
              >
                <span aria-hidden="true">&raquo;</span>
                <span class="visually-hidden">{t.next}</span>
              </button>
            </li>
            <li class={`page-item ${currentPage === totalPages ? "disabled" : ""}`}>
              <button
                class="page-link"
                aria-label={t.last}
                onclick={() => handlePageChange(totalPages)}
              >
                <span aria-hidden="true">&raquo;&raquo;</span>
                <span class="visually-hidden">{t.last}</span>
              </button>
            </li>
          </ul>
        </nav>
      </div>
    {/if}
  {/if}

  <!-- Create Commune Modal -->
  <CreateCommuneModal show={showCreateModal} locale={data.locale} onHide={handleCreateModalClose} />
</div>

<style>
  .hover-card {
    cursor: pointer;
    transition: transform 0.2s;
  }

  .hover-card:hover {
    transform: translateY(-5px);
  }
</style>
