"commune"
type Commune {
    id: ID!

    members: [CommuneMember!]!

    createdAt: DateTime!
    updatedAt: DateTime!
}

"commune-member-type"
enum CommuneMemberType {
    commune
    user
}

"commune-member"
type CommuneMember {
    id: ID!

    actorType: CommuneMemberType!
    actorId: ID!

    joinedAt: DateTime!
    leftAt: DateTime

    createdAt: DateTime!
    updatedAt: DateTime!
}

type Query {
    getCommune(id: ID!): Commune
    getCommunes: [Commune!]!

    getCommuneMember(id: ID!): CommuneMember!
    getCommuneMembers(communeId: ID!): [CommuneMember!]!
}

"create-commune-input"
input CreateCommuneInput {
    headUserId: ID!
}

"update-commune-input"
input UpdateCommuneInput {
    id: ID!
}

"create-commune-member-input"
input CreateCommuneMemberInput {
    communeId: ID!
    actorType: CommuneMemberType!
    actorId: ID!
}

"update-commune-member-input"
input UpdateCommuneMemberInput {
    id: ID!
}

type Mutation {
    createCommune(input: CreateCommuneInput!): Commune!
    updateCommune(input: UpdateCommuneInput!): Commune!
    deleteCommune(id: ID!): Boolean!

    createCommuneMember(input: CreateCommuneMemberInput!): CommuneMember!
    updateCommuneMember(input: UpdateCommuneMemberInput!): CommuneMember!
    deleteCommuneMember(id: ID!): Boolean!
}
