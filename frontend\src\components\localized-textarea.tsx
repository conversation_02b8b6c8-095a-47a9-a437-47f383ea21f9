import { useState } from "react";
import { Form, Dropdown } from "react-bootstrap";
import { Localization } from "@/utils/find-localization";
import { useLocale } from "next-intl";
import { Locale } from "@/app/types";

export interface LocalizedTextareaProps {
  id: string;
  label: string;
  placeholder: string;
  rows?: number;
  required?: boolean;
  value: Localization[];
  onChange: (value: Localization[]) => void;
}

const i18n = {
  en: {
    languages: {
      en: "English",
      ru: "Russian",
    },
    providedTranslations: "Provided translations:",
  },

  ru: {
    languages: {
      en: "Английский",
      ru: "Русский",
    },
    providedTranslations: "Указанные переводы:",
  },
};

export function LocalizedTextarea({ id, label, placeholder, rows = 3, required, value, onChange }: LocalizedTextareaProps) {
  const locale = useLocale() as Locale;
  const t = i18n[locale];

  const [selectedLanguage, setSelectedLanguage] = useState<Locale>("en");

  // Find the value for the currently selected language
  const getCurrentValue = () => {
    const localization = value.find(val => val.locale === selectedLanguage);
    return localization?.value || "";
  };

  // Update the value for the currently selected language
  const handleChange = (newValue: string) => {
    const updatedValues = [...value];
    const existingIndex = updatedValues.findIndex(val => val.locale === selectedLanguage);

    if (existingIndex >= 0) {
      updatedValues[existingIndex] = { locale: selectedLanguage as Locale, value: newValue };
    } else {
      updatedValues.push({ locale: selectedLanguage as Locale, value: newValue });
    }

    onChange(updatedValues);
  };

  // Switch the selected language
  const handleLanguageChange = (langCode: Locale) => {
    setSelectedLanguage(langCode);
  };

  // Get the display name of the current language
  const getLanguageDisplay = () => {
    return selectedLanguage.toUpperCase();
  };

  return (
    <Form.Group className="mb-3" controlId={id}>
      <div className="d-flex justify-content-between align-items-center mb-2">
        <Form.Label className="mb-0">
          {label} {required && <span className="text-danger">*</span>}
        </Form.Label>
        <Dropdown>
          <Dropdown.Toggle
            variant="outline-secondary"
            id={`dropdown-${id}`}
            size="sm"
            style={{ width: "60px", display: "flex", justifyContent: "space-between", alignItems: "center" }}
          >
            {getLanguageDisplay()}
          </Dropdown.Toggle>

          <Dropdown.Menu align="end">
            <Dropdown.Item
              onClick={() => handleLanguageChange("en")}
              active={selectedLanguage === "en"}
            >
              {t.languages.en}
            </Dropdown.Item>

            <Dropdown.Item
              onClick={() => handleLanguageChange("ru")}
              active={selectedLanguage === "ru"}
            >
              {t.languages.ru}
            </Dropdown.Item>
          </Dropdown.Menu>
        </Dropdown>
      </div>

      <Form.Control
        as="textarea"
        rows={rows}
        placeholder={placeholder}
        value={getCurrentValue()}
        onChange={(e) => handleChange(e.target.value)}
        required={required}
      />

      {value.length > 0 && (
        <div className="mt-2 small text-muted">
          <div>{t.providedTranslations}</div>
          <ul className="list-unstyled mb-0 mt-1">
            {value.filter(Boolean).map(val => (
              <li key={val.locale} className="badge bg-light text-dark me-1">
                {t.languages[val.locale]}: {
                  val.value.length > 50
                    ? val.value.slice(0, 47) + "..."
                    : val.value
                }
              </li>
            ))}
          </ul>
        </div>
      )}
    </Form.Group>
  );
}
