import * as Gql from "src/graphql";
import { VoteService } from "../vote.service";
import * as Dto from "./dto";
import { CurrentUser } from "src/auth/types";
export type IVoteResolver = Gql.IResolver<never, "createVote">;
export declare class VoteResolver implements IVoteResolver {
    private readonly voteService;
    constructor(voteService: VoteService);
    createVote(input: Dto.CreateVoteInput, user?: CurrentUser): Promise<boolean>;
}
