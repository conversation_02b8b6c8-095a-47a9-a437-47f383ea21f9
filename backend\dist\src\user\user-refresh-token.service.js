"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserRefreshTokenService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let UserRefreshTokenService = class UserRefreshTokenService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(createUserRefreshTokenDto) {
        const userRefreshToken = await this.prisma.userRefreshToken.create({
            data: {
                userId: createUserRefreshTokenDto.userId,
                token: createUserRefreshTokenDto.token,
                expiresAt: createUserRefreshTokenDto.expiresAt,
                ipAddress: createUserRefreshTokenDto.ipAddress,
                userAgent: createUserRefreshTokenDto.userAgent,
            },
        });
        return userRefreshToken;
    }
    async findAll(dto) {
        const userRefreshTokens = await this.prisma.userRefreshToken.findMany({
            where: {
                userId: dto.params.userId,
                deletedAt: null,
            },
        });
        return userRefreshTokens;
    }
    async findByValue(value) {
        const where = {
            token: value,
            deletedAt: null,
        };
        console.log({ where });
        const userRefreshToken = await this.prisma.userRefreshToken.findFirst({
            where: where,
            include: {
                user: true,
            },
        });
        return userRefreshToken;
    }
    async softDelete(id) {
        await this.prisma.userRefreshToken.update({
            where: {
                id,
            },
            data: {
                deletedAt: new Date(),
            },
        });
        return true;
    }
    async delete(id) {
        await this.prisma.userRefreshToken.delete({
            where: { id },
        });
        return true;
    }
    async revoke(dto) {
        await this.prisma.userRefreshToken.update({
            where: { id: dto.id },
            data: {
                revokedAt: new Date(),
                revokeReason: dto.reason,
            },
        });
        return true;
    }
};
exports.UserRefreshTokenService = UserRefreshTokenService;
exports.UserRefreshTokenService = UserRefreshTokenService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], UserRefreshTokenService);
//# sourceMappingURL=user-refresh-token.service.js.map