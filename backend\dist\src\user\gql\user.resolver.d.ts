import * as Gql from "src/graphql";
import { UserService } from "../user.service";
export type IUserResolver = Gql.IResolver<"getUser" | "getUsers", never>;
export declare class UserResolver implements IUserResolver {
    private readonly userService;
    constructor(userService: UserService);
    getUser(id: string): Promise<Gql.User>;
    getUsers(page: number, size: number): Promise<Gql.User[]>;
}
