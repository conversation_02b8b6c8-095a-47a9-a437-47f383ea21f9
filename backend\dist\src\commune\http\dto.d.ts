import { z, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "src/zod";
export type CommuneMemberType = ZodHelper.Infer<typeof CommuneMemberType>;
export declare const CommuneMemberType: z.ZodNativeEnum<{
    commune: "commune";
    user: "user";
}>;
export type CommuneMember = ZodHelper.Infer<typeof CommuneMember>;
export declare const CommuneMember: z.ZodObject<{
    id: z.ZodString;
    communeId: z.ZodString;
    actorType: z.ZodNativeEnum<{
        commune: "commune";
        user: "user";
    }>;
    actorId: z.ZodString;
    name: z.<PERSON><PERSON><PERSON><z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    images: z.<PERSON><z.ZodObject<{
        id: z.Zod<PERSON>;
        url: z.ZodString;
        createdAt: z.Z<PERSON><z.ZodU<PERSON>n<[z.ZodNumber, z.Zod<PERSON>, z.Zod<PERSON>ate]>, z.ZodDate>;
        updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        url: string;
    }, {
        id: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
        url: string;
    }>, "many">;
    joinedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    leftAt: z.ZodNullable<z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>>;
}, "strip", z.ZodTypeAny, {
    id: string;
    images: {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        url: string;
    }[];
    name: {
        locale: "en" | "ru";
        value: string;
    }[];
    actorType: "user" | "commune";
    actorId: string;
    joinedAt: Date;
    leftAt: Date | null;
    communeId: string;
}, {
    id: string;
    images: {
        id: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
        url: string;
    }[];
    name: {
        locale: "en" | "ru";
        value: string;
    }[];
    actorType: "user" | "commune";
    actorId: string;
    joinedAt: string | number | Date;
    leftAt: string | number | Date | null;
    communeId: string;
}>;
export declare const CommuneMembers: z.ZodArray<z.ZodObject<{
    id: z.ZodString;
    communeId: z.ZodString;
    actorType: z.ZodNativeEnum<{
        commune: "commune";
        user: "user";
    }>;
    actorId: z.ZodString;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    images: z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        url: z.ZodString;
        createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
        updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        url: string;
    }, {
        id: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
        url: string;
    }>, "many">;
    joinedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    leftAt: z.ZodNullable<z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>>;
}, "strip", z.ZodTypeAny, {
    id: string;
    images: {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        url: string;
    }[];
    name: {
        locale: "en" | "ru";
        value: string;
    }[];
    actorType: "user" | "commune";
    actorId: string;
    joinedAt: Date;
    leftAt: Date | null;
    communeId: string;
}, {
    id: string;
    images: {
        id: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
        url: string;
    }[];
    name: {
        locale: "en" | "ru";
        value: string;
    }[];
    actorType: "user" | "commune";
    actorId: string;
    joinedAt: string | number | Date;
    leftAt: string | number | Date | null;
    communeId: string;
}>, "many">;
export type Commune = ZodHelper.Infer<typeof Commune>;
export declare const Commune: z.ZodObject<{
    id: z.ZodString;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    headMember: z.ZodObject<{
        actorType: z.ZodNativeEnum<{
            commune: "commune";
            user: "user";
        }>;
        actorId: z.ZodString;
        name: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            locale: "en" | "ru";
            value: string;
        }, {
            locale: "en" | "ru";
            value: string;
        }>, "many">;
    }, "strip", z.ZodTypeAny, {
        name: {
            locale: "en" | "ru";
            value: string;
        }[];
        actorType: "user" | "commune";
        actorId: string;
    }, {
        name: {
            locale: "en" | "ru";
            value: string;
        }[];
        actorType: "user" | "commune";
        actorId: string;
    }>;
    memberCount: z.ZodNumber;
    images: z.ZodOptional<z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        url: z.ZodString;
        createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
        updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        url: string;
    }, {
        id: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
        url: string;
    }>, "many">>;
    createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    name: {
        locale: "en" | "ru";
        value: string;
    }[];
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    headMember: {
        name: {
            locale: "en" | "ru";
            value: string;
        }[];
        actorType: "user" | "commune";
        actorId: string;
    };
    memberCount: number;
    images?: {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        url: string;
    }[] | undefined;
}, {
    id: string;
    createdAt: string | number | Date;
    updatedAt: string | number | Date;
    name: {
        locale: "en" | "ru";
        value: string;
    }[];
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    headMember: {
        name: {
            locale: "en" | "ru";
            value: string;
        }[];
        actorType: "user" | "commune";
        actorId: string;
    };
    memberCount: number;
    images?: {
        id: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
        url: string;
    }[] | undefined;
}>;
export declare const Communes: z.ZodArray<z.ZodObject<{
    id: z.ZodString;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    headMember: z.ZodObject<{
        actorType: z.ZodNativeEnum<{
            commune: "commune";
            user: "user";
        }>;
        actorId: z.ZodString;
        name: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            locale: "en" | "ru";
            value: string;
        }, {
            locale: "en" | "ru";
            value: string;
        }>, "many">;
    }, "strip", z.ZodTypeAny, {
        name: {
            locale: "en" | "ru";
            value: string;
        }[];
        actorType: "user" | "commune";
        actorId: string;
    }, {
        name: {
            locale: "en" | "ru";
            value: string;
        }[];
        actorType: "user" | "commune";
        actorId: string;
    }>;
    memberCount: z.ZodNumber;
    images: z.ZodOptional<z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        url: z.ZodString;
        createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
        updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        url: string;
    }, {
        id: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
        url: string;
    }>, "many">>;
    createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    name: {
        locale: "en" | "ru";
        value: string;
    }[];
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    headMember: {
        name: {
            locale: "en" | "ru";
            value: string;
        }[];
        actorType: "user" | "commune";
        actorId: string;
    };
    memberCount: number;
    images?: {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        url: string;
    }[] | undefined;
}, {
    id: string;
    createdAt: string | number | Date;
    updatedAt: string | number | Date;
    name: {
        locale: "en" | "ru";
        value: string;
    }[];
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    headMember: {
        name: {
            locale: "en" | "ru";
            value: string;
        }[];
        actorType: "user" | "commune";
        actorId: string;
    };
    memberCount: number;
    images?: {
        id: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
        url: string;
    }[] | undefined;
}>, "many">;
export type CreateCommuneInput = ZodHelper.Infer<typeof CreateCommuneInput>;
export declare const CreateCommuneInput: z.ZodPipeline<z.ZodEffects<z.ZodString, any, string>, z.ZodObject<{
    headUserId: z.ZodOptional<z.ZodString>;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    name: {
        locale: "en" | "ru";
        value: string;
    }[];
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    headUserId?: string | undefined;
}, {
    name: {
        locale: "en" | "ru";
        value: string;
    }[];
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    headUserId?: string | undefined;
}>>;
export type UpdateCommuneInput = ZodHelper.Infer<typeof UpdateCommuneInput>;
export declare const UpdateCommuneInput: z.ZodObject<{
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    name: {
        locale: "en" | "ru";
        value: string;
    }[];
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
}, {
    name: {
        locale: "en" | "ru";
        value: string;
    }[];
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
}>;
export type CreateCommuneMemberInput = ZodHelper.Infer<typeof CreateCommuneMemberInput>;
export declare const CreateCommuneMemberInput: z.ZodObject<{
    actorType: z.ZodNativeEnum<{
        commune: "commune";
        user: "user";
    }>;
    actorId: z.ZodString;
}, "strip", z.ZodTypeAny, {
    actorType: "user" | "commune";
    actorId: string;
}, {
    actorType: "user" | "commune";
    actorId: string;
}>;
export type UpdateCommuneMemberInput = ZodHelper.Infer<typeof UpdateCommuneMemberInput>;
export declare const UpdateCommuneMemberInput: z.ZodObject<{}, "strip", z.ZodTypeAny, {}, {}>;
