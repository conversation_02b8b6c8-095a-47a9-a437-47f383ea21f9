import { z, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "src/zod";
export declare const typename = "Vote";
export type Vote = ZodHelper.Infer<typeof Vote>;
export declare const Vote: z.ZodObject<{
    __typename: z.<PERSON>od<PERSON>efault<z.ZodLiteral<"Vote">>;
} & {
    id: z.ZodString;
    actorType: z.ZodN<PERSON>num<{
        commune: "commune";
        user: "user";
    }>;
    actorId: z.ZodString;
    createdAt: z.Z<PERSON>ip<PERSON><z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    updatedAt: z.<PERSON>elin<PERSON><z.ZodUnion<[z.ZodNumber, z.ZodString, z.Zod<PERSON>ate]>, z.ZodDate>;
}, z.<PERSON>eysParam, z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    actorType: "user" | "commune";
    actorId: string;
    __typename: "Vote";
}, {
    id: string;
    createdAt: string | number | Date;
    updatedAt: string | number | Date;
    actorType: "user" | "commune";
    actorId: string;
    __typename?: "Vote" | undefined;
}>;
export type CreateVoteInput = ZodHelper.Infer<typeof CreateVoteInput>;
export declare const CreateVoteInput: z.ZodObject<{
    votingId: z.ZodString;
    optionId: z.ZodString;
}, "strip", z.ZodTypeAny, {
    votingId: string;
    optionId: string;
}, {
    votingId: string;
    optionId: string;
}>;
