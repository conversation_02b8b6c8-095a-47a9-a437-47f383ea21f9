import { Header } from "./components/header/header";
import { Footer } from "./components/footer/footer";
import { getLocale } from "@/utils/get-translation";

export default async function IndexLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const locale = await getLocale();

  return (
    <>
      <Header locale={locale} />

      <main className="container flex-grow-1 mb-5">
        {children}
      </main>

      <Footer locale={locale} />
    </>
  );
}
