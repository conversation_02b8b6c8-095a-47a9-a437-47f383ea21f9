import { Prisma } from "@prisma/client";
import { CurrentUser } from "src/auth/types";
import { BaseService } from "src/common/base-service";
import { PrismaService } from "src/prisma/prisma.service";
export declare class VotingOptionService extends BaseService {
    private readonly prisma;
    constructor(prisma: PrismaService);
    canGet(id: string, user: CurrentUser): Promise<true>;
    check(ids: string[]): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        votingId: string;
    }[]>;
    getOne(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        votingId: string;
    } | null>;
    getOneOrThrow(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        votingId: string;
    }>;
    getMany(where: Prisma.VotingOptionWhereInput, pagination?: {
        page: number;
        size: number;
    }): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        votingId: string;
    }[]>;
    createOne(data: Prisma.VotingOptionCreateInput): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        votingId: string;
    }>;
    createMany(data: Prisma.VotingOptionCreateManyInput[]): Promise<Prisma.BatchPayload>;
    updateOne(id: string, data: Prisma.VotingOptionUpdateInput): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        votingId: string;
    }>;
    updateMany(where: Prisma.VotingOptionWhereInput, data: Prisma.VotingOptionUpdateInput): Promise<Prisma.BatchPayload>;
    softDeleteOne(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        votingId: string;
    }>;
    softDeleteMany(where: Prisma.VotingOptionWhereInput): Promise<Prisma.BatchPayload>;
    deleteOne(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        votingId: string;
    }>;
    deleteMany(where: Prisma.VotingOptionWhereInput): Promise<Prisma.BatchPayload>;
}
