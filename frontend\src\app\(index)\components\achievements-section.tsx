export const achievements = [
  {
    title: "Community Growth",
    description: "Successfully built a diverse community of over 500 active members from 30+ countries, creating a global network of innovators committed to technological progress for social good.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" className="bi bi-people-fill text-primary" viewBox="0 0 16 16">
        <path d="M7 14s-1 0-1-1 1-4 5-4 5 3 5 4-1 1-1 1H7Zm4-6a3 3 0 1 0 0-6 3 3 0 0 0 0 6Zm-5.784 6A2.238 2.238 0 0 1 5 13c0-1.355.68-2.75 1.936-3.72A6.325 6.325 0 0 0 5 9c-4 0-5 3-5 4s1 1 1 1h4.216ZM4.5 8a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Z"/>
      </svg>
    ),
    color: "primary",
    value: "500+",
    emphasis: "Active Members",
  },
  {
    title: "Open Source Contributions",
    description: "Developed and released 15 open-source projects focused on infrastructure solutions, data privacy tools, and educational platforms, with over 10,000 downloads and active community contributions.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" className="bi bi-code-square text-success" viewBox="0 0 16 16">
        <path d="M14 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h12zM2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2z"/>
        <path d="M6.854 4.646a.5.5 0 0 1 0 .708L4.207 8l2.647 2.646a.5.5 0 0 1-.708.708l-3-3a.5.5 0 0 1 0-.708l3-3a.5.5 0 0 1 .708 0zm2.292 0a.5.5 0 0 0 0 .708L11.793 8l-2.647 2.646a.5.5 0 0 0 .708.708l3-3a.5.5 0 0 0 0-.708l-3-3a.5.5 0 0 0-.708 0z"/>
      </svg>
    ),
    color: "success",
    value: "15",
    emphasis: "Open Source Projects",
  },
  {
    title: "Educational Impact",
    description: "Launched our digital literacy program reaching over 2,500 learners across 5 countries, providing accessible technology education and skills development for underserved communities.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" className="bi bi-mortarboard-fill text-warning" viewBox="0 0 16 16">
        <path d="M8.211 2.047a.5.5 0 0 0-.422 0l-7.5 3.5a.5.5 0 0 0 .025.917l7.5 3a.5.5 0 0 0 .372 0L14 7.14V13a1 1 0 0 0-1 1v2h3v-2a1 1 0 0 0-1-1V6.739l.686-.275a.5.5 0 0 0 .025-.917l-7.5-3.5Z"/>
        <path d="M4.176 9.032a.5.5 0 0 0-.656.327l-.5 1.7a.5.5 0 0 0 .294.605l4.5 1.8a.5.5 0 0 0 .372 0l4.5-1.8a.5.5 0 0 0 .294-.605l-.5-1.7a.5.5 0 0 0-.656-.327L8 10.466 4.176 9.032Z"/>
      </svg>
    ),
    color: "warning",
    value: "2,500+",
    emphasis: "Program Participants",
  },
  {
    title: "Infrastructure Innovation",
    description: "Successfully developed and implemented a decentralized data storage solution that reduces energy consumption by 40% while improving data security and accessibility for community projects and partner organizations.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" className="bi bi-diagram-3-fill text-info" viewBox="0 0 16 16">
        <path fillRule="evenodd" d="M6 3.5A1.5 1.5 0 0 1 7.5 2h1A1.5 1.5 0 0 1 10 3.5v1A1.5 1.5 0 0 1 8.5 6v1H14a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-1 0V8h-5v.5a.5.5 0 0 1-1 0V8h-5v.5a.5.5 0 0 1-1 0v-1A.5.5 0 0 1 2 7h5.5V6A1.5 1.5 0 0 1 6 4.5v-1zm-6 8A1.5 1.5 0 0 1 1.5 10h1A1.5 1.5 0 0 1 4 11.5v1A1.5 1.5 0 0 1 2.5 14h-1A1.5 1.5 0 0 1 0 12.5v-1zm6 0A1.5 1.5 0 0 1 7.5 10h1a1.5 1.5 0 0 1 1.5 1.5v1A1.5 1.5 0 0 1 8.5 14h-1A1.5 1.5 0 0 1 6 12.5v-1zm6 0a1.5 1.5 0 0 1 1.5-1.5h1a1.5 1.5 0 0 1 1.5 1.5v1a1.5 1.5 0 0 1-1.5 1.5h-1a1.5 1.5 0 0 1-1.5-1.5v-1z"/>
      </svg>
    ),
    color: "info",
    value: "40%",
    emphasis: "Energy Reduction in Data Storage",
  },
  {
    title: "Global Collaboration Network",
    description: "Established partnerships with 12 technology organizations and academic institutions across 4 continents, creating a global network for knowledge exchange, resource sharing, and collaborative problem-solving on pressing technological challenges.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" className="bi bi-globe-americas text-danger" viewBox="0 0 16 16">
        <path d="M8 0a8 8 0 1 0 0 16A8 8 0 0 0 8 0ZM2.04 4.326c.325 1.329 2.532 2.54 3.717 3.19.48.263.793.434.743.484-.08.08-.162.158-.242.234-.416.396-.787.749-.758 1.266.035.634.618.824 1.214 1.017.577.188 1.168.38 1.286.983.082.417-.075.988-.22 1.52-.215.782-.406 1.48.22 1.48 1.5-.5 3.798-3.186 4-5 .138-1.243-2-2-3.5-2.5-.478-.16-.755.081-.99.284-.172.15-.322.279-.51.216-.445-.148-2.5-2-1.5-2.5.78-.39.952-.171 *************.099.163.208.273.318.609.304.662-.132.723-.633.039-.322.081-.671.277-.867.434-.434 1.265-.791 2.028-1.12.712-.306 1.365-.587 1.579-.88A7 7 0 1 1 2.04 4.327Z"/>
      </svg>
    ),
    color: "danger",
    value: "12",
    emphasis: "Global Technology Partnerships",
  },
];

export function Achievement({ data }: { data: typeof achievements[number] }) {
  const {
    color,
    title,
    description,
    icon,
    value,
    emphasis,
  } = data;

  return (
    <div className="col-md-6 col-lg-6 mb-4">
      <div className="card h-100 border-0 shadow-sm hover-shadow rounded-4 overflow-hidden">
        <div className="card-body p-4">
          <div className="d-flex align-items-center mb-4">
            <div className={`bg-${color}-subtle rounded-circle p-3 me-3`}>
              {icon}
            </div>
            <h3 className="h4 fw-bold mb-0">{title}</h3>
          </div>
          <p className="mb-0">{description}</p>
        </div>
        <div className={`card-footer bg-${color}-subtle border-0 py-3`}>
          <div className="d-flex align-items-center">
            <div className={`fs-4 fw-bold text-${color} me-2`}>{value}</div>
            <div className={`text-${color}-emphasis`}>{emphasis}</div>
          </div>
        </div>
      </div>
    </div>
  );
}

export function AchievementsSection() {
  return (
    <section className="py-5 py-lg-6 bg-white" id="achievements">
      <div className="container">
        <div className="row justify-content-center mb-5">
          <div className="col-lg-8 text-center">
            <h2 className="display-5 fw-bold mb-4">Our Achievements</h2>
            <p className="text-secondary fs-5">
              Milestones we&apos;ve reached in our journey to build a better world through technological innovation
            </p>
          </div>
        </div>

        <div className="row g-4">
          {achievements.map((achievement, index) => (
            <Achievement key={index} data={achievement} />
          ))}
        </div>
      </div>
    </section>
  );
}
