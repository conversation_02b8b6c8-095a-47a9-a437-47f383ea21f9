import { useLocale } from "next-intl";
import { Locale } from "@/app/types";
import { rules } from "./rules";
import { DictionaryKey } from "./dictionaries";
import { ExamplesSection } from "./components/examples-section";
import { lightDictionaryColors } from "./dictionary-colors";
import styles from "../components/responsive-container.module.css";

const alphabet = [
  "Aa",
  "Āā",
  "Bb",
  "Čč",
  "Dd",
  "Ee",
  "Ëë",
  "Ff",
  "Gg",
  "Hh",
  "Ii",
  "Íí",
  "Jj",
  "Kk",
  "Ll",
  "Mm",
  "Nn",
  "Oo",
  "Ōō",
  "Pp",
  "Rr",
  "Ss",
  "Šš",
  "Tt",
  "Uu",
  "Vv",
  "Ww",
  "Yy",
  "Zz",
];

const i18n = {
  en: {
    title: "New English: A Phonetic Revolution",
    problem: {
      title: "The Challenge",
      description: "English writing and pronunciation have diverged significantly over centuries, creating numerous challenges.",
      difficulties: {
        description: "The current English language presents several difficulties",
        list: [
          "Significant difference between written and spoken forms",
          "Inconsistent pronunciation rules with many exceptions",
          "Difficult accents that vary across regions",
          "Overcomplicated spelling rules that don't match pronunciation",
          "Natural reduction in spoken language not reflected in writing",
          "Redundant elements like multiple ways to represent the same sound",
        ],
      },
    },
    idea: {
      title: "Early Attempts",
      description: {
        1: "New English: Learning from Early Challenges in Phonetic Reform",
        2: "Our journey began with a simple question: \"How might English evolve over time?\" Our first attempt at creating New English faced significant challenges, as it incorporated overly aggressive simplifications that ultimately compromised the system's integrity.",
        3: "This early version relied on online translator pronunciations rather than established dictionaries. This approach proved problematic, as it lacked the linguistic foundation necessary to establish consistent rules. The resulting translations were unstable, inconsistent across platforms, and resistant to systematic automation.",
      },
    },
    implementation: {
      title: "Our Implementation",
      description: "A systematic approach to reforming English spelling and pronunciation.",
      features: {
        description: "Our New English implementation features",
        list: [
          "Words written exactly as they are pronounced",
          "Reduction of indefinite articles to their essential forms",
          "Simplification of definite articles and demonstratives to \"da\"",
          "Consistent phonetic representation for all sounds",
          "Elimination of silent letters and redundant spellings",
          "Preservation of word recognition while improving logical consistency",
        ],
      },
    },
    dictionaries: {
      title: "Reference Dictionaries",
      description: "Our system is based on established phonetic standards from these authoritative sources",
      cambridge: {
        name: "Cambridge English Dictionary",
        description: "A comprehensive dictionary of English with phonetic transcriptions",
      },
      opendict: {
        name: "Open Dictionary",
        description: "Open-licensed dictionary data",
      },
    },
    sources: {
      title: "Research Sources",
      description: "Our approach is informed by these linguistic resources",
      englishPhonology: {
        name: "English Phonology - Wikipedia",
        description: "Overview of the sound system of the English language",
      },
      internationalPhoneticAlphabet: {
        name: "International Phonetic Alphabet",
        description: "Standardized representation of sounds in written form",
      },
      americanIpaChart: {
        name: "American IPA Chart",
        description: "Chart of the American IPA",
      },
    },
    dialects: {
      title: "Reference Dialects",
      description: "Our phonetic system prioritizes these dialects in descending order",
      generalAmerican: {
        name: "General American",
        description: "The accent of American English most commonly perceived as neutral",
      },
      standardEnglish: {
        name: "Standard English",
        description: "The standard accent of Standard English in the United Kingdom",
      },
      localDialect: {
        name: "Local Dialect",
        description: "The form of English used in the local area",
      },
    },
    alphabet: {
      title: "New English Alphabet",
    },
    rules: {
      title: "Phonetic Rules",
      description: "Each sound is consistently represented by specific letter(s)",
      phoneticSounds: "Phonetic Sounds",
      newEnglishLetter: "New English Letter(s)",
      exampleWords: "Example Words",
    },
  },

  ru: {
    title: "Новый английский: фонетическая революция",
    problem: {
      title: "Проблема",
      description: "Правописание и произношение в английском языке значительно разошлись за века, создавая множество трудностей.",
      difficulties: {
        description: "Современный английский язык создаёт ряд затруднений",
        list: [
          "Существенное расхождение между письменной и устной формами",
          "Непоследовательные правила произношения с множеством исключений",
          "Сложные акценты, сильно различающиеся по регионам",
          "Сложные правила орфографии, не соответствующие произношению",
          "Естественные сокращения в устной речи не отражаются на письме",
          "Избыточные элементы, такие как разные способы обозначения одного и того же звука",
        ],
      },
    },
    idea: {
      title: "Ранние попытки",
      description: {
        1: "Новый английский: уроки ранних трудностей фонетической реформы",
        2: "Наш путь начался с простого вопроса: «Как английский может развиваться со временем?» Первая попытка создать Новый английский столкнулась с серьёзными трудностями из-за чрезмерных упрощений, которые в итоге подорвали целостность системы.",
        3: "Ранняя версия опиралась на произношения онлайн-переводчиков вместо признанных словарей. Такой подход оказался проблематичным, поскольку не имел лингвистической базы для создания последовательных правил. В результате переводы были нестабильны, непоследовательны между платформами и не поддавались автоматизации.",
      },
    },
    implementation: {
      title: "Наша реализация",
      description: "Системный подход к реформе английской орфографии и произношения.",
      features: {
        description: "Особенности нашей реализации Нового английского",
        list: [
          "Слова пишутся точно так, как произносятся",
          "Неопределённые артикли сведены к их сути",
          "Определённые артикли и указательные слова упрощены до «da»",
          "Последовательное фонетическое обозначение всех звуков",
          "Исключение немых букв и избыточных написаний",
          "Сохранение узнаваемости слов при улучшении логической согласованности",
        ],
      },
    },
    dictionaries: {
      title: "Справочные словари",
      description: "Наша система основывается на признанных фонетических стандартах из этих авторитетных источников",
      cambridge: {
        name: "Кембриджский словарь английского языка",
        description: "Обширный словарь английского языка с фонетическими транскрипциями",
      },
      opendict: {
        name: "OpenDict",
        description: "Свободно-лицензированные данные словаря",
      },
    },
    sources: {
      title: "Исследовательские источники",
      description: "Наш подход основан на следующих лингвистических ресурсах",
      englishPhonology: {
        name: "Фонология английского языка — Википедия",
        description: "Обзор звуковой системы английского языка",
      },
      internationalPhoneticAlphabet: {
        name: "Международный фонетический алфавит",
        description: "Стандартизированное представление звуков в письменной форме",
      },
      americanIpaChart: {
        name: "IPA-таблица американского английского",
        description: "Таблица фонем американского английского",
      },
    },
    dialects: {
      title: "Базовые диалекты",
      description: "Наша фонетическая система отдаёт приоритет этим диалектам в порядке убывания",
      generalAmerican: {
        name: "Общий американский",
        description: "Акцент американского английского, воспринимаемый как нейтральный",
      },
      standardEnglish: {
        name: "Стандартный английский",
        description: "Стандартный акцент британского английского",
      },
      localDialect: {
        name: "Местный диалект",
        description: "Форма английского языка, используемая в конкретной местности",
      },
    },
    alphabet: {
      title: "Алфавит Нового английского",
    },
    rules: {
      title: "Фонетические правила",
      description: "Каждый звук последовательно обозначается определённой буквой или буквами",
      phoneticSounds: "Фонетические звуки",
      newEnglishLetter: "Буквы Нового английского",
      exampleWords: "Примеры слов",
    },
  },
};

export default function NewEnglishPage() {
  const locale = useLocale() as Locale;
  const t = i18n[locale];

  return (
    <div className="container my-5">
      <div className={styles.responsiveContainer}>
        <h1 className="mb-4">{t.title}</h1>

        {/* Problem Section */}
        <section className="mb-5">
          <h2>{t.problem.title}</h2>
          <div className="card">
            <div className="card-body">
              <p className="lead">{t.problem.description}</p>
              <p>{t.problem.difficulties.description}:</p>
              <ul>
                {t.problem.difficulties.list.map((difficulty, index) => (
                  <li key={index}>{difficulty}</li>
                ))}
              </ul>
            </div>
          </div>
        </section>

        {/* Idea Section */}
        <section className="mb-5">
          <h2>{t.idea.title}</h2>
          <div className="card">
            <div className="card-body">
              <p className="lead">{t.idea.description[1]}</p>
              <p>{t.idea.description[2]}</p>
              <p>{t.idea.description[3]}</p>
            </div>
          </div>
        </section>

        {/* Implementation Section */}
        <section className="mb-5">
          <h2>{t.implementation.title}</h2>
          <div className="card">
            <div className="card-body">
              <p className="lead">{t.implementation.description}</p>
              <p>{t.implementation.features.description}:</p>
              <ul>
                {t.implementation.features.list.map((feature, index) => (
                  <li key={index}>{feature}</li>
                ))}
              </ul>
            </div>
          </div>
        </section>

        {/* Dictionaries Section */}
        <section className="mb-5">
          <h2>{t.dictionaries.title}</h2>
          <div className="card">
            <div className="card-body">
              <p className="lead">{t.dictionaries.description}:</p>
              <ul className="list-group list-group-flush">
                <DictionaryItem
                  dictionaryKey="cambridge"
                  name={t.dictionaries.cambridge.name}
                  description={t.dictionaries.cambridge.description}
                  url="https://dictionary.cambridge.org"
                />

                <DictionaryItem
                  dictionaryKey="opendict"
                  name={t.dictionaries.opendict.name}
                  description={t.dictionaries.opendict.description}
                  url="https://open-dict-data.github.io"
                />
              </ul>
            </div>
          </div>
        </section>

        {/* Sources Section */}
        <section className="mb-5">
          <h2>{t.sources.title}</h2>
          <div className="card">
            <div className="card-body">
              <p className="lead">{t.sources.description}:</p>
              <ul className="list-group list-group-flush">
                <SourceItem
                  name={t.sources.englishPhonology.name}
                  description={t.sources.englishPhonology.description}
                  url="https://en.wikipedia.org/wiki/English_phonology"
                />

                <SourceItem
                  name={t.sources.internationalPhoneticAlphabet.name}
                  description={t.sources.internationalPhoneticAlphabet.description}
                  url="https://www.internationalphoneticalphabet.org/"
                />

                <SourceItem
                  name={t.sources.americanIpaChart.name}
                  description={t.sources.americanIpaChart.description}
                  url="https://americanipachart.com/"
                />
              </ul>
            </div>
          </div>
        </section>

        {/* Dialects Section */}
        <section className="mb-5">
          <h2>{t.dialects.title}</h2>
          <div className="card">
            <div className="card-body">
              <p className="lead">{t.dialects.description}:</p>
              <ul className="list-group list-group-flush">
                <DialectItem
                  name={t.dialects.generalAmerican.name}
                  description={t.dialects.generalAmerican.description}
                  priority={1}
                />

                <DialectItem
                  name={t.dialects.standardEnglish.name}
                  description={t.dialects.standardEnglish.description}
                  priority={2}
                />

                <DialectItem
                  name={t.dialects.localDialect.name}
                  description={t.dialects.localDialect.description}
                  priority={3}
                />
              </ul>
            </div>
          </div>
        </section>

        {/* Alphabet Section */}
        <section className="mb-5">
          <h2>{t.alphabet.title}</h2>
          <div className="card">
            <div className="card-body">
              <div className="row row-cols-2 row-cols-md-3 row-cols-lg-6 g-2">
                {alphabet.map((item, index) => (
                  <div key={index} className="col">
                    <div className="card h-100">
                      <div className="card-body text-center">
                        <h3 className="card-title">{item}</h3>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Rules Section */}
        <section className="mb-5">
          <h2>{t.rules.title}</h2>
          <div className="card">
            <div className="card-body">
              <p className="lead">{t.rules.description}:</p>
              <div className="table-responsive">
                <table className="table table-striped">
                  <thead>
                    <tr>
                      <th>{t.rules.phoneticSounds}</th>
                      <th>{t.rules.newEnglishLetter}</th>
                      <th>{t.rules.exampleWords}</th>
                    </tr>
                  </thead>
                  <tbody>
                    {rules.map((rule, index) => (
                      <tr key={index}>
                        <td>
                          {rule.sources.map((source, i) => (
                            <div key={i} className="mb-1">
                              {source.sounds.map((sound, j) => (
                                <span
                                  key={j}
                                  className="badge rounded-pill me-2 font-freemono"
                                  style={{
                                    backgroundColor: `#${lightDictionaryColors[source.dictionaryKey]}`,
                                    color: "#000000",
                                    padding: "6px 12px",
                                    marginBottom: "6px",
                                    fontSize: "1.2rem",
                                    fontWeight: "normal",
                                  }}
                                >
                                  {sound}
                                </span>
                              ))}
                            </div>
                          ))}
                        </td>
                        <td>
                          <strong style={{ fontSize: "1.5rem", display: "inline-block", padding: "4px 0" }}>{rule.mapping}</strong>
                        </td>
                        <td>
                          {rule.examples.map((example, i) => (
                            <span key={i}>
                              {i > 0 && ", "}
                              <HighlightedExample example={example} />
                            </span>
                          ))}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </section>

        {/* Examples Section */}
        <ExamplesSection locale={locale} />
      </div>
    </div>
  );
}

function DictionaryItem({
  dictionaryKey,
  name,
  description,
  url,
}: {
  dictionaryKey: DictionaryKey;
  name: string;
  description: string;
  url: string;
}) {
  return (
    <li
      className="list-group-item"
      style={{ borderLeft: `4px solid #${lightDictionaryColors[dictionaryKey]}` }}
    >
      <strong>
        <a
          href={url}
          target="_blank"
          rel="noopener noreferrer"
        >
          {name}
        </a>
      </strong>
      <p className="mb-0 text-muted">{description}</p>
    </li>
  );
}

function SourceItem({
  name,
  description,
  url,
}: {
  name: string,
  description: string,
  url: string,
}) {
  return (
    <li className="list-group-item">
      <strong>
        <a
          href={url}
          target="_blank"
          rel="noopener noreferrer"
        >
          {name}
        </a>
      </strong>
      <p className="mb-0 text-muted">{description}</p>
    </li>
  );
}

function DialectItem({
  name,
  description,
  priority,
}: {
  name: string,
  description: string,
  priority: number,
}) {
  return (
    <li className="list-group-item">
      <div className="d-flex align-items-center">
        <span className="badge bg-primary me-2">{priority}</span>
        <div>
          <strong>{name}</strong>
          <p className="mb-0 text-muted">{description}</p>
        </div>
      </div>
    </li>
  );
}

function HighlightedExample({ example }: { example: string }) {
  const [before, highlight, after] = example.split("*") as [string, string, string];

  return (
    <>
      <span>{before}</span>
      <strong style={{ color: "#d63384" }}>{highlight}</strong>
      <span>{after}</span>
    </>
  );
}
