import { Prisma } from "@prisma/client";
import { BaseService } from "src/common/base-service";
import { PrismaService } from "src/prisma/prisma.service";
import { CurrentUser } from "src/auth/types";
import { <PERSON>od<PERSON><PERSON><PERSON> } from "src/zod";
import { MinioService, FileInfo } from "src/minio/minio.service";
export type CreateUser = {
    referrerId: string | null;
    email: string;
};
export declare class UserService extends BaseService {
    private readonly prisma;
    private readonly minioService;
    constructor(prisma: PrismaService, minioService: MinioService);
    canGet(id: string, user: CurrentUser): Promise<true>;
    canChange(id: string, user: CurrentUser): Promise<true>;
    getByEmail(email: string): Promise<{
        id: string;
        referrerId: string | null;
        email: string;
        role: import(".prisma/client").$Enums.UserRole;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    } | null>;
    create(data: CreateUser): Promise<{
        id: string;
        referrerId: string | null;
        email: string;
        role: import(".prisma/client").$Enums.UserRole;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    check(ids: string[]): Promise<{
        id: string;
        referrerId: string | null;
        email: string;
        role: import(".prisma/client").$Enums.UserRole;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }[]>;
    getOne(id: string): Promise<({
        images: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        }[];
        name: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
        description: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
        titles: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            ownerId: string | null;
            isActive: boolean;
            color: string | null;
        }[];
    } & {
        id: string;
        referrerId: string | null;
        email: string;
        role: import(".prisma/client").$Enums.UserRole;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }) | null>;
    getOneOrThrow(id: string): Promise<{
        images: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        }[];
        name: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
        description: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
        titles: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            ownerId: string | null;
            isActive: boolean;
            color: string | null;
        }[];
    } & {
        id: string;
        referrerId: string | null;
        email: string;
        role: import(".prisma/client").$Enums.UserRole;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    getMany(where: Prisma.UserWhereInput, pagination?: {
        page: number;
        size: number;
    }): Promise<({
        images: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        }[];
        name: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
        description: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
        titles: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            ownerId: string | null;
            isActive: boolean;
            color: string | null;
        }[];
    } & {
        id: string;
        referrerId: string | null;
        email: string;
        role: import(".prisma/client").$Enums.UserRole;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    })[]>;
    createOne(data: Prisma.UserCreateInput): Promise<{
        id: string;
        referrerId: string | null;
        email: string;
        role: import(".prisma/client").$Enums.UserRole;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    createMany(data: Prisma.UserCreateManyInput[]): Promise<Prisma.BatchPayload>;
    updateOne(id: string, data: Prisma.UserUpdateInput): Promise<{
        id: string;
        referrerId: string | null;
        email: string;
        role: import(".prisma/client").$Enums.UserRole;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    update(id: string, data: Partial<{
        name: ZodHelper.Localization[];
        description: ZodHelper.Localization[];
    }>, user: CurrentUser): Promise<{
        images: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        }[];
        name: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
        description: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
    } & {
        id: string;
        referrerId: string | null;
        email: string;
        role: import(".prisma/client").$Enums.UserRole;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    uploadUserImage(userId: string, file: FileInfo): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        url: string;
    }>;
    updateMany(where: Prisma.UserWhereInput, data: Prisma.UserUpdateInput): Promise<Prisma.BatchPayload>;
    softDeleteOne(id: string): Promise<{
        id: string;
        referrerId: string | null;
        email: string;
        role: import(".prisma/client").$Enums.UserRole;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    softDeleteMany(where: Prisma.UserWhereInput): Promise<Prisma.BatchPayload>;
    deleteOne(id: string): Promise<{
        id: string;
        referrerId: string | null;
        email: string;
        role: import(".prisma/client").$Enums.UserRole;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    deleteMany(where: Prisma.UserWhereInput): Promise<Prisma.BatchPayload>;
}
