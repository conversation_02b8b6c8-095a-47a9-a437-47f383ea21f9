export type Locale = "en" | "ru";

export type Localization = {
  locale: Locale;
  value: string;
};

export function findLocalization(
  userLocales: readonly string[],
  localizations: Localization[],
): string | null {
  const locales = [...userLocales, "en", "ru"];

  const matchingUserLocale = locales.find((userLocale) =>
    localizations.some((localization) => localization.locale === userLocale),
  );

  const localization = matchingUserLocale
    ? localizations.find((localization) => localization.locale === matchingUserLocale)
    : null;

  return (localization ?? localizations[0])?.value ?? null;
}

export function findLocalizationForUserLocales(
  localizations: Localization[],
): string | null {
  const locales = typeof window !== "undefined" ? [...window.navigator.languages] : [];
  const preferredLocale = localStorage.getItem("PREFERRED_LOCALE");

  if (preferredLocale) {
    locales.unshift(preferredLocale);
  }

  return findLocalization(locales, localizations);
}
