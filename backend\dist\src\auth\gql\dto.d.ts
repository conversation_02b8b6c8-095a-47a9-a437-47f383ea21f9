import { z } from "zod";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "src/zod";
export declare const currentUserTypename = "CurrentUser";
export type CurrentUser = ZodHelper.Infer<typeof CurrentUser>;
export declare const CurrentUser: z.ZodObject<{
    __typename: z.<PERSON>ef<PERSON><z.ZodLiteral<"CurrentUser">>;
    id: z.ZodString;
    email: z.ZodString;
    role: z.ZodNativeEnum<{
        admin: "admin";
        moderator: "moderator";
        user: "user";
    }>;
}, "strip", z.ZodTypeAny, {
    id: string;
    email: string;
    role: "user" | "admin" | "moderator";
    __typename: "CurrentUser";
}, {
    id: string;
    email: string;
    role: "user" | "admin" | "moderator";
    __typename?: "CurrentUser" | undefined;
}>;
export declare const meTypename = "Me";
export type Me = ZodHelper.Infer<typeof Me>;
export declare const Me: z.ZodObject<{
    __typename: z.<PERSON>odDefault<z.ZodLiteral<"Me">>;
    id: z.ZodString;
    email: z.ZodString;
    role: z.ZodNative<PERSON>num<{
        admin: "admin";
        moderator: "moderator";
        user: "user";
    }>;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    joinedAt: z.ZodEffects<z.ZodDate, string, Date>;
}, "strip", z.ZodTypeAny, {
    id: string;
    email: string;
    role: "user" | "admin" | "moderator";
    name: {
        locale: "en" | "ru";
        value: string;
    }[];
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    joinedAt: string;
    __typename: "Me";
}, {
    id: string;
    email: string;
    role: "user" | "admin" | "moderator";
    name: {
        locale: "en" | "ru";
        value: string;
    }[];
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    joinedAt: Date;
    __typename?: "Me" | undefined;
}>;
