/* @font-face {
  font-family: 'Iosevka';
  src: url('/fonts/iosevka-regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
} */

@font-face {
  font-family: 'FreeMono';
  src: url('/fonts/FreeMonospaced.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

/* @font-face {
  font-family: "AverageMono";
  src: url('/fonts/AverageMono.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
} */

/* .font-iosevka {
  font-family: 'Iosevka', monospace;
} */

.font-freemono {
  font-family: 'FreeMono', monospace;
}

/* .font-average-mono {
  font-family: 'AverageMono', monospace;
} */
