"use client";

import { useEffect, useState } from "react";
import { Modal, Button, Carousel } from "react-bootstrap";
import styles from "./image-gallery-modal.module.css";
import { Locale } from "@/app/types";
import { useLocale } from "next-intl";

interface ImageGalleryModalProps {
  show: boolean;
  onHide: () => void;
  images: { id: string; url: string }[];
  activeIndex?: number;
}

const i18n = {
  en: {
    title: "Profile Images",
    noImages: "No images available",
    close: "Close",
  },

  ru: {
    title: "Изображения профиля",
    noImages: "Нет изображений",
    close: "Закрыть",
  },
};

export function ImageGalleryModal({ show, onHide, images, activeIndex = 0 }: ImageGalleryModalProps) {
  const locale = useLocale() as Locale;
  const t = i18n[locale];

  const [index, setIndex] = useState(activeIndex);

  const handleSelect = (selectedIndex: number) => {
    setIndex(selectedIndex);
  };

  useEffect(() => {
    if (show && index !== activeIndex) {
      setIndex(activeIndex);
    }
  }, [show]);

  return (
    <Modal show={show} onHide={onHide} centered size="lg" className={styles.galleryModal}>
      <Modal.Header closeButton>
        <Modal.Title>{t.title}</Modal.Title>
      </Modal.Header>
      <Modal.Body className="p-0">
        {images.length > 0 ? (
          <Carousel
            activeIndex={index}
            onSelect={handleSelect}
            interval={null} // Disable auto-switching
            indicators={images.length > 1}
            controls={images.length > 1}
            className={styles.carousel}
          >
            {images.map((image, idx) => (
              <Carousel.Item key={image.id} className={styles.carouselItem}>
                <div className={styles.imageContainer}>
                  <img
                    src={`/api/images/${image.url}`}
                    alt={`Profile image ${idx + 1}`}
                    sizes="(max-width: 768px) 100vw, 800px"
                    className={styles.image}
                  />
                </div>
              </Carousel.Item>
            ))}
          </Carousel>
        ) : (
          <div className="text-center py-5">
            <p className="mb-0">{t.noImages}</p>
          </div>
        )}
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={onHide}>
          {t.close}
        </Button>
      </Modal.Footer>
    </Modal>
  );
}
