"use client";

import { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>ge } from "react-bootstrap";
import { useParams } from "next/navigation";
import { useHttpRequest } from "@/app/hooks/use-http-request";
import { findLocalizationForUserLocales, Localization } from "@/utils/find-localization";
import { DetailImageCarousel } from "./detail-image-carousel";
import { EditPostModal } from "./edit-post-modal";
import { formatDate } from "@/utils/format-date";
import { useLocale } from "next-intl";
import { Locale } from "@/app/types";

interface PostImage {
  id: string;
  url: string;
  source: string;
}

interface Post {
  id: string;
  title: Localization[];
  description: Localization[];
  status: "draft" | "published" | "archived";
  publishedAt: string | null;
  createdAt: string;
  updatedAt: string;
  images?: PostImage[];
}

interface CurrentUser {
  id: string;
  email: string;
  role: "user" | "admin";
}

const i18n = {
  en: {
    loading: "Loading...",
    failedToFetchPost: "Failed to fetch post",
    failedToFetchUpdatedPost: "Failed to fetch updated post",
    errorOccured: "An error occurred while fetching data",
    errorRefreshingPostData: "Error refreshing post data",
    postNotFound: "Post not found",
    postDetails: "Post Details",
    edit: "Edit",
    published: "Published",
    created: "Created",
    noDescription: "No description available",
    lastUpdated: "Last Updated",
    dateFormatLocale: "en-US",
    status: {
      title: "Status",
      draft: "Draft",
      published: "Published",
      archived: "Archived",
    },
  },

  ru: {
    loading: "Загрузка...",
    failedToFetchPost: "Не удалось загрузить публикацию",
    failedToFetchUpdatedPost: "Не удалось загрузить обновленную публикацию",
    errorOccured: "Произошла ошибка при загрузке данных",
    errorRefreshingPostData: "Ошибка при обновлении данных публикации",
    postNotFound: "Публикация не найдена",
    postDetails: "Детали публикации",
    edit: "Редактировать",
    published: "Опубликовано",
    created: "Создано",
    noDescription: "Нет описания",
    lastUpdated: "Последнее обновление",
    status: {
      title: "Статус",
      draft: "Черновик",
      published: "Опубликовано",
      archived: "Архивировано",
    },
    dateFormatLocale: "ru-RU",
  },
};

export default function PostDetailPage() {
  const locale = useLocale() as Locale;
  const t = i18n[locale];

  const params = useParams();
  const postId = params.id as string;

  const [post, setPost] = useState<Post | null>(null);
  const [currentUser, setCurrentUser] = useState<CurrentUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);

  const httpRequest = useHttpRequest();

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch current user data
        const userResponse = await httpRequest("/api/auth/me");
        if (userResponse.ok) {
          const userData = await userResponse.json();
          setCurrentUser(userData);
        }

        // Fetch post details
        const postResponse = await httpRequest(`/api/post/${postId}`);

        if (!postResponse.ok) {
          throw new Error(`${t.failedToFetchPost}: ${postResponse.statusText}`);
        }

        const postData: Post = await postResponse.json();
        setPost(postData);
      } catch (err) {
        setError(err instanceof Error ? err.message : t.errorOccured);
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    if (postId) {
      fetchData();
    }
  }, [postId, httpRequest]);

  const handleEditClick = () => {
    setShowEditModal(true);
  };

  const handleEditModalClose = () => {
    setShowEditModal(false);
  };

  const handlePostUpdated = async () => {
    try {
      // Fetch updated post details
      const postResponse = await httpRequest(`/api/post/${postId}`);

      if (!postResponse.ok) {
        throw new Error(`${t.failedToFetchUpdatedPost}: ${postResponse.statusText}`);
      }

      const postData: Post = await postResponse.json();
      setPost(postData);
    } catch (err) {
      console.error(`${t.errorRefreshingPostData}:`, err);
    }
  };

  if (loading) {
    return (
      <Container className="py-5 text-center">
        <Spinner animation="border" role="status" className="me-2" />
        <span>{t.loading}</span>
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="py-5">
        <Alert variant="danger">
          {error}
        </Alert>
      </Container>
    );
  }

  if (!post) {
    return (
      <Container className="py-5">
        <Alert variant="warning">
          {t.postNotFound}
        </Alert>
      </Container>
    );
  }

  const postTitle = findLocalizationForUserLocales(post.title);
  const postDescription = findLocalizationForUserLocales(post.description);
  const isAdmin = currentUser?.role === "admin";
  const displayDate = post.publishedAt ? new Date(post.publishedAt) : new Date(post.createdAt);

  return (
    <Container className="py-4">
      <Row>
        <Col lg={8}>
          {/* Image Carousel */}
          <DetailImageCarousel images={post.images || []} />

          {/* Post Information */}
          <div className="mb-4">
            <div className="d-flex justify-content-between align-items-center mb-3">
              <h2 className="mb-0">{postTitle}</h2>
              {post.status !== "published" && (
                <Badge bg="secondary">{post.status}</Badge>
              )}
            </div>
            <p className="text-muted mb-2">
              {post.publishedAt ? t.published : t.created} {formatDate(displayDate, t.dateFormatLocale)}
            </p>
            <div className="lead mt-3">{postDescription || t.noDescription}</div>
          </div>
        </Col>

        <Col lg={4}>
          <Card className="shadow-sm mb-4">
            <Card.Body>
              <div className="d-flex justify-content-between align-items-center mb-2">
                <Card.Title className="mb-0">{t.postDetails}</Card.Title>
                {isAdmin && (
                  <Button variant="outline-primary" size="sm" onClick={handleEditClick}>
                    {t.edit}
                  </Button>
                )}
              </div>
              <hr />
              <div className="mb-2">
                <strong>{t.status.title}:</strong> {t.status[post.status]}
              </div>
              <div className="mb-2">
                <strong>{t.created}:</strong> {formatDate(new Date(post.createdAt), t.dateFormatLocale)}
              </div>
              {post.publishedAt && (
                <div className="mb-2">
                  <strong>{t.published}:</strong> {formatDate(new Date(post.publishedAt), t.dateFormatLocale)}
                </div>
              )}
              <div className="mb-2">
                <strong>{t.lastUpdated}:</strong> {formatDate(new Date(post.updatedAt), t.dateFormatLocale)}
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Edit Post Modal */}
      <EditPostModal
        show={showEditModal}
        onHide={handleEditModalClose}
        postData={post}
        onPostUpdated={handlePostUpdated}
      />
    </Container>
  );
}
