"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommuneModule = void 0;
const common_1 = require("@nestjs/common");
const commune_service_1 = require("./commune.service");
const commune_controller_1 = require("./http/commune.controller");
const commune_resolver_1 = require("./gql/commune.resolver");
const commune_member_service_1 = require("./commune-member.service");
const user_module_1 = require("../user/user.module");
const minio_module_1 = require("../minio/minio.module");
let CommuneModule = class CommuneModule {
};
exports.CommuneModule = CommuneModule;
exports.CommuneModule = CommuneModule = __decorate([
    (0, common_1.Module)({
        imports: [user_module_1.UserModule, minio_module_1.MinioModule],
        controllers: [commune_controller_1.CommuneController],
        providers: [commune_resolver_1.CommuneResolver, commune_service_1.CommuneService, commune_member_service_1.CommuneMemberService],
        exports: [commune_service_1.CommuneService, commune_member_service_1.CommuneMemberService],
    })
], CommuneModule);
//# sourceMappingURL=commune.module.js.map