"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeleteComment = exports.AnonimifyComment = exports.CommentRating = exports.UpdateCommentRating = exports.UpdateComment = exports.CreateComment = exports.GetCommentsResponse = exports.Comment = exports.GetCommentsEntityType = exports.DeletePost = exports.PostUsefulness = exports.UpdatePostUsefulness = exports.PostRating = exports.UpdatePostRating = exports.UpdatePost = exports.CreatePost = exports.GetPostsResponse = exports.Post = exports.GetPosts = exports.EntityType = void 0;
const prisma = require("@prisma/client");
const zod_1 = require("../../zod");
const postUsefulness = zod_1.z.number().int().min(0).max(10);
exports.EntityType = zod_1.z.enum(["post", "comment"]);
exports.GetPosts = zod_1.z.object({
    pagination: zod_1.z.object({
        page: zod_1.ZodHelper.pagination.page,
        size: zod_1.ZodHelper.pagination.size,
    }),
});
exports.Post = zod_1.z.object({
    id: zod_1.ZodHelper.Uuid,
    author: zod_1.z.object({
        id: zod_1.ZodHelper.Uuid,
        name: zod_1.ZodHelper.Localizations,
        avatar: zod_1.z.string().nullable(),
    }),
    rating: zod_1.z.object({
        likes: zod_1.z.number().int().nonnegative(),
        dislikes: zod_1.z.number().int().nonnegative(),
        status: zod_1.z.nativeEnum(prisma.ReactorRatingType).nullable(),
    }),
    usefulness: zod_1.z.object({
        value: postUsefulness.nullable(),
        count: zod_1.z.number().int().nonnegative(),
        totalValue: zod_1.z.number().min(0).max(10).nullable(),
    }),
    title: zod_1.ZodHelper.Localizations,
    body: zod_1.ZodHelper.Localizations,
    tags: zod_1.z.array(zod_1.ZodHelper.Uuid),
    createdAt: zod_1.z.date(),
    updatedAt: zod_1.z.date(),
});
exports.GetPostsResponse = zod_1.z.object({
    items: zod_1.z.array(exports.Post),
    total: zod_1.z.number().int().nonnegative(),
});
exports.CreatePost = zod_1.z.object({
    title: zod_1.ZodHelper.Localizations,
    body: zod_1.ZodHelper.Localizations,
    tags: zod_1.z.array(zod_1.ZodHelper.Uuid),
});
exports.UpdatePost = zod_1.z
    .object({
    title: zod_1.ZodHelper.Localizations,
    body: zod_1.ZodHelper.Localizations,
    tags: zod_1.z.array(zod_1.ZodHelper.Uuid),
})
    .partial();
exports.UpdatePostRating = zod_1.z.object({
    type: zod_1.z.nativeEnum(prisma.ReactorRatingType),
});
exports.PostRating = zod_1.z.object({
    likes: zod_1.z.number().int().nonnegative(),
    dislikes: zod_1.z.number().int().nonnegative(),
    status: zod_1.z.nativeEnum(prisma.ReactorRatingType).nullable(),
});
exports.UpdatePostUsefulness = zod_1.z.object({
    value: postUsefulness.nullable(),
});
exports.PostUsefulness = zod_1.z.object({
    value: postUsefulness.nullable(),
    count: zod_1.z.number().int().nonnegative(),
    totalValue: zod_1.z.number().min(0).max(10).nullable(),
});
exports.DeletePost = zod_1.z.object({
    reason: zod_1.z.string().nullable(),
});
exports.GetCommentsEntityType = zod_1.z.enum(["post"]);
exports.Comment = zod_1.z.object({
    id: zod_1.ZodHelper.Uuid,
    path: zod_1.z.string().nonempty(),
    author: zod_1.z
        .object({
        id: zod_1.ZodHelper.Uuid,
        name: zod_1.ZodHelper.Localizations,
        avatar: zod_1.z.string().nullable(),
    })
        .nullable(),
    isAnonymous: zod_1.z.boolean(),
    anonimityReason: zod_1.z.string().nullable(),
    rating: zod_1.z.object({
        likes: zod_1.z.number().int().nonnegative(),
        dislikes: zod_1.z.number().int().nonnegative(),
        status: zod_1.z.nativeEnum(prisma.ReactorRatingType).nullable(),
    }),
    body: zod_1.ZodHelper.Localizations.nullable(),
    childrenCount: zod_1.z.number().int().nonnegative(),
    deleteReason: zod_1.z.string().nonempty().nullable(),
    createdAt: zod_1.z.date(),
    updatedAt: zod_1.z.date(),
    deletedAt: zod_1.z.date().nullable(),
});
exports.GetCommentsResponse = zod_1.z.object({
    items: zod_1.z.array(exports.Comment),
    total: zod_1.z.number().int().nonnegative(),
});
exports.CreateComment = zod_1.z.object({
    entityType: exports.EntityType,
    entityId: zod_1.ZodHelper.Uuid,
    body: zod_1.ZodHelper.Localizations,
});
exports.UpdateComment = zod_1.z
    .object({
    body: zod_1.ZodHelper.Localizations,
})
    .partial();
exports.UpdateCommentRating = zod_1.z.object({
    type: zod_1.z.nativeEnum(prisma.ReactorRatingType),
});
exports.CommentRating = zod_1.z.object({
    likes: zod_1.z.number().int().nonnegative(),
    dislikes: zod_1.z.number().int().nonnegative(),
    status: zod_1.z.nativeEnum(prisma.ReactorRatingType).nullable(),
});
exports.AnonimifyComment = zod_1.z.object({
    reason: zod_1.z.string().nullable(),
});
exports.DeleteComment = zod_1.z.object({
    reason: zod_1.z.string().nullable(),
});
//# sourceMappingURL=dto.js.map