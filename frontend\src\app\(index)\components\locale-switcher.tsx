"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { getCookie, setCookie, deleteCookie } from "cookies-next";
import { Locale } from "@/app/types";

type Props = {
  locale: Locale;
}

export function LocaleSwitcher({ locale }: Props) {
  const router = useRouter();
  const [currentLocale, setCurrentLocale] = useState<Locale | "auto">(locale);
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Read the current locale from cookie on component mount
  useEffect(() => {
    const preferredLocale = localStorage.getItem("PREFERRED_LOCALE");

    if (preferredLocale === "en" || preferredLocale === "ru") {
      setCurrentLocale(preferredLocale as Locale);
    } else {
      setCurrentLocale("auto");
    }
  }, []);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleLocaleChange = (locale: Locale | "auto") => {
    if (locale === "auto") {
      // Remove the cookie to use browser's language
      deleteCookie("PREFERRED_LOCALE");
      localStorage.removeItem("PREFERRED_LOCALE");
      setCurrentLocale("auto");
    } else {
      // Set the preferred locale cookie
      setCookie("PREFERRED_LOCALE", locale, {
        // maxAge: 60 * 60 * 24 * 365, // Valid for 1 year (in seconds)
        path: "/",
        sameSite: "lax",
      });
      localStorage.setItem("PREFERRED_LOCALE", locale);
      setCurrentLocale(locale);
    }

    setIsOpen(false);

    // Refresh the page to apply the new locale
    router.refresh();
  };

  // Get display text for the current locale
  const getLocaleDisplayText = (locale: Locale | "auto"): string => {
    switch (locale) {
      case "en": return "English";
      case "ru": return "Русский";
      case "auto": return "Auto";
      default: return "Auto";
    }
  };

  return (
    <div className="dropdown mx-2" ref={dropdownRef}>
      <button
        className="btn btn-outline-secondary btn-sm dropdown-toggle d-flex align-items-center justify-content-center"
        style={{ width: "110px", minWidth: "110px" }} /* Fixed width to prevent layout shifts */
        type="button"
        id="locale-dropdown"
        onClick={() => setIsOpen(!isOpen)}
        aria-expanded={isOpen}
      >
        <i className="bi bi-globe me-1"></i>
        {getLocaleDisplayText(currentLocale)}
      </button>

      <ul className={`dropdown-menu ${isOpen ? "show" : ""}`} aria-labelledby="locale-dropdown">
        <li>
          <button
            className={`dropdown-item ${currentLocale === "en" ? "active" : ""}`}
            onClick={() => handleLocaleChange("en")}
          >
            <i className="bi bi-translate me-2"></i>
            English
          </button>
        </li>
        <li>
          <button
            className={`dropdown-item ${currentLocale === "ru" ? "active" : ""}`}
            onClick={() => handleLocaleChange("ru")}
          >
            <i className="bi bi-translate me-2"></i>
            Русский
          </button>
        </li>
        <li>
          <button
            className={`dropdown-item ${currentLocale === "auto" ? "active" : ""}`}
            onClick={() => handleLocaleChange("auto")}
          >
            <i className="bi bi-globe me-2"></i>
            Auto
          </button>
        </li>
      </ul>
    </div>
  );
}
