{"version": 3, "file": "generate-graphql.js", "sourceRoot": "", "sources": ["../../src/generate-graphql.ts"], "names": [], "mappings": ";;AAAA,6CAA4D;AAC5D,0CAA6B;AAC7B,+BAAwB;AAExB,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;AAC/C,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;AAE/C,MAAM,kBAAkB,GAAG,IAAI,mCAAyB,EAAE,CAAC;AAE3D,MAAM,gBAAgB,GAAG;;;;;;;;;;;;;;;;;;CAkBxB,CAAC;AAEF,SAAS,oBAAoB,CAAC,QAAgB;IAO1C,OAAO;uBACY,QAAQ,wCAAwC,QAAQ;KAC1E,CAAC;AAaN,CAAC;AAED,kBAAkB;KACb,QAAQ,CAAC;IACN,SAAS,EAAE,CAAC,oBAAoB,CAAC;IAEjC,IAAI,EAAE,oBAAoB;IAE1B,QAAQ,EAAE,WAAW;IACrB,uBAAuB,EAAE;QACrB,QAAQ,EAAE,wBAAwB;KACrC;IACD,gBAAgB;IAChB,YAAY,EAAE,IAAI;IAClB,iBAAiB,EAAE,IAAI;IACvB,KAAK;IACL,KAAK;CACR,CAAC;KACD,IAAI,CAAC,KAAK,IAAI,EAAE;IACb,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,kBAAkB,CAAC,CAAC;IAC9D,MAAM,WAAW,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;IAExD,MAAM,UAAU,GAAG;QACf,GAAG,WAAW,CAAC,QAAQ,CAAC,4BAA4B,CAAC;KACxD,CAAC;IAEF,MAAM,eAAe,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;QAC7C,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAE1B,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,yBAAyB,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,CAAC;QAED,OAAO,oBAAoB,CAAC,QAAQ,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC;IAEH,MAAM,kBAAkB,GAAG;QACvB,WAAW,CAAC,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC;QACjC,GAAG,eAAe;KACrB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEb,MAAM,kBAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAC;AACrD,CAAC,CAAC,CAAC"}