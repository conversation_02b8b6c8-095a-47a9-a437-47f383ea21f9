.galleryModal :global(.modal-content) {
  background-color: #f8f9fa;
  border-radius: 0.5rem;
  overflow: hidden;
}

.carousel {
  width: 100%;
  background-color: #000;
}

.carouselItem {
  height: 500px;
  position: relative;
  background-color: #000;
}

.imageContainer {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image {
  object-fit: contain;
  max-width: 100%;
  max-height: 100%;
}

/* Semi-transparent navigation arrows */
.carousel :global(.carousel-control-prev),
.carousel :global(.carousel-control-next) {
  background-color: rgba(0, 0, 0, 0.3);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  top: 50%;
  transform: translateY(-50%);
  margin: 0 15px;
}

/* Indicator dots */
.carousel :global(.carousel-indicators) {
  margin-bottom: 0.5rem;
}

.carousel :global(.carousel-indicators button) {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin: 0 5px;
  background-color: rgba(255, 255, 255, 0.5);
}

.carousel :global(.carousel-indicators button.active) {
  background-color: #fff;
}

@media (max-width: 768px) {
  .carouselItem {
    height: 300px;
  }
}
