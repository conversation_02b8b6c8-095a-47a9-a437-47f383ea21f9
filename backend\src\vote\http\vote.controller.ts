import { Body, Controller, Put } from "@nestjs/common";
import { ZodPipe } from "src/zod";
import { CurrentUser } from "src/auth/types";
import { HttpCurrentUser } from "src/auth/http/current-user.decorator";
import { VoteService } from "../vote.service";
import * as Dto from "./dto";

@Controller("vote")
export class VoteController {
    constructor(private readonly voteService: VoteService) {}

    @Put()
    async createVote(
        @Body(new ZodPipe(Dto.CreateVote)) body: Dto.CreateVote,
        @HttpCurrentUser() user: CurrentUser,
    ): Promise<true> {
        await this.voteService.create({
            userId: user.id,
            votingId: body.votingId,
            optionId: body.optionId,
        });

        return true;
    }
}
