import Link from "next/link";
import Image from "next/image";
import styles from "./header.module.css";
import { Locale } from "@/app/types";
import { LocaleSwitcher } from "../locale-switcher";

const i18n = {
  ru: {
    theLaw: "Право",
    rules: "Правила",
    newEnglish: "Новый английский",
    newCalendar: "Новый календарь",
    news: "Новости",
    users: "Пользователи",
    communes: "Коммуны",
    profile: "Профиль",
  },

  en: {
    theLaw: "The Law",
    rules: "Rules",
    newEnglish: "New English",
    newCalendar: "New Calendar",
    news: "News",
    users: "Users",
    communes: "Communes",
    profile: "Profile",
  },
};

const navGaps: Record<Locale, string> = {
  ru: "mx-1",
  en: "mx-2",
};

type Props = {
  locale: Locale;
}

export function Header({ locale }: Props) {
  const t = i18n[locale];

  const navGap = navGaps[locale];

  return (
    <nav className={`navbar navbar-expand-lg mb-2 py-2 sticky-top ${styles.compactNavbar}`}>
      <div className="container px-5">
        <Link href="/" className="navbar-brand py-0 ps-5">
          <Image
            src="/images/full-v3-transparent.svg"
            alt="Site Logo"
            height={60}
            width={60}
            style={{ width: "auto" }}
            priority
          />
        </Link>
        <button
          className="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
          aria-controls="navbarNav"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <span className="navbar-toggler-icon"></span>
        </button>
        <div className="collapse navbar-collapse" id="navbarNav">
          <ul className="navbar-nav mx-auto">
            <li className={`nav-item ${navGap} text-nowrap`}>
              <Link href="/the-law" className="nav-link">{t.theLaw}</Link>
            </li>
            <li className={`nav-item ${navGap} text-nowrap`}>
              <Link href="/rules" className="nav-link">{t.rules}</Link>
            </li>
            <li className={`nav-item ${navGap} text-nowrap`}>
              <Link href="/new-english" className="nav-link">{t.newEnglish}</Link>
            </li>
            <li className={`nav-item ${navGap} text-nowrap`}>
              <Link href="/new-calendar" className="nav-link">{t.newCalendar}</Link>
            </li>
            <li className={`nav-item ${navGap} text-nowrap`}>
              <Link href="/news" className="nav-link">{t.news}</Link>
            </li>
            <li className={`nav-item ${navGap} text-nowrap`}>
              <Link href="/users" className="nav-link">{t.users}</Link>
            </li>
            <li className={`nav-item ${navGap} text-nowrap`}>
              <Link href="/communes" className="nav-link">{t.communes}</Link>
            </li>
          </ul>
          <ul className="navbar-nav">
            <li className="nav-item">
              <Link href="/account" className="btn btn-primary btn-sm">
                {t.profile}
              </Link>
            </li>
            <li className="nav-item">
              <LocaleSwitcher locale={locale} />
            </li>
          </ul>
        </div>
      </div>
    </nav>
  );
}
