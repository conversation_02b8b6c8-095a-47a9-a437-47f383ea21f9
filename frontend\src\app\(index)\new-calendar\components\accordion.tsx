"use client";

import { useState, ReactNode } from "react";

interface AccordionItemProps {
  title: ReactNode;
  children: ReactNode;
  isOpen?: boolean;
  onToggle?: () => void;
}

export function AccordionItem({ title, children, isOpen = false, onToggle }: AccordionItemProps) {
  const [isExpanded, setIsExpanded] = useState(isOpen);

  const handleToggle = () => {
    const newState = !isExpanded;
    setIsExpanded(newState);
    if (onToggle) {
      onToggle();
    }
  };

  return (
    <div className="card mb-2">
      <div
        className="card-header d-flex justify-content-between align-items-center"
        onClick={handleToggle}
        style={{ cursor: "pointer" }}
      >
        <div>{title}</div>
        <button
          className="btn btn-link"
          type="button"
          aria-expanded={isExpanded}
        >
          {isExpanded ? "−" : "+"}
        </button>
      </div>
      {isExpanded && (
        <div className="card-body">
          {children}
        </div>
      )}
    </div>
  );
}

interface AccordionProps {
  children: ReactNode;
}

export function Accordion({ children }: AccordionProps) {
  return <div className="accordion">{children}</div>;
}
