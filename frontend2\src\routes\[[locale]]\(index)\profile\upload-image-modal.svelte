<script lang="ts">
  import { fetchWithAuth, type Locale } from "$lib";
  import { Modal } from "$lib/components";

  interface Props {
    locale: Locale;
    show: boolean;
    onHide: () => void;
    userId: string;
    onImageUploaded: () => void;
  }

  // Constants for file upload validation
  const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
  const ALLOWED_FILE_TYPES = ["image/jpeg", "image/png", "image/webp"];

  const MAX_FILE_SIZE_MB = MAX_FILE_SIZE / (1024 * 1024);

  const i18n = {
    en: {
      uploadImage: "Upload Profile Image",
      upload: "Upload",
      cancel: "Cancel",
      uploading: "Uploading...",
      imageUploadedSuccessfully: "Image uploaded successfully!",
      pleaseSelectImage: "Please select an image to upload",
      invalidFileTypeError: "Invalid file type. Please upload a JPG, PNG, or WebP image.",
      fileTooLarge: `File is too large. Maximum size is ${MAX_FILE_SIZE_MB}MB.`,
      failedToUploadImage: "Failed to upload image",
      errorOccurred: "An error occurred while uploading the image",
      uploadImageMaxSize: `Upload an image (JPG, PNG, WebP), max ${MAX_FILE_SIZE_MB}MB.`,
    },

    ru: {
      uploadImage: "Загрузить изображение профиля",
      upload: "Загрузить",
      cancel: "Отменить",
      uploading: "Загрузка...",
      imageUploadedSuccessfully: "Изображение загружено успешно!",
      pleaseSelectImage: "Пожалуйста, выберите изображение для загрузки",
      invalidFileTypeError:
        "Неверный тип файла. Пожалуйста, загрузите JPG, PNG или WebP изображение.",
      fileTooLarge: `Файл слишком большой. Максимальный размер - ${MAX_FILE_SIZE_MB}MB.`,
      failedToUploadImage: "Не удалось загрузить изображение",
      errorOccurred: "Произошла ошибка при загрузке изображения",
      uploadImageMaxSize: `Загрузите изображение (JPG, PNG, WebP), максимальный размер - ${MAX_FILE_SIZE_MB}MB.`,
    },
  };

  const { locale, show, onHide, userId, onImageUploaded }: Props = $props();

  const t = i18n[locale];

  let selectedFile = $state<File | null>(null);
  let previewUrl = $state<string | null>(null);
  let error = $state("");
  let isSubmitting = $state(false);
  let submitSuccess = $state(false);

  const handleFileChange = (e: Event) => {
    const files = (e.target as HTMLInputElement).files;

    error = "";

    if (!files || files.length === 0) {
      selectedFile = null;
      previewUrl = null;

      return;
    }

    const file = files[0];

    // Validate file type
    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      error = t.invalidFileTypeError;
      selectedFile = null;
      previewUrl = null;

      return;
    }

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      error = t.fileTooLarge;
      selectedFile = null;
      previewUrl = null;

      return;
    }

    selectedFile = file;

    // Create preview URL
    const objectUrl = URL.createObjectURL(file);
    previewUrl = objectUrl;

    return () => {
      URL.revokeObjectURL(objectUrl);
    };
  };

  const handleSubmit = async () => {
    if (!selectedFile) {
      error = t.pleaseSelectImage;

      return;
    }

    isSubmitting = true;
    error = "";

    try {
      const formData = new FormData();
      formData.append("image", selectedFile);

      const response = await fetchWithAuth(`/api/user/${userId}/image`, {
        method: "POST",
        body: formData,
        // Don't set Content-Type header, it will be set automatically with the boundary
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || t.failedToUploadImage);
      }

      submitSuccess = true;
      onImageUploaded();

      const imageInput = document.getElementById("imageInput") as HTMLInputElement | null;

      if (imageInput) {
        imageInput.files = null;
      }

      // Close modal after a short delay
      setTimeout(() => {
        handleClose();
      }, 1500);
    } catch (err) {
      error = err instanceof Error ? err.message : t.errorOccurred;
      console.error(err);
    } finally {
      isSubmitting = false;
    }
  };

  const handleClose = () => {
    selectedFile = null;
    previewUrl = null;
    error = "";
    submitSuccess = false;
    onHide();
  };
</script>

<Modal
  {show}
  title={t.uploadImage}
  onClose={handleClose}
  onSubmit={handleSubmit}
  submitText={isSubmitting ? t.uploading : t.upload}
  cancelText={t.cancel}
  submitDisabled={!selectedFile || isSubmitting}
  cancelDisabled={isSubmitting}
  {isSubmitting}
  size="lg"
>
  {#if submitSuccess}
    <div class="alert alert-success mb-3">
      {t.imageUploadedSuccessfully}
    </div>
  {/if}

  {#if error}
    <div class="alert alert-danger mb-3">
      {error}
    </div>
  {/if}

  <form>
    <div class="mb-3">
      <label for="imageInput" class="form-label">{t.pleaseSelectImage}</label>
      <input
        id="imageInput"
        type="file"
        class="form-control"
        accept=".jpg,.jpeg,.png,.webp"
        onchange={handleFileChange}
        disabled={isSubmitting}
      />
      <p class="form-text text-muted">
        {t.uploadImageMaxSize}
      </p>

      {#if previewUrl}
        <div class="mt-3 text-center">
          <img src={previewUrl} alt="Preview" class="img-thumbnail" style:max-height="200px" />
        </div>
      {/if}
    </div>
  </form>
</Modal>
