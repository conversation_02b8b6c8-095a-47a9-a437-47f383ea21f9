"use client";

import { useState, useEffect, useRef, ChangeEvent } from "react";
import { Modal, Button, Form, Alert, Image, Row, Col } from "react-bootstrap";
import { Localization } from "@/utils/find-localization";
import { useHttpRequest } from "@/app/hooks/use-http-request";
import { LocalizedInput } from "@/components/localized-input";
import { LocalizedTextarea } from "@/components/localized-textarea";
import { useLocale } from "next-intl";
import { Locale } from "@/app/types";

interface EditPostModalProps {
  show: boolean;
  onHide: () => void;
  postData: {
    id: string;
    title: Localization[];
    description: Localization[];
    status: string;
    publishedAt: string | null;
  } | null;
  onPostUpdated: () => void;
}

// Constants for file upload validation
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const MAX_FILES_COUNT = 10;
const ALLOWED_FILE_TYPES = ["image/jpeg", "image/png", "image/webp"];

const MAX_FILE_SIZE_MB = MAX_FILE_SIZE / (1024 * 1024);

const i18n = {
  en: {
    editPost: "Edit Post",
    title: {
      label: "Title",
      placeholder: "Enter post title",
    },
    description: {
      label: "Description",
      placeholder: "Enter post description",
    },
    status: {
      label: "Status",
      draft: "Draft",
      published: "Published",
      archived: "Archived",
    },
    publishDate: {
      label: "Publish Date",
      leaveEmpty: "Leave empty to publish immediately",
    },
    postUpdatedSuccessfully: "Post updated successfully!",
    maxFilesError: `You can upload a maximum of ${MAX_FILES_COUNT} images.`,
    fileSizeError: (name: string) => `File "${name}" exceeds the maximum size of ${MAX_FILE_SIZE_MB}MB.`,
    fileTypeError: (name: string) => `File "${name}" is not a supported image type.`,
    titleRequired: "Please provide a title for the post.",
    updatePostFailed: "Failed to update post",
    uploadImagesFailed: "Failed to upload images",
    errorOccured: "An error occurred while updating the post",
    addImages: {
      label: "Add Images (optional)",
      uploadInfo: `Upload up to ${MAX_FILES_COUNT} images (JPG, PNG, WebP), max ${MAX_FILE_SIZE_MB}MB each.`,
    },
    newImagesToUpload: "New Images to Upload:",
    cancel: "Cancel",
    updating: "Updating...",
    update: "Update",
  },

  ru: {
    editPost: "Редактировать публикацию",
    title: {
      label: "Заголовок",
      placeholder: "Введите заголовок публикации",
    },
    description: {
      label: "Описание",
      placeholder: "Введите описание публикации",
    },
    status: {
      label: "Статус",
      draft: "Черновик",
      published: "Опубликовано",
      archived: "Архивировано",
    },
    publishDate: {
      label: "Дата публикации",
      leaveEmpty: "Оставьте пустым для немедленной публикации",
    },
    postUpdatedSuccessfully: "Публикация обновлена успешно!",
    maxFilesError: `Вы можете загрузить максимум ${MAX_FILES_COUNT} изображений.`,
    fileSizeError: (name: string) => `Файл "${name}" превышает максимальный размер ${MAX_FILE_SIZE_MB}MB.`,
    fileTypeError: (name: string) => `Файл "${name}" не является поддерживаемым изображением.`,
    titleRequired: "Пожалуйста, предоставьте заголовок для публикации.",
    updatePostFailed: "Не удалось обновить публикацию",
    uploadImagesFailed: "Не удалось загрузить изображения",
    errorOccured: "Произошла ошибка при обновлении публикации",
    addImages: {
      label: "Добавить изображения (опционально)",
      uploadInfo: `Загрузите до ${MAX_FILES_COUNT} изображений (JPG, PNG, WebP), максимум ${MAX_FILE_SIZE_MB}MB каждое.`,
    },
    newImagesToUpload: "Новые изображения для загрузки:",
    cancel: "Отмена",
    updating: "Обновление...",
    update: "Обновить",
  },
};

export function EditPostModal({ show, onHide, postData, onPostUpdated }: EditPostModalProps) {
  const locale = useLocale() as Locale;
  const t = i18n[locale];

  const httpRequest = useHttpRequest();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [title, setTitle] = useState<Localization[]>([]);
  const [description, setDescription] = useState<Localization[]>([]);
  const [status, setStatus] = useState<string>("draft");
  const [publishedAt, setPublishedAt] = useState<string>("");
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [previewUrls, setPreviewUrls] = useState<string[]>([]);
  const [error, setError] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  // Initialize form with post data when modal opens or postData changes
  useEffect(() => {
    if (postData && show) {
      setTitle(postData.title || []);
      setDescription(postData.description || []);
      setStatus(postData.status || "draft");

      // Format the date for the datetime-local input
      if (postData.publishedAt) {
        const date = new Date(postData.publishedAt);
        const localDatetime = new Date(date.getTime() - (date.getTimezoneOffset() * 60000))
          .toISOString()
          .slice(0, 16); // Format as YYYY-MM-DDTHH:MM
        setPublishedAt(localDatetime);
      } else {
        setPublishedAt("");
      }

      // Reset file selection
      setSelectedFiles([]);
      setPreviewUrls([]);
    }
  }, [postData, show]);

  const handleClose = () => {
    setError("");
    setIsSubmitting(false);
    setSubmitSuccess(false);
    onHide();
  };

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files) return;

    // Validate file count
    if (files.length > MAX_FILES_COUNT) {
      setError(t.maxFilesError);
      return;
    }

    const newSelectedFiles: File[] = [];
    const newPreviewUrls: string[] = [];
    let hasError = false;

    // Validate each file
    Array.from(files).forEach(file => {
      // Check file size
      if (file.size > MAX_FILE_SIZE) {
        setError(t.fileSizeError(file.name));
        hasError = true;
        return;
      }

      // Check file type
      if (!ALLOWED_FILE_TYPES.includes(file.type)) {
        setError(t.fileTypeError(file.name));
        hasError = true;
        return;
      }

      newSelectedFiles.push(file);
      newPreviewUrls.push(URL.createObjectURL(file));
    });

    if (hasError) return;

    setSelectedFiles(newSelectedFiles);
    setPreviewUrls(newPreviewUrls);
    setError("");
  };

  const handleSubmit = async () => {
    if (!postData) return;

    setIsSubmitting(true);
    setError("");

    try {
      // Validate required fields
      if (!title.some(item => item.value.trim().length)) {
        setError(t.titleRequired);
        setIsSubmitting(false);
        return;
      }

      // Prepare post data
      const updateData: any = {
        title,
        description,
        status,
      };

      // Add publishedAt if provided and status is published
      if (status === "published") {
        updateData.publishedAt = publishedAt ? new Date(publishedAt).toISOString() : null;
      } else {
        updateData.publishedAt = null;
      }

      // Update post
      const updateResponse = await httpRequest(`/api/post/${postData.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updateData),
      });

      if (!updateResponse.ok) {
        const errorData = await updateResponse.json();
        throw new Error(errorData.message || t.updatePostFailed);
      }

      // Upload images if any are selected
      if (selectedFiles.length > 0) {
        const formData = new FormData();
        selectedFiles.forEach(file => {
          formData.append("images", file);
        });

        const uploadResponse = await httpRequest(`/api/post/${postData.id}/images`, {
          method: "POST",
          body: formData,
        });

        if (!uploadResponse.ok) {
          const errorData = await uploadResponse.json();
          throw new Error(errorData.message || t.uploadImagesFailed);
        }
      }

      setSubmitSuccess(true);
      onPostUpdated();

      setTimeout(() => {
        handleClose();
      }, 1500);
    } catch (err) {
      console.error("Error updating post:", err);
      setError(err instanceof Error ? err.message : t.errorOccured);
    } finally {
      setIsSubmitting(false);
    }
  };

  const removePreview = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
    setPreviewUrls(prev => prev.filter((_, i) => i !== index));
  };

  return (
    <Modal show={show} onHide={handleClose} centered size="lg">
      <Modal.Header closeButton>
        <Modal.Title>{t.editPost}</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        {submitSuccess && (
          <Alert variant="success" className="mb-3">
            {t.postUpdatedSuccessfully}
          </Alert>
        )}

        {error && (
          <Alert variant="danger" className="mb-3">
            {error}
          </Alert>
        )}

        <Form onSubmit={(e) => { e.preventDefault(); handleSubmit(); }}>
          <LocalizedInput
            id="postTitle"
            label={t.title.label}
            placeholder={t.title.placeholder}
            required={true}
            value={title}
            onChange={setTitle}
          />

          <LocalizedTextarea
            id="postDescription"
            label={t.description.label}
            placeholder={t.description.placeholder}
            rows={4}
            value={description}
            onChange={setDescription}
          />

          <Row className="mb-3">
            <Col md={6}>
              <Form.Group controlId="postStatus">
                <Form.Label>{t.status.label}</Form.Label>
                <Form.Select
                  value={status}
                  onChange={(e) => setStatus(e.target.value)}
                >
                  <option value="draft">{t.status.draft}</option>
                  <option value="published">{t.status.published}</option>
                  <option value="archived">{t.status.archived}</option>
                </Form.Select>
              </Form.Group>
            </Col>

            {status === "published" && (
              <Col md={6}>
                <Form.Group controlId="publishedAt">
                  <Form.Label>{t.publishDate.label}</Form.Label>
                  <Form.Control
                    type="datetime-local"
                    value={publishedAt}
                    onChange={(e) => setPublishedAt(e.target.value)}
                  />
                  <Form.Text className="text-muted">
                    {t.publishDate.leaveEmpty}
                  </Form.Text>
                </Form.Group>
              </Col>
            )}
          </Row>

          <Form.Group className="mb-3" controlId="postImages">
            <Form.Label>{t.addImages.label}</Form.Label>
            <Form.Control
              type="file"
              multiple
              accept=".jpg,.jpeg,.png,.webp"
              onChange={handleFileChange}
              ref={fileInputRef}
            />
            <Form.Text className="text-muted">
              {t.addImages.uploadInfo}
            </Form.Text>
          </Form.Group>

          {/* Image Previews */}
          {previewUrls.length > 0 && (
            <div className="mb-3">
              <p className="mb-2">{t.newImagesToUpload}</p>
              <div className="d-flex flex-wrap gap-2">
                {previewUrls.map((url, index) => (
                  <div key={index} className="position-relative" style={{ width: "100px" }}>
                    <Image
                      src={url}
                      alt={`Preview ${index + 1}`}
                      style={{ width: "100px", height: "75px", objectFit: "cover" }}
                      thumbnail
                    />
                    <Button
                      variant="danger"
                      size="sm"
                      className="position-absolute top-0 end-0"
                      style={{ fontSize: "0.7rem", padding: "0.1rem 0.3rem" }}
                      onClick={() => removePreview(index)}
                    >
                      ×
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </Form>
      </Modal.Body>
      <Modal.Footer>
        <Button
          variant="secondary"
          onClick={handleClose}
          disabled={isSubmitting}
        >
          {t.cancel}
        </Button>
        <Button
          variant="primary"
          disabled={!title.some(item => item.value.trim().length) || isSubmitting}
          onClick={handleSubmit}
        >
          {isSubmitting ? t.updating : t.update}
        </Button>
      </Modal.Footer>
    </Modal>
  );
}
