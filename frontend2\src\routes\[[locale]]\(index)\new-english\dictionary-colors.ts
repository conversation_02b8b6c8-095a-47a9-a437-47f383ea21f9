import type { DictionaryKeyOrAny } from "./dictionaries";

export const lightDictionaryColors: Record<DictionaryKeyOrAny, string> = {
  cambridge: "A8CF92", // green
  opendict: "F5BE6A", // orange
  any: "EBBACB", // pink
};

export const darkDictionaryColors: Record<DictionaryKeyOrAny, string> = {
  cambridge: "7B966B", // darker green
  opendict: "B58C4E", // darker orange
  any: "B08B98", // darker pink
};
