"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserResolver = void 0;
const common_1 = require("@nestjs/common");
const graphql_1 = require("@nestjs/graphql");
const zod_1 = require("../../zod");
const errors_1 = require("../../common/errors");
const user_service_1 = require("../user.service");
const Dto = require("./dto");
let UserResolver = class UserResolver {
    constructor(userService) {
        this.userService = userService;
    }
    async getUser(id) {
        const user = await this.userService.getOne(id);
        if (!user) {
            throw new common_1.NotFoundException(...(0, errors_1.getError)("user_not_found"));
        }
        return zod_1.ZodHelper.parseInput(Dto.User, user);
    }
    async getUsers(page, size) {
        console.log({ page, size });
        const users = await this.userService.getMany({}, { page, size });
        console.log(users);
        const result = zod_1.ZodHelper.parseInput(Dto.Users, users);
        console.log(result);
        return result;
    }
};
exports.UserResolver = UserResolver;
__decorate([
    (0, graphql_1.Query)("getUser"),
    __param(0, (0, graphql_1.Args)("id", new zod_1.ZodPipe(zod_1.ZodHelper.Uuid))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UserResolver.prototype, "getUser", null);
__decorate([
    (0, graphql_1.Query)("getUsers"),
    __param(0, (0, graphql_1.Args)("page")),
    __param(1, (0, graphql_1.Args)("size")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], UserResolver.prototype, "getUsers", null);
exports.UserResolver = UserResolver = __decorate([
    (0, graphql_1.Resolver)("User"),
    __metadata("design:paramtypes", [user_service_1.UserService])
], UserResolver);
//# sourceMappingURL=user.resolver.js.map