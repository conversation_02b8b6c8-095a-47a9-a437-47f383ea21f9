import { UserService } from "src/user/user.service";
import { CurrentUser } from "../types";
import { AuthService } from "../auth.service";
import * as Dto from "./dto";
import { Response } from "express";
export declare class AuthController {
    private readonly authService;
    private readonly userService;
    private readonly useSecureCookies;
    constructor(authService: AuthService, userService: UserService);
    test(): Promise<boolean>;
    me(currentUser: CurrentUser): Promise<{
        id: string;
        email: string;
        role: "user" | "admin" | "moderator";
        name: {
            locale: "en" | "ru";
            value: string;
        }[];
        description: {
            locale: "en" | "ru";
            value: string;
        }[];
        joinedAt: Date;
        images?: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            url: string;
        }[] | undefined;
    }>;
    otp(body: Dto.Otp, ipAddress: string, userAgent: string): Promise<{
        isSent: boolean;
    }>;
    private setTokenCookies;
    private clearTokenCookies;
    register(res: Response, body: Dto.Register, ipAddress: string, userAgent: string): Promise<{
        id: string;
        email: string;
        role: import(".prisma/client").$Enums.UserRole;
    }>;
    login(res: Response, body: Dto.Login, ipAddress: string, userAgent: string): Promise<{
        id: string;
        email: string;
        role: import(".prisma/client").$Enums.UserRole;
    }>;
    refresh(res: Response, refreshToken: string, ipAddress: string, userAgent: string): Promise<void>;
    signOut(res: Response): Promise<void>;
}
