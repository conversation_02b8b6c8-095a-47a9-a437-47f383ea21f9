"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const common_1 = require("@nestjs/common");
const zod_1 = require("../../zod");
const user_service_1 = require("../../user/user.service");
const auth_service_1 = require("../auth.service");
const Dto = require("./dto");
const jwt_auth_guard_1 = require("./jwt-auth.guard");
const current_user_decorator_1 = require("./current-user.decorator");
const cookies_decorator_1 = require("../../decorators/cookies.decorator");
let AuthController = class AuthController {
    constructor(authService, userService) {
        this.authService = authService;
        this.userService = userService;
        this.useSecureCookies = zod_1.z.coerce
            .boolean()
            .default(false)
            .parse(process.env.AUTH_USE_SECURE_COOKIES);
    }
    async test() {
        return true;
    }
    async me(currentUser) {
        console.log("AuthController.me.request.user", currentUser);
        const user = await this.userService.getOne(currentUser.id);
        if (!user) {
            throw new common_1.UnauthorizedException();
        }
        return zod_1.ZodHelper.parseInput(Dto.Me, {
            id: user.id,
            email: user.email,
            role: user.role,
            name: user.name,
            description: user.description,
            images: user.images,
            joinedAt: user.createdAt,
        });
    }
    async otp(body, ipAddress, userAgent) {
        const isSent = await this.authService.otp({
            ...body,
            ipAddress,
            userAgent,
        });
        return zod_1.ZodHelper.parseInput(zod_1.z.object({ isSent: zod_1.z.boolean() }), {
            isSent,
        });
    }
    setTokenCookies(res, tokens) {
        res.cookie("access_token", tokens.accessToken.value, {
            httpOnly: true,
            secure: this.useSecureCookies,
            sameSite: "strict",
            path: "/",
            expires: tokens.accessToken.expiresAt,
        });
        res.cookie("refresh_token", tokens.refreshToken.value, {
            httpOnly: true,
            secure: this.useSecureCookies,
            sameSite: "strict",
            path: "/",
            expires: tokens.refreshToken.expiresAt,
        });
    }
    clearTokenCookies(res) {
        res.clearCookie("access_token", {
            httpOnly: true,
            secure: this.useSecureCookies,
            sameSite: "strict",
            path: "/api",
        });
        res.clearCookie("refresh_token", {
            httpOnly: true,
            secure: this.useSecureCookies,
            sameSite: "strict",
            path: "/api/auth/refresh",
        });
    }
    async register(res, body, ipAddress, userAgent) {
        const { user, ...tokens } = await this.authService.register({
            ...body,
            referrerId: body.referrerId ?? null,
            ipAddress,
            userAgent,
        });
        this.setTokenCookies(res, tokens);
        return user;
    }
    async login(res, body, ipAddress, userAgent) {
        const { user, ...tokens } = await this.authService.login({
            ...body,
            ipAddress,
            userAgent,
        });
        this.setTokenCookies(res, tokens);
        return user;
    }
    async refresh(res, refreshToken, ipAddress, userAgent) {
        console.log("AuthController.refresh.request.refreshToken", refreshToken);
        const tokens = await this.authService.refresh({
            refreshToken,
            ipAddress,
            userAgent,
        });
        this.setTokenCookies(res, tokens);
    }
    async signOut(res) {
        this.clearTokenCookies(res);
    }
};
exports.AuthController = AuthController;
__decorate([
    (0, common_1.Get)("test"),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "test", null);
__decorate([
    (0, common_1.Get)("me"),
    (0, common_1.UseGuards)(new jwt_auth_guard_1.HttpJwtAuthGuard()),
    __param(0, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "me", null);
__decorate([
    (0, common_1.Post)("otp"),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)(new zod_1.ZodPipe(Dto.Otp))),
    __param(1, (0, common_1.Ip)()),
    __param(2, (0, common_1.Headers)("user-agent")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "otp", null);
__decorate([
    (0, common_1.Post)("register"),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Res)({ passthrough: true })),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(Dto.Register))),
    __param(2, (0, common_1.Ip)()),
    __param(3, (0, common_1.Headers)("user-agent")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, String, String]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "register", null);
__decorate([
    (0, common_1.Post)("login"),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Res)({ passthrough: true })),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(Dto.Login))),
    __param(2, (0, common_1.Ip)()),
    __param(3, (0, common_1.Headers)("user-agent")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, String, String]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "login", null);
__decorate([
    (0, common_1.Get)("refresh"),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Res)({ passthrough: true })),
    __param(1, (0, cookies_decorator_1.Cookies)("refresh_token", new zod_1.ZodPipe(zod_1.z.string().nonempty()))),
    __param(2, (0, common_1.Ip)()),
    __param(3, (0, common_1.Headers)("user-agent")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String, String]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "refresh", null);
__decorate([
    (0, common_1.Get)("sign-out"),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Res)({ passthrough: true })),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "signOut", null);
exports.AuthController = AuthController = __decorate([
    (0, common_1.Controller)("auth"),
    __metadata("design:paramtypes", [auth_service_1.AuthService,
        user_service_1.UserService])
], AuthController);
//# sourceMappingURL=auth.controller.js.map