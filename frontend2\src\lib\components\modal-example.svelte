<script lang="ts">
  import Modal from "./modal.svelte";

  // State for controlling the modal
  let showModal = $state(false);
  let isSubmitting = $state(false);
  let formData = $state({
    name: "",
    email: "",
  });

  // Open the modal
  function openModal() {
    showModal = true;
  }

  // Close the modal
  function closeModal() {
    showModal = false;
  }

  // Handle form submission
  function handleSubmit() {
    isSubmitting = true;

    // Simulate API call
    setTimeout(() => {
      console.log("Form submitted:", formData);
      isSubmitting = false;
      closeModal();

      // Reset form data
      formData = {
        name: "",
        email: "",
      };
    }, 1000);
  }
</script>

<div class="container mt-5">
  <h2>Modal Example</h2>
  <button class="btn btn-primary" onclick={openModal}>Open Modal</button>

  <Modal
    show={showModal}
    title="User Information"
    onClose={closeModal}
    onSubmit={handleSubmit}
    submitText="Save"
    cancelText="Cancel"
    submitDisabled={!formData.name || !formData.email}
    {isSubmitting}
  >
    <form>
      <div class="mb-3">
        <label for="name" class="form-label">Name</label>
        <input
          type="text"
          class="form-control"
          id="name"
          bind:value={formData.name}
          placeholder="Enter your name"
        />
      </div>
      <div class="mb-3">
        <label for="email" class="form-label">Email</label>
        <input
          type="email"
          class="form-control"
          id="email"
          bind:value={formData.email}
          placeholder="Enter your email"
        />
      </div>
    </form>
  </Modal>
</div>
