import * as prisma from "@prisma/client";
import { z, <PERSON>od<PERSON><PERSON><PERSON> } from "src/zod";

export type PostStatus = ZodHelper.Infer<typeof PostStatus>;
export const PostStatus = z.nativeEnum(prisma.PostStatus);

export type Post = ZodHelper.Infer<typeof Post>;
export const Post = z.object({
    id: ZodHelper.Uuid,

    title: ZodHelper.Localizations,
    description: ZodHelper.Localizations,

    status: PostStatus,
    publishedAt: ZodHelper.ToDateTime.nullable(),

    images: z.array(ZodHelper.Image).optional(),

    createdAt: ZodHelper.ToDateTime,
    updatedAt: ZodHelper.ToDateTime,
});

export const Posts = z.array(Post);

export type CreatePostInput = ZodHelper.Infer<typeof CreatePostInput>;
export const CreatePostInput = ZodHelper.JsonToObject({
    title: <PERSON>odHelper.Localizations,
    description: ZodHelper.Localizations,
    status: PostStatus,
    publishedAt: ZodHelper.ToDateTime.nullable(),
});

export type UpdatePostInput = ZodHelper.Infer<typeof UpdatePostInput>;
export const UpdatePostInput = z
    .object({
        title: ZodHelper.Localizations,
        description: ZodHelper.Localizations,
        status: PostStatus,
    })
    .partial();
