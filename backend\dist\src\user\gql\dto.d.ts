import { z, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "src/zod";
export declare const typename = "User";
export declare const UserTitle: z.ZodObject<{
    __typename: z.ZodDefault<z.ZodLiteral<"UserTitle">>;
    id: z.ZodString;
    color: z.ZodNullable<z.ZodString>;
    isActive: z.ZodBoolean;
    createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
}, "strip", z.ZodType<PERSON>ny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    __typename: "UserTitle";
    isActive: boolean;
    color: string | null;
}, {
    id: string;
    createdAt: string | number | Date;
    updatedAt: string | number | Date;
    isActive: boolean;
    color: string | null;
    __typename?: "UserTitle" | undefined;
}>;
export type User = ZodHelper.Infer<typeof User>;
export declare const User: z.ZodObject<{
    __typename: z.ZodDefault<z.ZodLiteral<"User">>;
    id: z.ZodString;
    email: z.ZodString;
    role: z.ZodNativeEnum<{
        admin: "admin";
        moderator: "moderator";
        user: "user";
    }>;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    images: z.ZodOptional<z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        url: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        id: string;
        url: string;
    }, {
        id: string;
        url: string;
    }>, "many">>;
    titles: z.ZodArray<z.ZodObject<{
        __typename: z.ZodDefault<z.ZodLiteral<"UserTitle">>;
        id: z.ZodString;
        color: z.ZodNullable<z.ZodString>;
        isActive: z.ZodBoolean;
        createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
        updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        __typename: "UserTitle";
        isActive: boolean;
        color: string | null;
    }, {
        id: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
        isActive: boolean;
        color: string | null;
        __typename?: "UserTitle" | undefined;
    }>, "many">;
    createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    id: string;
    email: string;
    role: "user" | "admin" | "moderator";
    createdAt: Date;
    updatedAt: Date;
    name: {
        locale: "en" | "ru";
        value: string;
    }[];
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    titles: {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        __typename: "UserTitle";
        isActive: boolean;
        color: string | null;
    }[];
    __typename: "User";
    images?: {
        id: string;
        url: string;
    }[] | undefined;
}, {
    id: string;
    email: string;
    role: "user" | "admin" | "moderator";
    createdAt: string | number | Date;
    updatedAt: string | number | Date;
    name: {
        locale: "en" | "ru";
        value: string;
    }[];
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    titles: {
        id: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
        isActive: boolean;
        color: string | null;
        __typename?: "UserTitle" | undefined;
    }[];
    images?: {
        id: string;
        url: string;
    }[] | undefined;
    __typename?: "User" | undefined;
}>;
export declare const Users: z.ZodArray<z.ZodObject<{
    __typename: z.ZodDefault<z.ZodLiteral<"User">>;
    id: z.ZodString;
    email: z.ZodString;
    role: z.ZodNativeEnum<{
        admin: "admin";
        moderator: "moderator";
        user: "user";
    }>;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    images: z.ZodOptional<z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        url: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        id: string;
        url: string;
    }, {
        id: string;
        url: string;
    }>, "many">>;
    titles: z.ZodArray<z.ZodObject<{
        __typename: z.ZodDefault<z.ZodLiteral<"UserTitle">>;
        id: z.ZodString;
        color: z.ZodNullable<z.ZodString>;
        isActive: z.ZodBoolean;
        createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
        updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        __typename: "UserTitle";
        isActive: boolean;
        color: string | null;
    }, {
        id: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
        isActive: boolean;
        color: string | null;
        __typename?: "UserTitle" | undefined;
    }>, "many">;
    createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    id: string;
    email: string;
    role: "user" | "admin" | "moderator";
    createdAt: Date;
    updatedAt: Date;
    name: {
        locale: "en" | "ru";
        value: string;
    }[];
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    titles: {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        __typename: "UserTitle";
        isActive: boolean;
        color: string | null;
    }[];
    __typename: "User";
    images?: {
        id: string;
        url: string;
    }[] | undefined;
}, {
    id: string;
    email: string;
    role: "user" | "admin" | "moderator";
    createdAt: string | number | Date;
    updatedAt: string | number | Date;
    name: {
        locale: "en" | "ru";
        value: string;
    }[];
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    titles: {
        id: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
        isActive: boolean;
        color: string | null;
        __typename?: "UserTitle" | undefined;
    }[];
    images?: {
        id: string;
        url: string;
    }[] | undefined;
    __typename?: "User" | undefined;
}>, "many">;
