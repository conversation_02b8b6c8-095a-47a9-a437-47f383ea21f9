<script lang="ts">
  import type { Locale } from "$lib";

  interface Props {
    locale: Locale;
  }

  const navGaps: Record<Locale, string> = {
    ru: "mx-1",
    en: "mx-2",
  };

  const i18n = {
    ru: {
      theLaw: "Право",
      rules: "Правила",
      newEnglish: "Новый английский",
      newCalendar: "Новый календарь",
      news: "Новости",
      users: "Пользователи",
      communes: "Коммуны",
      profile: "Профиль",
    },

    en: {
      theLaw: "The Law",
      rules: "Rules",
      newEnglish: "New English",
      newCalendar: "New Calendar",
      news: "News",
      users: "Users",
      communes: "Communes",
      profile: "Profile",
    },
  };

  const { locale }: Props = $props();

  const t = i18n[locale];
  const navGap = navGaps[locale];
</script>

<nav class="navbar navbar-expand-lg mb-2 py-2 sticky-top compact-navbar">
  <div class="container px-5">
    <a href="/" class="navbar-brand py-0 ps-5">
      <img
        src="/images/full-v3-transparent.svg"
        alt="Site Logo"
        height={60}
        width={60}
        style:width="auto"
      />
    </a>
    <button
      class="navbar-toggler"
      type="button"
      data-bs-toggle="collapse"
      data-bs-target="#navbarNav"
      aria-controls="navbarNav"
      aria-expanded="false"
      aria-label="Toggle navigation"
    >
      <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarNav">
      <ul class="navbar-nav mx-auto">
        <li class={`nav-item ${navGap} text-nowrap`}>
          <a href="/the-law" class="nav-link">{t.theLaw}</a>
        </li>
        <li class={`nav-item ${navGap} text-nowrap`}>
          <a href="/rules" class="nav-link">{t.rules}</a>
        </li>
        <li class={`nav-item ${navGap} text-nowrap`}>
          <a href="/new-english" class="nav-link">{t.newEnglish}</a>
        </li>
        <li class={`nav-item ${navGap} text-nowrap`}>
          <a href="/new-calendar" class="nav-link">{t.newCalendar}</a>
        </li>
        <li class={`nav-item ${navGap} text-nowrap`}>
          <a href="/news" class="nav-link">{t.news}</a>
        </li>
        <li class={`nav-item ${navGap} text-nowrap`}>
          <a href="/users" class="nav-link">{t.users}</a>
        </li>
        <li class={`nav-item ${navGap} text-nowrap`}>
          <a href="/communes" class="nav-link">{t.communes}</a>
        </li>
      </ul>
      <ul class="navbar-nav">
        <li class="nav-item">
          <a href="/profile" class="btn btn-primary btn-sm">
            {t.profile}
          </a>
        </li>
        <li class="nav-item">
          <!-- <LocaleSwitcher {locale} /> -->
        </li>
      </ul>
    </div>
  </div>
</nav>

<style>
  .compact-navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    z-index: 1030;
    backdrop-filter: blur(5px);
    background-color: rgba(248, 249, 250, 0.95) !important;
  }

  .compact-navbar :global(.nav-link) {
    font-size: 1.05rem; /* Increased font size */
    padding: 0.7rem 1rem; /* Increased padding for larger clickable area */
    transition: color 0.2s ease;
    color: #333333; /* Darker color for better contrast */
    font-weight: 500; /* Slightly bolder for better visibility */
    display: block; /* Ensures the entire area is clickable */
  }

  .compact-navbar :global(.nav-link:hover) {
    color: #0d6efd;
  }

  .compact-navbar :global(.navbar-brand) {
    display: flex;
    align-items: center;
  }

  .compact-navbar :global(.navbar-collapse) {
    justify-content: space-between;
  }

  .compact-navbar :global(.navbar-nav.mx-auto) {
    display: flex;
    justify-content: center;
  }

  .compact-navbar :global(.nav-item) {
    display: flex;
    align-items: center;
  }

  .compact-navbar :global(.btn-sm) {
    font-size: 0.95rem;
    padding: 0.5rem 1.1rem;
    font-weight: 500;
    display: inline-block;
  }

  @media (max-width: 991.98px) {
    .compact-navbar :global(.navbar-collapse) {
      padding: 0.5rem 0;
      flex-direction: column;
    }

    .compact-navbar :global(.navbar-nav) {
      text-align: center;
      margin-bottom: 0.5rem;
    }

    .compact-navbar :global(.nav-item) {
      margin: 0.2rem 0 !important;
    }

    .compact-navbar :global(.navbar-nav.mx-auto) {
      margin-left: 0 !important;
      margin-right: 0 !important;
    }
  }
</style>
