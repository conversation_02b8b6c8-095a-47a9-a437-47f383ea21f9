export function RoadmapSection() {
  return (
    <section className="py-5 py-lg-6 bg-light" id="roadmap">
      <div className="container">
        <div className="row justify-content-center mb-5">
          <div className="col-lg-8 text-center">
            <h2 className="display-5 fw-bold mb-4">Our Roadmap</h2>
            <p className="text-secondary fs-5">
            Our strategic plan for building a better future through technological innovation and community collaboration
            </p>
          </div>
        </div>

        <div className="position-relative">
          {/* Vertical line */}
          <div className="position-absolute top-0 start-50 translate-middle-x d-none d-md-block" style={{ width: "2px", height: "100%", backgroundColor: "#dee2e6" }}></div>

          <div className="row g-5">
            {/* Milestone 1 */}
            <div className="col-md-6 mb-4">
              <div className="card border-0 shadow-sm rounded-4 h-100 position-relative ms-md-5">
                <div className="position-absolute top-0 start-0 translate-middle d-none d-md-block">
                  <div className="d-flex align-items-center justify-content-center bg-primary rounded-circle text-white" style={{ width: "50px", height: "50px", fontSize: "1.5rem" }}>1</div>
                </div>
                <div className="card-body p-4">
                  <div className="d-flex align-items-center mb-3">
                    <div className="bg-primary-subtle rounded-circle p-2 me-3 d-md-none">
                      <span className="fs-5 fw-bold text-primary">1</span>
                    </div>
                    <h3 className="h4 fw-bold mb-0">First 1000 Participants</h3>
                  </div>
                  <p className="mb-0">Building our foundational community of innovators, developers, and visionaries who share our mission of technological progress for social good. This diverse group will form the core of our collaborative ecosystem.</p>
                </div>
              </div>
            </div>

            <div className="col-md-6 mb-4 offset-md-6">
              <div className="card border-0 shadow-sm rounded-4 h-100 position-relative me-md-5">
                <div className="position-absolute top-0 start-100 translate-middle d-none d-md-block">
                  <div className="d-flex align-items-center justify-content-center bg-primary rounded-circle text-white" style={{ width: "50px", height: "50px", fontSize: "1.5rem" }}>2</div>
                </div>
                <div className="card-body p-4">
                  <div className="d-flex align-items-center mb-3">
                    <div className="bg-primary-subtle rounded-circle p-2 me-3 d-md-none">
                      <span className="fs-5 fw-bold text-primary">2</span>
                    </div>
                    <h3 className="h4 fw-bold mb-0">Insurance Programs</h3>
                  </div>
                  <p className="mb-0">Developing comprehensive protection systems for our community members, ensuring security and stability as we work together on innovative projects. These programs will provide a safety net that encourages bold experimentation.</p>
                </div>
              </div>
            </div>

            <div className="col-md-6 mb-4">
              <div className="card border-0 shadow-sm rounded-4 h-100 position-relative ms-md-5">
                <div className="position-absolute top-0 start-0 translate-middle d-none d-md-block">
                  <div className="d-flex align-items-center justify-content-center bg-primary rounded-circle text-white" style={{ width: "50px", height: "50px", fontSize: "1.5rem" }}>3</div>
                </div>
                <div className="card-body p-4">
                  <div className="d-flex align-items-center mb-3">
                    <div className="bg-primary-subtle rounded-circle p-2 me-3 d-md-none">
                      <span className="fs-5 fw-bold text-primary">3</span>
                    </div>
                    <h3 className="h4 fw-bold mb-0">Educational Projects</h3>
                  </div>
                  <p className="mb-0">Launching initiatives focused on knowledge sharing, skill development, and technological literacy. These educational programs will empower individuals with the tools and understanding needed to participate in our digital future.</p>
                </div>
              </div>
            </div>

            <div className="col-md-6 mb-4 offset-md-6">
              <div className="card border-0 shadow-sm rounded-4 h-100 position-relative me-md-5">
                <div className="position-absolute top-0 start-100 translate-middle d-none d-md-block">
                  <div className="d-flex align-items-center justify-content-center bg-primary rounded-circle text-white" style={{ width: "50px", height: "50px", fontSize: "1.5rem" }}>4</div>
                </div>
                <div className="card-body p-4">
                  <div className="d-flex align-items-center mb-3">
                    <div className="bg-primary-subtle rounded-circle p-2 me-3 d-md-none">
                      <span className="fs-5 fw-bold text-primary">4</span>
                    </div>
                    <h3 className="h4 fw-bold mb-0">First Real-World Infrastructure Project</h3>
                  </div>
                  <p className="mb-0">Implementing our first major physical infrastructure initiative that demonstrates the practical application of our technological solutions. This milestone project will serve as a proof of concept for our approach to solving real-world challenges.</p>
                </div>
              </div>
            </div>

            <div className="col-md-6 mb-4">
              <div className="card border-0 shadow-sm rounded-4 h-100 position-relative ms-md-5">
                <div className="position-absolute top-0 start-0 translate-middle d-none d-md-block">
                  <div className="d-flex align-items-center justify-content-center bg-primary rounded-circle text-white" style={{ width: "50px", height: "50px", fontSize: "1.5rem" }}>5</div>
                </div>
                <div className="card-body p-4">
                  <div className="d-flex align-items-center mb-3">
                    <div className="bg-primary-subtle rounded-circle p-2 me-3 d-md-none">
                      <span className="fs-5 fw-bold text-primary">5</span>
                    </div>
                    <h3 className="h4 fw-bold mb-0">Global Expansion</h3>
                  </div>
                  <p className="mb-0">Scaling our impact worldwide by establishing regional hubs, forming international partnerships, and adapting our solutions to diverse contexts. This expansion will allow us to address global challenges while respecting local needs and conditions.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
