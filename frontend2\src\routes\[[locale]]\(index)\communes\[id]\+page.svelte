<script lang="ts">
  import type { Localization } from "$lib";

  import { onMount } from "svelte";
  import { findLocalizationForUserLocales, fetchWithAuth } from "$lib";
  import { formatDate } from "$lib/format-date";
  import { page } from "$app/state";
  import DetailImageCarousel from "./detail-image-carousel.svelte";
  import MemberCard from "./member-card.svelte";
  import AddMemberModal from "./add-member-modal.svelte";
  import EditCommuneModal from "./edit-commune-modal.svelte";

  type CommuneMemberType = "user" | "commune";

  interface CommuneImage {
    id: string;
    url: string;
    source: string;
  }

  interface CommuneMember {
    id: string;
    communeId: string;
    actorType: CommuneMemberType;
    actorId: string;
    isHead: boolean;
    joinedAt: string;
    leftAt: string | null;
    name?: Localization[];
    images?: CommuneImage[];
  }

  interface CurrentUser {
    id: string;
    email: string;
    role: "user" | "admin";
    name: Localization[];
    description: Localization[];
    joinedAt: string;
  }

  interface Commune {
    id: string;
    name: Localization[];
    description: Localization[];
    memberCount: number;
    headMember: {
      actorType: CommuneMemberType;
      actorId: string;
      name: Localization[];
    };
    images?: CommuneImage[];
    createdAt: string;
    updatedAt: string;
  }

  const i18n = {
    en: {
      loading: "Loading commune details...",
      communeNotFound: "Commune not found",
      noDescription: "No description available",
      communeDetails: "Commune Details",
      edit: "Edit",
      members: "Members",
      member: "member",
      members_plural: "members",
      headMember: "Head Member",
      created: "Created",
      addMember: "Add Member",
      noMembers: "No members found",
      errorFetchingCommune: "Failed to fetch commune",
      errorFetchingMembers: "Failed to fetch members",
      errorOccurred: "An error occurred while fetching data",
      dateFormatLocale: "en-US",
    },
    ru: {
      loading: "Загрузка данных коммуны...",
      communeNotFound: "Коммуна не найдена",
      noDescription: "Нет описания",
      communeDetails: "Информация о коммуне",
      edit: "Редактировать",
      members: "Участники",
      member: "участник",
      members_plural: "участников",
      headMember: "Глава",
      created: "Создана",
      addMember: "Добавить участника",
      noMembers: "Участники не найдены",
      errorFetchingCommune: "Не удалось загрузить коммуну",
      errorFetchingMembers: "Не удалось загрузить участников",
      errorOccurred: "Произошла ошибка при загрузке данных",
      dateFormatLocale: "ru-RU",
    },
  };

  const { data } = $props();
  const communeId = page.params.id;

  const t = i18n[data.locale];

  // State using runes
  let commune = $state<Commune | null>(null);
  let members = $state<CommuneMember[]>([]);
  let currentUser = $state<CurrentUser | null>(null);
  let loading = $state(true);
  let error = $state<string | null>(null);
  let showAddMemberModal = $state(false);
  let showEditModal = $state(false);

  // Function to refresh member data
  async function refreshMembers() {
    try {
      const membersResponse = await fetchWithAuth(`/api/commune/${communeId}/member`);

      if (!membersResponse.ok) {
        throw new Error(`${t.errorFetchingMembers}: ${membersResponse.statusText}`);
      }

      const membersData: CommuneMember[] = await membersResponse.json();

      // Sort members by joinedAt date
      const sortedMembers = membersData
        .filter((member) => !member.leftAt) // Only show active members
        .sort((a, b) => a.joinedAt.localeCompare(b.joinedAt));

      members = sortedMembers;
    } catch (err) {
      console.error("Error refreshing members:", err);
    }
  }

  async function fetchData() {
    loading = true;
    try {
      // Fetch current user data
      const userResponse = await fetchWithAuth("/api/auth/me");
      if (userResponse.ok) {
        const userData = await userResponse.json();
        currentUser = userData;
      }

      // Fetch commune details
      const communeResponse = await fetchWithAuth(`/api/commune/${communeId}`);

      if (!communeResponse.ok) {
        throw new Error(`${t.errorFetchingCommune}: ${communeResponse.statusText}`);
      }

      const communeData: Commune = await communeResponse.json();
      commune = communeData;

      // Fetch commune members
      await refreshMembers();
    } catch (err) {
      error = err instanceof Error ? err.message : t.errorOccurred;
      console.error(err);
    } finally {
      loading = false;
    }
  }

  function handleAddMemberClick() {
    showAddMemberModal = true;
  }

  function handleAddMemberModalClose() {
    showAddMemberModal = false;
  }

  function handleMemberAdded() {
    refreshMembers();
  }

  function handleMemberRemoved() {
    refreshMembers();
  }

  function handleEditClick() {
    showEditModal = true;
  }

  function handleEditModalClose() {
    showEditModal = false;
  }

  async function handleCommuneUpdated() {
    try {
      // Fetch updated commune details
      const communeResponse = await fetchWithAuth(`/api/commune/${communeId}`);

      if (!communeResponse.ok) {
        throw new Error(`Failed to fetch updated commune: ${communeResponse.statusText}`);
      }

      const communeData: Commune = await communeResponse.json();
      commune = communeData;
    } catch (err) {
      console.error("Error refreshing commune data:", err);
    }
  }

  // Derived values
  const communeName = $derived(commune ? findLocalizationForUserLocales(commune.name) : "");
  const communeDescription = $derived(
    commune ? findLocalizationForUserLocales(commune.description) : "",
  );

  // Find the head member
  const headMember = $derived(
    members.find(
      (member) =>
        commune &&
        member.actorType === commune.headMember.actorType &&
        member.actorId === commune.headMember.actorId,
    ),
  );

  // Check if current user is the head member
  const isCurrentUserHead = $derived(
    !!(
      currentUser &&
      headMember &&
      headMember.actorType === "user" &&
      headMember.actorId === currentUser.id
    ),
  );

  onMount(() => {
    if (communeId) {
      fetchData();
    }
  });
</script>

<div class="container py-4">
  {#if loading}
    <div class="text-center py-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">{t.loading}</span>
      </div>
    </div>
  {:else if error}
    <div class="alert alert-danger">
      {error}
    </div>
  {:else if !commune}
    <div class="alert alert-warning">
      {t.communeNotFound}
    </div>
  {:else}
    <div class="row">
      <div class="col-lg-8">
        <!-- Image Carousel -->
        <DetailImageCarousel images={commune.images || []} locale={data.locale} />

        <!-- Commune Information -->
        <div class="mb-4">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h2 class="mb-0">{communeName}</h2>
          </div>
          <p class="lead text-muted">{communeDescription || t.noDescription}</p>
        </div>
      </div>

      <div class="col-lg-4">
        <div class="card shadow-sm mb-4">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-2">
              <h5 class="card-title mb-0">{t.communeDetails}</h5>
              {#if isCurrentUserHead}
                <button class="btn btn-outline-primary btn-sm" onclick={handleEditClick}>
                  {t.edit}
                </button>
              {/if}
            </div>
            <hr />
            <div class="d-flex justify-content-between mb-2">
              <span>{t.members}:</span>
              <span class="badge bg-primary">{commune.memberCount}</span>
            </div>
            <div class="d-flex justify-content-between mb-2">
              <span>{t.headMember}:</span>
              <span class="text-muted">
                {findLocalizationForUserLocales(commune.headMember.name)}
              </span>
            </div>
            <div class="d-flex justify-content-between">
              <span>{t.created}:</span>
              <span class="text-muted">
                {formatDate(new Date(commune.createdAt), t.dateFormatLocale)}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Members Section -->
    <div class="d-flex justify-content-between align-items-center mt-5 mb-4">
      <h3 class="mb-0">{t.members} ({members.length})</h3>
      {#if isCurrentUserHead}
        <button class="btn btn-primary" onclick={handleAddMemberClick}>
          {t.addMember}
        </button>
      {/if}
    </div>

    {#if members.length === 0}
      <div class="alert alert-info">{t.noMembers}</div>
    {:else}
      <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-4 g-4">
        {#each members as member (member.id)}
          <div class="col">
            <MemberCard
              id={member.id}
              actorType={member.actorType}
              actorId={member.actorId}
              name={member.name ?? []}
              isHead={member.actorType === commune.headMember.actorType &&
                member.actorId === commune.headMember.actorId}
              joinedAt={member.joinedAt}
              {communeId}
              {isCurrentUserHead}
              locale={data.locale}
              onMemberRemoved={handleMemberRemoved}
              images={member.images || []}
            />
          </div>
        {/each}
      </div>
    {/if}

    <!-- Add Member Modal -->
    <AddMemberModal
      show={showAddMemberModal}
      onHide={handleAddMemberModalClose}
      {communeId}
      locale={data.locale}
      onMemberAdded={handleMemberAdded}
    />

    <!-- Edit Commune Modal -->
    <EditCommuneModal
      show={showEditModal}
      onHide={handleEditModalClose}
      communeData={commune}
      locale={data.locale}
      onCommuneUpdated={handleCommuneUpdated}
    />
  {/if}
</div>
