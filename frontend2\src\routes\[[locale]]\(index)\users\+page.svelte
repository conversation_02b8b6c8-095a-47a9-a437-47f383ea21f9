<script lang="ts">
  import type { Localization } from "$lib";

  import { onMount } from "svelte";
  import { findLocalizationForUserLocales, fetchWithAuth } from "$lib";
  import UserImageCarousel from "./user-image-carousel.svelte";

  interface User {
    id: string;
    email: string;
    role: "user" | "admin";
    name: Localization[];
    description: Localization[];
    images?: {
      id: string;
      url: string;
      source: string;
    }[];
  }

  const i18n = {
    en: {
      users: "Users",
      loading: "Loading...",
      noUsersFound: "No users found",
      noDescription: "No description",
      errorFetchingUsers: "Failed to fetch users",
      errorOccurred: "An error occurred while fetching users",
      first: "First",
      previous: "Previous",
      next: "Next",
      last: "Last",
      page: "Page",
    },
    ru: {
      users: "Пользователи",
      loading: "Загрузка...",
      noUsersFound: "Пользователи не найдены",
      noDescription: "Нет описания",
      errorFetchingUsers: "Не удалось загрузить пользователей",
      errorOccurred: "Произошла ошибка при загрузке пользователей",
      first: "Первая",
      previous: "Предыдущая",
      next: "Следующая",
      last: "Последняя",
      page: "Страница",
    },
  };

  // Get the locale from the page data
  const { data } = $props();

  const t = i18n[data.locale];

  // State using runes
  let users = $state<User[]>([]);
  let currentPage = $state(1);
  let totalPages = $state(1);
  let loading = $state(true);
  let error = $state<string | null>(null);

  const pageSize = 20;

  // Function to fetch users
  async function fetchUsers() {
    loading = true;

    try {
      const response = await fetchWithAuth(`/api/user?page=${currentPage}&size=${pageSize}`);

      if (!response.ok) {
        throw new Error(`${t.errorFetchingUsers}: ${response.statusText}`);
      }

      const data: User[] = await response.json();
      users = data;

      // Set total pages based on data length
      totalPages = Math.ceil(data.length / pageSize) || 1;
    } catch (err) {
      error = err instanceof Error ? err.message : t.errorOccurred;
      console.error(err);
    } finally {
      loading = false;
    }
  }

  // Handle page change
  function handlePageChange(page: number) {
    currentPage = page;
  }

  // Generate pagination array
  function getPaginationArray() {
    return Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
      return Math.max(1, Math.min(currentPage - 2 + i, totalPages));
    });
  }

  onMount(() => {
    fetchUsers();
  });
</script>

<div class="container">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h1>{t.users}</h1>
  </div>

  {#if loading}
    <div class="text-center py-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">{t.loading}</span>
      </div>
    </div>
  {:else if error}
    <div class="alert alert-danger" role="alert">
      {error}
    </div>
  {:else if users.length === 0}
    <div class="text-center py-5">
      <p class="text-muted">{t.noUsersFound}</p>
    </div>
  {:else}
    <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-5 g-4">
      {#each users as user (user.id)}
        <div class="col">
          <div class="card h-100 shadow-sm hover-card">
            <!-- User image carousel -->
            <UserImageCarousel images={user.images || []} locale={data.locale} />
            <a href={`/users/${user.id}`} class="text-decoration-none text-black">
              <div class="card-body d-flex flex-column">
                <h5 class="card-title fs-5 text-truncate">
                  {findLocalizationForUserLocales(user.name) || user.email}
                </h5>

                <p class="card-text text-muted small" style="height: 3rem; overflow: hidden">
                  {findLocalizationForUserLocales(user.description) || t.noDescription}
                </p>

                <div class="mt-auto">
                  <div class="small text-muted">
                    <span class={`badge ${user.role === "admin" ? "bg-danger" : "bg-primary"}`}>
                      {user.role}
                    </span>
                  </div>
                </div>
              </div>
            </a>
          </div>
        </div>
      {/each}
    </div>

    <!-- Pagination -->
    {#if users.length > 0 && totalPages > 1}
      <div class="d-flex justify-content-center mt-4">
        <nav aria-label="User pagination">
          <ul class="pagination">
            <li class={`page-item ${currentPage === 1 ? "disabled" : ""}`}>
              <button class="page-link" aria-label={t.first} onclick={() => handlePageChange(1)}>
                <span aria-hidden="true">&laquo;&laquo;</span>
                <span class="visually-hidden">{t.first}</span>
              </button>
            </li>
            <li class={`page-item ${currentPage === 1 ? "disabled" : ""}`}>
              <button
                class="page-link"
                aria-label={t.previous}
                onclick={() => handlePageChange(currentPage - 1)}
              >
                <span aria-hidden="true">&laquo;</span>
                <span class="visually-hidden">{t.previous}</span>
              </button>
            </li>

            <!-- Show current page and adjacent pages -->
            {#each getPaginationArray() as pageNum}
              <li class={`page-item ${pageNum === currentPage ? "active" : ""}`}>
                <button
                  class="page-link"
                  onclick={() => handlePageChange(pageNum)}
                  aria-label={`${t.page} ${pageNum}`}
                  aria-current={pageNum === currentPage ? "page" : undefined}
                >
                  {pageNum}
                </button>
              </li>
            {/each}

            <li class={`page-item ${currentPage === totalPages ? "disabled" : ""}`}>
              <button
                class="page-link"
                aria-label={t.next}
                onclick={() => handlePageChange(currentPage + 1)}
              >
                <span aria-hidden="true">&raquo;</span>
                <span class="visually-hidden">{t.next}</span>
              </button>
            </li>
            <li class={`page-item ${currentPage === totalPages ? "disabled" : ""}`}>
              <button
                class="page-link"
                aria-label={t.last}
                onclick={() => handlePageChange(totalPages)}
              >
                <span aria-hidden="true">&raquo;&raquo;</span>
                <span class="visually-hidden">{t.last}</span>
              </button>
            </li>
          </ul>
        </nav>
      </div>
    {/if}
  {/if}
</div>

<style>
  .hover-card {
    cursor: pointer;
    transition: transform 0.2s;
  }

  .hover-card:hover {
    transform: translateY(-5px);
  }
</style>
