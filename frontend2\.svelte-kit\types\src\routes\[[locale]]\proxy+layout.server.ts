// @ts-nocheck
import type {
  LayoutLoad,
  LayoutLoadEvent,
  LayoutServerLoad,
  LayoutServerLoadEvent,
} from "./$types";

import Negotiator from "negotiator";
import { Locale } from "$lib";
import { match } from "@formatjs/intl-localematcher";

export const load = (event: Parameters<LayoutServerLoad>[0]) => {
  const locale = getLocale(event);

  return {
    locale,
  };
};

function getLocale(event: LayoutServerLoadEvent): Locale {
  if (event.params.locale) {
    const parsedLocale = Locale.safeParse(event.params.locale);

    if (parsedLocale.success) {
      return parsedLocale.data;
    }
  }

  const acceptLanguage = event.request.headers.get("accept-language");

  if (acceptLanguage) {
    const negotiatorRequest = {
      headers: {
        "accept-language": acceptLanguage,
      },
    };

    const preferredLanguages = new Negotiator(negotiatorRequest).languages();

    return match(preferredLanguages, Locale._def.values, "en") as Locale;
  }

  return "en";
}
