"use client";

import { useEffect, useState } from "react";
import { Container, Row, Col, Card, Pagination } from "react-bootstrap";
import { useHttpRequest } from "@/app/hooks/use-http-request";
import { findLocalizationForUserLocales, Localization } from "@/utils/find-localization";
import { UserImageCarousel } from "./user-image-carousel";
import Link from "next/link";
import { useLocale } from "next-intl";
import { Locale } from "@/app/types";

interface User {
  id: string;
  email: string;
  role: "user" | "admin";
  name: Localization[];
  description: Localization[];
  images?: {
    id: string;
    url: string;
    source: string;
  }[];
}

const i18n = {
  en: {
    users: "Users",
    loading: "Loading...",
    noUsersFound: "No users found",
    noDescription: "No description",
    errorFetchingUsers: "Failed to fetch users",
    errorOccurred: "An error occurred while fetching users",
  },

  ru: {
    users: "Пользователи",
    loading: "Загрузка...",
    noUsersFound: "Пользователи не найдены",
    noDescription: "Нет описания",
    errorFetchingUsers: "Не удалось загрузить пользователей",
    errorOccurred: "Произошла ошибка при загрузке пользователей",
  },
};

export default function Users() {
  const locale = useLocale() as Locale;
  const t = i18n[locale];

  const [users, setUsers] = useState<User[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const pageSize = 20;

  const httpRequest = useHttpRequest();

  useEffect(() => {
    const fetchUsers = async () => {
      setLoading(true);
      try {
        const response = await httpRequest(`/api/user?page=${currentPage}&size=${pageSize}`);

        if (!response.ok) {
          throw new Error(`${t.errorFetchingUsers}: ${response.statusText}`);
        }

        const data: User[] = await response.json();
        setUsers(data);

        // Set total pages based on data length
        setTotalPages(Math.ceil(data.length / pageSize) || 1);
      } catch (err) {
        setError(err instanceof Error ? err.message : t.errorOccurred);
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, [currentPage]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  return (
    <Container>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h1>{t.users}</h1>
      </div>

      {loading ? (
        <div className="text-center py-5">
          <div className="spinner-border" role="status">
            <span className="visually-hidden">{t.loading}</span>
          </div>
        </div>
      ) : error ? (
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
      ) : (
        <>
          {users.length === 0 ? (
            <div className="text-center py-5">
              <p className="text-muted">{t.noUsersFound}</p>
            </div>
          ) : (
            <Row xs={1} sm={2} md={3} lg={5} className="g-4">
              {users.map((user) => (
                <Col key={user.id}>
                  <Card
                    className="h-100 user-card shadow-sm"
                    style={{ cursor: "pointer", transition: "transform 0.2s" }}
                    onMouseOver={(e) => (e.currentTarget.style.transform = "translateY(-5px)")}
                    onMouseOut={(e) => (e.currentTarget.style.transform = "translateY(0)")}
                  >
                    {/* User image carousel */}
                    <UserImageCarousel
                      images={user.images || []}
                    />
                    <Link href={`/users/${user.id}`} className="text-decoration-none text-black">

                      <Card.Body className="d-flex flex-column">
                        <Card.Title className="fs-5 text-truncate">
                          {findLocalizationForUserLocales(user.name) || user.email}
                        </Card.Title>

                        <Card.Text className="text-muted small" style={{ height: "3rem", overflow: "hidden" }}>
                          {findLocalizationForUserLocales(user.description) || t.noDescription}
                        </Card.Text>

                        <div className="mt-auto">
                          <div className="small text-muted">
                            <span
                              className={`badge bg-${user.role === "admin" ? "danger" : "primary"}`}
                              style={{ display: "inline-block" }}
                            >
                              {user.role}
                            </span>
                          </div>
                        </div>
                      </Card.Body>
                    </Link>
                  </Card>
                </Col>
              ))}
            </Row>
          )}

          {/* Pagination */}
          {users.length > 0 && totalPages > 1 && (
            <div className="d-flex justify-content-center mt-4">
              <Pagination>
                <Pagination.First
                  onClick={() => handlePageChange(1)}
                  disabled={currentPage === 1}
                />
                <Pagination.Prev
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                />

                {/* Show current page and adjacent pages */}
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  const pageNum = Math.max(1, Math.min(currentPage - 2 + i, totalPages));
                  return (
                    <Pagination.Item
                      key={pageNum}
                      active={pageNum === currentPage}
                      onClick={() => handlePageChange(pageNum)}
                    >
                      {pageNum}
                    </Pagination.Item>
                  );
                })}

                <Pagination.Next
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                />
                <Pagination.Last
                  onClick={() => handlePageChange(totalPages)}
                  disabled={currentPage === totalPages}
                />
              </Pagination>
            </div>
          )}
        </>
      )}
    </Container>
  );
}
