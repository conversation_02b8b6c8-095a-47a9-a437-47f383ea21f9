import { CurrentUser } from "src/auth/types";
import { ReactorService } from "../reactor.service";
import * as Dto from "./dto";
export declare class ReactorController {
    private readonly reactorService;
    constructor(reactorService: ReactorService);
    getPosts(page: number, size: number, user: CurrentUser): Promise<{
        items: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            tags: string[];
            title: {
                locale: "en" | "ru";
                value: string;
            }[];
            author: {
                id: string;
                name: {
                    locale: "en" | "ru";
                    value: string;
                }[];
                avatar: string | null;
            };
            body: {
                locale: "en" | "ru";
                value: string;
            }[];
            usefulness: {
                value: number | null;
                count: number;
                totalValue: number | null;
            };
            rating: {
                status: "like" | "dislike" | null;
                likes: number;
                dislikes: number;
            };
        }[];
        total: number;
    }>;
    getPost(id: string, user: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        tags: string[];
        title: {
            locale: "en" | "ru";
            value: string;
        }[];
        author: {
            id: string;
            name: {
                locale: "en" | "ru";
                value: string;
            }[];
            avatar: string | null;
        };
        body: {
            locale: "en" | "ru";
            value: string;
        }[];
        usefulness: {
            value: number | null;
            count: number;
            totalValue: number | null;
        };
        rating: {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        };
    }>;
    createPost(body: Dto.CreatePost, user: CurrentUser): Promise<{
        id: string;
    }>;
    updatePost(id: string, body: Dto.UpdatePost, user: CurrentUser): Promise<boolean>;
    updatePostRating(id: string, body: Dto.UpdatePostRating, user: CurrentUser): Promise<{
        likes: number;
        dislikes: number;
        status: import(".prisma/client").$Enums.ReactorRatingType | null;
    }>;
    updatePostUsefulness(id: string, body: Dto.UpdatePostUsefulness, user: CurrentUser): Promise<{
        count: number;
        totalValue: number | null;
        value: number | null;
    }>;
    deletePost(id: string, body: Dto.DeletePost, user: CurrentUser): Promise<boolean>;
    getComments(entityType: Dto.GetCommentsEntityType, entityId: string, user: CurrentUser): Promise<{
        items: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            path: string;
            isAnonymous: boolean;
            anonimityReason: string | null;
            deleteReason: string | null;
            author: {
                id: string;
                name: {
                    locale: "en" | "ru";
                    value: string;
                }[];
                avatar: string | null;
            } | null;
            body: {
                locale: "en" | "ru";
                value: string;
            }[] | null;
            childrenCount: number;
            rating: {
                status: "like" | "dislike" | null;
                likes: number;
                dislikes: number;
            };
        }[];
        total: number;
    }>;
    getComment(id: string, user: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        path: string;
        isAnonymous: boolean;
        anonimityReason: string | null;
        deleteReason: string | null;
        author: {
            id: string;
            name: {
                locale: "en" | "ru";
                value: string;
            }[];
            avatar: string | null;
        } | null;
        body: {
            locale: "en" | "ru";
            value: string;
        }[] | null;
        childrenCount: number;
        rating: {
            status: "like" | "dislike" | null;
            likes: number;
            dislikes: number;
        };
    }>;
    createComment(body: Dto.CreateComment, user: CurrentUser): Promise<{
        id: string;
    }>;
    updateComment(id: string, body: Dto.UpdateComment, user: CurrentUser): Promise<boolean>;
    updateCommentRating(id: string, body: Dto.UpdateCommentRating, user: CurrentUser): Promise<{
        likes: number;
        dislikes: number;
        status: import(".prisma/client").$Enums.ReactorRatingType | null;
    }>;
    anonimifyComment(id: string, body: Dto.AnonimifyComment, user: CurrentUser): Promise<boolean>;
    deleteComment(id: string, body: Dto.DeleteComment, user: CurrentUser): Promise<boolean>;
}
