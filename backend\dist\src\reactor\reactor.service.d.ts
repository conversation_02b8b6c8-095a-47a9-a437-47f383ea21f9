import { ReactorRatingType } from "@prisma/client";
import { Localization } from "src/zod/helper";
import { PrismaService } from "src/prisma/prisma.service";
import { CurrentUser } from "src/auth/types";
export type Post = {
    id: string;
    author: {
        id: string;
        name: Localization[];
        avatar: string | null;
    };
    rating: {
        likes: number;
        dislikes: number;
        status: ReactorRatingType | null;
    };
    usefulness: {
        value: number | null;
        count: number;
        totalValue: number;
    };
    title: Localization[];
    body: Localization[];
    tags: string[];
    createdAt: Date;
    updatedAt: Date;
};
export type PostsResponse = {
    items: Post[];
    total: number;
};
export type Comment = {
    id: string;
    path: string;
    internalNumber: number;
    author: {
        id: string;
        name: Localization[];
        avatar: string | null;
    } | null;
    isAnonymous: boolean;
    anonimityReason: string | null;
    rating: {
        likes: number;
        dislikes: number;
        status: ReactorRatingType | null;
    };
    body: Localization[] | null;
    childrenCount: number;
    deleteReason: string | null;
    createdAt: Date;
    updatedAt: Date;
    deletedAt: Date | null;
};
export type CommentsResponse = {
    items: Comment[];
    total: number;
};
export declare class ReactorService {
    private readonly prisma;
    constructor(prisma: PrismaService);
    getPosts(pagination: {
        page: number;
        size: number;
    } | undefined, user: CurrentUser): Promise<PostsResponse>;
    getPost(id: string, user: CurrentUser): Promise<{
        id: string;
        author: {
            id: string;
            name: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                key: string;
                locale: import(".prisma/client").$Enums.Locale;
                value: string;
            }[];
            avatar: string | null;
        };
        rating: {
            likes: number;
            dislikes: number;
            status: import(".prisma/client").$Enums.ReactorRatingType | null;
        };
        usefulness: {
            value: number | null;
            count: number;
            totalValue: number;
        };
        title: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
        body: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
        tags: string[];
        createdAt: Date;
        updatedAt: Date;
    }>;
    createPost(dto: {
        title: Localization[];
        body: Localization[];
        tags: string[];
    }, user: CurrentUser): Promise<{
        id: string;
    }>;
    updatePost(id: string, dto: Partial<{
        title: Localization[];
        body: Localization[];
        tags: string[];
    }>, user: CurrentUser): Promise<boolean>;
    updatePostRating(id: string, dto: {
        type: ReactorRatingType;
    }, user: CurrentUser): Promise<{
        likes: number;
        dislikes: number;
        status: import(".prisma/client").$Enums.ReactorRatingType | null;
    }>;
    updatePostUsefulness(id: string, dto: {
        value: number | null;
    }, user: CurrentUser): Promise<{
        count: number;
        totalValue: number | null;
        value: number | null;
    }>;
    deletePost(id: string, dto: {
        reason: string | null;
    }, user: CurrentUser): Promise<boolean>;
    getComments(dto: {
        entityType: "post";
        entityId: string;
    }, user: CurrentUser): Promise<CommentsResponse>;
    getComment(dto: {
        id: string;
    }, user: CurrentUser): Promise<Comment>;
    private getNextCommentInternalNumber;
    createComment(dto: {
        entityType: "post" | "comment";
        entityId: string;
        body: Localization[];
    }, user: CurrentUser): Promise<{
        id: string;
    }>;
    private createPostComment;
    private createCommentComment;
    updateComment(id: string, dto: Partial<{
        body: Localization[];
    }>, user: CurrentUser): Promise<boolean>;
    updateCommentRating(id: string, dto: {
        type: ReactorRatingType;
    }, user: CurrentUser): Promise<{
        likes: number;
        dislikes: number;
        status: import(".prisma/client").$Enums.ReactorRatingType | null;
    }>;
    anonimifyComment(id: string, dto: {
        reason: string | null;
    }, user: CurrentUser): Promise<boolean>;
    deleteComment(id: string, dto: {
        reason: string | null;
    }, user: CurrentUser): Promise<boolean>;
}
