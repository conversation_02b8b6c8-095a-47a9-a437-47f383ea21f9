"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateVotingInput = exports.CreateVotingOptionInput = exports.Votings = exports.Voting = exports.typename = exports.VotingOptions = exports.VotingOption = exports.votingOptionTypename = exports.votesRequired = void 0;
const zod_1 = require("zod");
const zod_2 = require("../../zod");
exports.votesRequired = zod_1.z.number().int().positive();
exports.votingOptionTypename = "VotingOption";
exports.VotingOption = zod_2.ZodHelper.GqlObject(exports.votingOptionTypename, {
    id: zod_2.ZodHelper.Uuid,
    title: zod_2.ZodHelper.Localizations,
    createdAt: zod_2.ZodHelper.ToDateTime,
    updatedAt: zod_2.ZodHelper.ToDateTime,
});
exports.VotingOptions = zod_1.z.array(exports.VotingOption);
exports.typename = "Voting";
exports.Voting = zod_2.ZodHelper.GqlObject(exports.typename, {
    id: zod_2.ZodHelper.Uuid,
    votesRequired: exports.votesRequired,
    endsAt: zod_2.ZodHelper.ToDateTime,
    title: zod_2.ZodHelper.Localizations,
    description: zod_2.ZodHelper.Localizations,
    options: zod_1.z.array(exports.VotingOption),
    createdAt: zod_2.ZodHelper.ToDateTime,
    updatedAt: zod_2.ZodHelper.ToDateTime,
});
exports.Votings = zod_1.z.array(exports.Voting);
exports.CreateVotingOptionInput = zod_1.z.object({
    title: zod_2.ZodHelper.Localizations,
});
exports.CreateVotingInput = zod_1.z.object({
    votesRequired: exports.votesRequired,
    endsAt: zod_2.ZodHelper.ToDateTime,
    title: zod_2.ZodHelper.Localizations,
    description: zod_2.ZodHelper.Localizations,
    options: zod_1.z.array(exports.CreateVotingOptionInput),
});
//# sourceMappingURL=dto.js.map