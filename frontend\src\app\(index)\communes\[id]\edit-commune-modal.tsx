"use client";

import { useState, useEffect } from "react";
import { Mo<PERSON>, Button, Form, Alert } from "react-bootstrap";
import { Localization } from "@/utils/find-localization";
import { useHttpRequest } from "@/app/hooks/use-http-request";
import { LocalizedInput } from "@/components/localized-input";
import { LocalizedTextarea } from "@/components/localized-textarea";
import { useLocale } from "next-intl";
import { Locale } from "@/app/types";

interface EditCommuneModalProps {
  show: boolean;
  onHide: () => void;
  communeData: {
    id: string;
    name: Localization[];
    description: Localization[];
  } | null;
  onCommuneUpdated: () => void;
}

const i18n = {
  en: {
    editCommune: "Edit Commune",
    communeUpdatedSuccess: "Commune updated successfully!",
    name: "Name",
    enterCommuneName: "Enter commune name",
    description: "Description (optional)",
    enterCommuneDescription: "Enter commune description",
    cancel: "Cancel",
    save: "Save Changes",
    saving: "Saving...",
    provideName: "Please provide a name for the commune.",
    failedToUpdate: "Failed to update commune",
    unexpectedError: "An unexpected error occurred. Please try again.",
  },

  ru: {
    editCommune: "Редактировать коммуну",
    communeUpdatedSuccess: "Коммуна успешно обновлена!",
    name: "Название",
    enterCommuneName: "Введите название коммуны",
    description: "Описание (опционально)",
    enterCommuneDescription: "Введите описание коммуны",
    cancel: "Отмена",
    save: "Сохранить изменения",
    saving: "Сохранение...",
    provideName: "Пожалуйста, укажите название коммуны.",
    failedToUpdate: "Не удалось обновить коммуну",
    unexpectedError: "Произошла непредвиденная ошибка. Пожалуйста, попробуйте снова.",
  },
};

export function EditCommuneModal({ show, onHide, communeData, onCommuneUpdated }: EditCommuneModalProps) {
  const locale = useLocale() as Locale;
  const t = i18n[locale];

  const httpRequest = useHttpRequest();

  const [name, setName] = useState<Localization[]>([]);
  const [description, setDescription] = useState<Localization[]>([]);
  const [error, setError] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  // Initialize form with commune data when modal opens or communeData changes
  useEffect(() => {
    if (communeData) {
      setName(communeData.name || []);
      setDescription(communeData.description || []);
    }
  }, [communeData, show]);

  const handleSubmit = async () => {
    if (!name.some(item => item.value.trim().length)) {
      setError(t.provideName);
      return;
    }

    setIsSubmitting(true);
    setError("");

    try {
      const response = await httpRequest(`/api/commune/${communeData?.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name,
          description,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || t.failedToUpdate);
      }

      setSubmitSuccess(true);
      onCommuneUpdated();

      // Close modal after a short delay
      setTimeout(() => {
        handleClose();
      }, 1500);
    } catch (err) {
      setError(err instanceof Error ? err.message : t.unexpectedError);
      console.error(err);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setName([]);
    setDescription([]);
    setError("");
    setSubmitSuccess(false);
    onHide();
  };

  return (
    <Modal show={show} onHide={handleClose} centered>
      <Modal.Header closeButton>
        <Modal.Title>{t.editCommune}</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        {submitSuccess && (
          <Alert variant="success" className="mb-3">
            {t.communeUpdatedSuccess}
          </Alert>
        )}

        {error && (
          <Alert variant="danger" className="mb-3">
            {error}
          </Alert>
        )}

        <Form onSubmit={(e) => { e.preventDefault(); handleSubmit(); }}>
          <LocalizedInput
            id="communeName"
            label={t.name}
            placeholder={t.enterCommuneName}
            required={true}
            value={name}
            onChange={setName}
          />

          <LocalizedTextarea
            id="communeDescription"
            label={t.description}
            placeholder={t.enterCommuneDescription}
            rows={4}
            value={description}
            onChange={setDescription}
          />
        </Form>
      </Modal.Body>
      <Modal.Footer>
        <Button
          variant="secondary"
          onClick={handleClose}
          disabled={isSubmitting}
        >
          {t.cancel}
        </Button>
        <Button
          variant="primary"
          disabled={!name.some(item => item.value.trim().length) || isSubmitting}
          onClick={handleSubmit}
        >
          {isSubmitting ? t.saving : t.save}
        </Button>
      </Modal.Footer>
    </Modal>
  );
}
