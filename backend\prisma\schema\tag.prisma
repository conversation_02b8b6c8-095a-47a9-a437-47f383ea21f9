// tag
model Tag {
    @@map("tags")

    id String @id @db.Uuid @default(uuid(7))

    communes     Commune[]     @relation("commune_tags")
    votings      Voting[]      @relation("voting_tags")
    posts        Post[]        @relation("post_tags")
    merchandises Merchandise[] @relation("merchandise_tags")
    reactorPosts ReactorPost[] @relation("reactor_post_tags")

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
    deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)
}
