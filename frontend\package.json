{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:check": "eslint ./src --ext .ts --max-warnings 0", "lint:fix": "eslint ./src --ext .ts --max-warnings 0 --fix"}, "dependencies": {"@heroicons/react": "^2.2.0", "bootstrap": "^5.3.3", "chart.js": "^4.4.8", "classnames": "^2.5.1", "cookies-next": "^5.1.0", "next": "^15.2.4", "next-intl": "^4.1.0", "pino": "^9.6.0", "react": "^19.0.0", "react-bootstrap": "^2.10.9", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "superagent": "^10.2.0", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/bootstrap": "^5.2.10", "@types/negotiator": "^0.6.3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/superagent": "^8.1.9", "eslint": "^9", "eslint-config-next": "15.2.1", "eslint-plugin-react": "^7.37.4", "typescript": "^5"}}