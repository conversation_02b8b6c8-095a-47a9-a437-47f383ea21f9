<script lang="ts">
  import type { Locale } from "$lib";
  import { Modal } from "$lib/components";

  interface Props {
    locale: Locale;
    show: boolean;
    onHide: () => void;
    images: { id: string; url: string }[];
    activeIndex?: number;
  }

  const i18n = {
    en: {
      title: "Profile Images",
      noImages: "No images available",
      close: "Close",
    },

    ru: {
      title: "Изображения профиля",
      noImages: "Нет изображений",
      close: "Закрыть",
    },
  };

  const { locale, show, onHide, images, activeIndex }: Props = $props();

  const t = i18n[locale];

  // Initialize index with activeIndex or default to 0
  let index = $state(activeIndex !== undefined ? activeIndex : 0);
</script>

<Modal
  {show}
  title={t.title}
  onClose={onHide}
  cancelText={t.close}
  showFooter={false}
  showCloseButton={true}
  submitDisabled={true}
  size="lg"
  centered={true}
>
  <div class="p-0">
    {#if images.length > 0}
      <div class="carousel">
        {#each images as image, idx (image.id)}
          <div class="carousel-item {idx === index ? 'active' : ''}">
            <div class="image-container">
              <img
                class="image"
                src={`/api/images/${image.url}`}
                alt={`Profile image ${idx + 1}`}
                sizes="(max-width: 768px) 100vw, 800px"
              />
            </div>
          </div>
        {/each}

        {#if images.length > 1}
          <!-- Navigation arrows -->
          <button
            class="carousel-control-prev"
            type="button"
            onclick={() => (index = (index - 1 + images.length) % images.length)}
          >
            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
            <span class="visually-hidden">Previous</span>
          </button>
          <button
            class="carousel-control-next"
            type="button"
            onclick={() => (index = (index + 1) % images.length)}
          >
            <span class="carousel-control-next-icon" aria-hidden="true"></span>
            <span class="visually-hidden">Next</span>
          </button>

          <!-- Indicators -->
          <div class="carousel-indicators">
            {#each images as _, idx}
              <button
                type="button"
                class={idx === index ? "active" : ""}
                onclick={() => (index = idx)}
                aria-label={`Image ${idx + 1}`}
              ></button>
            {/each}
          </div>
        {/if}
      </div>
    {:else}
      <div class="text-center py-5">
        <p class="mb-0">{t.noImages}</p>
      </div>
    {/if}
  </div>
</Modal>

<style>
  /* Modal styling is now handled by the Modal component */

  .carousel {
    width: 100%;
    background-color: #000;
  }

  .carousel-item {
    height: 500px;
    position: relative;
    background-color: #000;
  }

  .image-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .image {
    object-fit: contain;
    max-width: 100%;
    max-height: 100%;
  }

  /* Semi-transparent navigation arrows */
  .carousel :global(.carousel-control-prev),
  .carousel :global(.carousel-control-next) {
    background-color: rgba(0, 0, 0, 0.3);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    top: 50%;
    transform: translateY(-50%);
    margin: 0 15px;
  }

  /* Indicator dots */
  .carousel :global(.carousel-indicators) {
    margin-bottom: 0.5rem;
  }

  .carousel :global(.carousel-indicators button) {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin: 0 5px;
    background-color: rgba(255, 255, 255, 0.5);
  }

  .carousel :global(.carousel-indicators button.active) {
    background-color: #fff;
  }

  @media (max-width: 768px) {
    .carousel-item {
      height: 300px;
    }
  }
</style>
