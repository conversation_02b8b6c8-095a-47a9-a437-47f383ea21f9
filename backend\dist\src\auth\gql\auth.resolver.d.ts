import * as Gql from "src/graphql.d";
import { UserService } from "src/user/user.service";
import { CurrentUser } from "../types";
export type IAuthResolver = Gql.IResolver<"test" | "me", never>;
export declare class AuthResolver implements IAuthResolver {
    private readonly userService;
    constructor(userService: UserService);
    test(): boolean;
    me(currentUser?: CurrentUser): Promise<{
        id: string;
        email: string;
        role: "user" | "admin" | "moderator";
        name: {
            locale: "en" | "ru";
            value: string;
        }[];
        description: {
            locale: "en" | "ru";
            value: string;
        }[];
        joinedAt: string;
        __typename: "Me";
    }>;
}
