"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommuneService = void 0;
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
const utils_1 = require("../utils");
const errors_1 = require("../common/errors");
const base_service_1 = require("../common/base-service");
const prisma_service_1 = require("../prisma/prisma.service");
const minio_service_1 = require("../minio/minio.service");
let CommuneService = class CommuneService extends base_service_1.BaseService {
    constructor(prisma, minioService) {
        super("commune");
        this.prisma = prisma;
        this.minioService = minioService;
    }
    async canGet(id, user) {
        await this.getOneOrThrow(id);
        return true;
    }
    async canChange(id, user) {
        await this.getOneOrThrow(id);
        const headMember = await this.getHeadMember(id);
        if (user.role !== client_1.UserRole.admin) {
            if (headMember.actorType === client_1.CommuneMemberType.user &&
                headMember.actorId !== user.id) {
                throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_head_member"));
            }
        }
        return true;
    }
    async check(ids) {
        return await this._check(ids, this.prisma.commune, this.entityType);
    }
    async getOne(id) {
        return await this.prisma.commune.findUnique({
            where: { id },
            include: {
                members: true,
                name: true,
                description: true,
                images: true,
            },
        });
    }
    async getOneOrThrow(id) {
        const commune = await this.getOne(id);
        if (!commune) {
            throw this.createNotFoundException();
        }
        return commune;
    }
    async getMany(where, pagination) {
        return await this.prisma.commune.findMany({
            ...(0, utils_1.toPrismaPagination)(pagination),
            where,
            include: {
                members: true,
                name: true,
                description: true,
                images: true,
            },
        });
    }
    async createOne(data) {
        return await this.prisma.commune.create({
            data,
        });
    }
    async createMany(data) {
        return await this.prisma.commune.createMany({
            data,
        });
    }
    async create(data, user) {
        if (!data.headUserId) {
            data.headUserId = user.id;
        }
        if (data.headUserId !== user.id) {
            if (user.role !== "admin") {
                throw new common_1.ForbiddenException(...(0, errors_1.getError)("head_user_id_mismatch"));
            }
        }
        const commune = await this.prisma.commune.create({
            data: {
                members: {
                    create: {
                        actorType: client_1.CommuneMemberType.user,
                        actorId: data.headUserId,
                        isHead: true,
                    },
                },
                name: {
                    create: data.name.map((item) => ({
                        ...item,
                        key: "name",
                    })),
                },
                description: {
                    create: data.description.map((item) => ({
                        ...item,
                        key: "description",
                    })),
                },
            },
            include: {
                members: true,
                name: true,
                description: true,
                images: true,
            },
        });
        return commune;
    }
    async uploadCommuneImages(communeId, files) {
        await this.getOneOrThrow(communeId);
        const images = await Promise.all(files.map(async (file, index) => {
            const imageUrl = await this.minioService.uploadCommuneImage(file, communeId, index);
            const image = await this.prisma.image.create({
                data: {
                    url: imageUrl,
                },
            });
            return image;
        }));
        await this.prisma.commune.update({
            where: { id: communeId },
            data: {
                images: {
                    connect: images.map((image) => ({ id: image.id })),
                },
            },
        });
        return images;
    }
    async updateOne(id, data) {
        return await this.prisma.commune.update({
            where: { id },
            data,
            include: {
                members: true,
                name: true,
                description: true,
                images: true,
            },
        });
    }
    async getHeadMember(id) {
        return await this.prisma.communeMember.findFirstOrThrow({
            where: {
                communeId: id,
                isHead: true,
                deletedAt: null,
            },
        });
    }
    async update(id, data, user) {
        const commune = await this.getOne(id);
        if (!commune) {
            throw this.createNotFoundException();
        }
        if (user.role !== "admin") {
            const headMember = await this.getHeadMember(id);
            if (headMember.actorType === client_1.CommuneMemberType.user &&
                headMember.actorId !== user.id) {
                throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_head_member"));
            }
        }
        return await this.updateOne(id, data);
    }
    async updateMany(where, data) {
        return await this.prisma.commune.updateMany({ where, data });
    }
    async softDeleteOne(id) {
        return await this.updateOne(id, { deletedAt: new Date() });
    }
    async softDeleteOneCascade(id, user) {
        const commune = await this.getOne(id);
        if (!commune) {
            throw this.createNotFoundException();
        }
        if (user.role !== "admin") {
            const headMember = await this.getHeadMember(id);
            if (headMember.actorType === client_1.CommuneMemberType.user &&
                headMember.actorId !== user.id) {
                throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_head_member"));
            }
        }
        await this.prisma.$transaction(async (trx) => {
            const now = new Date();
            await trx.commune.update({
                where: { id },
                data: { deletedAt: now },
            });
            await trx.communeMember.updateMany({
                where: { communeId: id },
                data: { deletedAt: now },
            });
        });
    }
    async softDeleteMany(where) {
        return await this.updateMany(where, { deletedAt: new Date() });
    }
    async deleteOne(id) {
        return await this.prisma.commune.delete({ where: { id } });
    }
    async deleteMany(where) {
        return await this.prisma.commune.deleteMany({ where });
    }
};
exports.CommuneService = CommuneService;
exports.CommuneService = CommuneService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        minio_service_1.MinioService])
], CommuneService);
//# sourceMappingURL=commune.service.js.map