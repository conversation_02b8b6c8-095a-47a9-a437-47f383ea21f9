import { Mo<PERSON><PERSON> } from "@nestjs/common";
import { JwtModule } from "@nestjs/jwt";
import { ConfigService } from "@nestjs/config";
import { JwtStrategy } from "./jwt.strategy";
import { UserModule } from "src/user/user.module";
import { EmailModule } from "src/email/email.module";
import { AuthService } from "./auth.service";
import { AuthController } from "./http/auth.controller";
import { AuthResolver } from "./gql/auth.resolver";
@Module({
    imports: [
        JwtModule.registerAsync({
            global: true,
            useFactory: (configService: ConfigService) => ({
                secret: configService.get("ACCESS_TOKEN_SECRET"),
                signOptions: {
                    expiresIn: `${configService.get(
                        "ACCESS_TOKEN_EXPIRES_IN_MINUTES",
                    )}m`,
                },
            }),
            inject: [ConfigService],
        }),
        EmailModule,
        UserModule,
    ],
    controllers: [AuthController],
    providers: [AuthResolver, AuthService, JwtStrategy],
})
export class AuthModule {}
