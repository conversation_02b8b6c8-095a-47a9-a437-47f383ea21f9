import {
    createParamDecorator,
    ExecutionContext,
    UnauthorizedException,
} from "@nestjs/common";
import { GQL_CONTEXT_USER_SYMBOL } from "src/consts";
import { CurrentUser } from "../types";
import { GqlExecutionContext } from "@nestjs/graphql";

export const GqlCurrentUser = createParamDecorator(
    (_: unknown, ctx: ExecutionContext) => {
        const context = GqlExecutionContext.create(ctx).getContext();

        const user: CurrentUser | undefined = context.req.user;

        if (!user) {
            throw new UnauthorizedException();
        }

        context.req[GQL_CONTEXT_USER_SYMBOL] = user;

        return user;
    },
);
