.compactNavbar {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  z-index: 1030;
  backdrop-filter: blur(5px);
  background-color: rgba(248, 249, 250, 0.95) !important;
}

.compactNavbar :global(.nav-link) {
  font-size: 1.05rem; /* Increased font size */
  padding: 0.7rem 1rem; /* Increased padding for larger clickable area */
  transition: color 0.2s ease;
  color: #333333; /* Darker color for better contrast */
  font-weight: 500; /* Slightly bolder for better visibility */
  display: block; /* Ensures the entire area is clickable */
}

.compactNavbar :global(.nav-link:hover) {
  color: #0d6efd;
}

.compactNavbar :global(.navbar-brand) {
  display: flex;
  align-items: center;
}

.compactNavbar :global(.navbar-collapse) {
  justify-content: space-between;
}

.compactNavbar :global(.navbar-nav.mx-auto) {
  display: flex;
  justify-content: center;
}

.compactNavbar :global(.nav-item) {
  display: flex;
  align-items: center;
}

.compactNavbar :global(.btn-sm) {
  font-size: 0.95rem;
  padding: 0.5rem 1.1rem;
  font-weight: 500;
  display: inline-block;
}

@media (max-width: 991.98px) {
  .compactNavbar :global(.navbar-collapse) {
    padding: 0.5rem 0;
    flex-direction: column;
  }

  .compactNavbar :global(.navbar-nav) {
    text-align: center;
    margin-bottom: 0.5rem;
  }

  .compactNavbar :global(.nav-item) {
    margin: 0.2rem 0 !important;
  }

  .compactNavbar :global(.navbar-nav.mx-auto) {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
}
