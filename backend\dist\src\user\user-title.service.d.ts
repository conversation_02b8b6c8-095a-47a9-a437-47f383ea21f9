import { Prisma } from "@prisma/client";
import { BaseService } from "src/common/base-service";
import { CurrentUser } from "src/auth/types";
import { PrismaService } from "src/prisma/prisma.service";
export declare class UserTitleService extends BaseService {
    private readonly prisma;
    constructor(prisma: PrismaService);
    update(id: string, data: Prisma.UserTitleUpdateInput, user: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        ownerId: string | null;
        isActive: boolean;
        color: string | null;
    }>;
    canGet(id: string, user: CurrentUser): Promise<true>;
    canChange(id: string, user: CurrentUser): Promise<true>;
    check(ids: string[]): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        ownerId: string | null;
        isActive: boolean;
        color: string | null;
    }[]>;
    getOne(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        ownerId: string | null;
        isActive: boolean;
        color: string | null;
    } | null>;
    getOneOrThrow(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        ownerId: string | null;
        isActive: boolean;
        color: string | null;
    }>;
    getMany(where: Prisma.UserTitleWhereInput, pagination?: {
        page: number;
        size: number;
    }): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        ownerId: string | null;
        isActive: boolean;
        color: string | null;
    }[]>;
    createOne(data: Prisma.UserTitleCreateInput): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        ownerId: string | null;
        isActive: boolean;
        color: string | null;
    }>;
    createMany(data: Prisma.UserTitleCreateManyInput[]): Promise<Prisma.BatchPayload>;
    updateOne(id: string, data: Prisma.UserTitleUpdateInput): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        ownerId: string | null;
        isActive: boolean;
        color: string | null;
    }>;
    updateMany(where: Prisma.UserTitleWhereInput, data: Prisma.UserTitleUpdateInput): Promise<Prisma.BatchPayload>;
    softDeleteOne(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        ownerId: string | null;
        isActive: boolean;
        color: string | null;
    }>;
    softDeleteMany(where: Prisma.UserTitleWhereInput): Promise<Prisma.BatchPayload>;
    deleteOne(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        ownerId: string | null;
        isActive: boolean;
        color: string | null;
    }>;
    deleteMany(where: Prisma.UserTitleWhereInput): Promise<Prisma.BatchPayload>;
}
