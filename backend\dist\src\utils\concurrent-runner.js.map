{"version": 3, "file": "concurrent-runner.js", "sourceRoot": "", "sources": ["../../../src/utils/concurrent-runner.ts"], "names": [], "mappings": ";;;AAGA,MAAa,gBAAgB;IAQzB,MAAM,CAAC,KAAK,CAAC,GAAG,CACZ,KAA8B,EAC9B,gBAAwB,CAAC;QAEzB,MAAM,OAAO,GAAQ,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAC7C,IAAI,YAAY,GAAG,CAAC,CAAC;QAGrB,KAAK,UAAU,WAAW,CAAC,SAAiB;YACxC,OAAO,CAAC,SAAS,CAAC,GAAG,MAAM,KAAK,CAAC,SAAS,CAAE,EAAE,CAAC;YAG/C,IAAI,YAAY,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;gBAC9B,MAAM,SAAS,GAAG,YAAY,EAAE,CAAC;gBACjC,MAAM,WAAW,CAAC,SAAS,CAAC,CAAC;YACjC,CAAC;QACL,CAAC;QAGD,MAAM,YAAY,GAAoB,EAAE,CAAC;QACzC,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;QAE3D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE,CAAC;YACpC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;QACnD,CAAC;QAGD,MAAM,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAEhC,OAAO,OAAO,CAAC;IACnB,CAAC;CACJ;AAvCD,4CAuCC"}