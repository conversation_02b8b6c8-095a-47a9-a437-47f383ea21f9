import * as Gql from "src/graphql";
import { CurrentUser } from "src/auth/types";
import { CommuneService } from "../commune.service";
import { CommuneMemberService } from "../commune-member.service";
import * as Dto from "./dto";
export type ICommuneResolver = Gql.IResolver<"getCommune" | "getCommunes" | "getCommuneMember" | "getCommuneMembers", "createCommune" | "updateCommune" | "deleteCommune" | "createCommuneMember" | "updateCommuneMember" | "deleteCommuneMember">;
export declare class CommuneResolver implements ICommuneResolver {
    private readonly communeService;
    private readonly communeMemberService;
    constructor(communeService: CommuneService, communeMemberService: CommuneMemberService);
    getCommune(id: string): Promise<Gql.Commune>;
    getCommunes(): Promise<Gql.Commune[]>;
    createCommune(input: Dto.CreateCommuneInput, user?: CurrentUser): Promise<Gql.Commune>;
    updateCommune(input: Dto.UpdateCommuneInput, user?: CurrentUser): Promise<Gql.Commune>;
    deleteCommune(id: string, user?: CurrentUser): Promise<boolean>;
    getCommuneMember(id: string): Promise<Gql.CommuneMember>;
    getCommuneMembers(communeId: string): Promise<Gql.CommuneMember[]>;
    createCommuneMember(createCommuneMemberInput: Dto.CreateCommuneMemberInput): Promise<Gql.CommuneMember>;
    updateCommuneMember(updateCommuneMemberInput: Dto.UpdateCommuneMemberInput): Promise<Gql.CommuneMember>;
    deleteCommuneMember(id: string): Promise<boolean>;
}
