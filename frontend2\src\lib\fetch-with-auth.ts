import { goto } from "$app/navigation";

/**
 * A utility function for making HTTP requests with authentication handling
 */
export async function fetchWithAuth(
  input: RequestInfo | URL,
  init?: RequestInit,
): Promise<Response> {
  // Call the native fetch function with the provided parameters
  const response = await fetch(input, init);

  if (response.status !== 401) {
    return response;
  }

  // If the response status is 401 (Unauthorized), try to refresh token
  const refreshResponse = await fetch("/api/auth/refresh");

  if (!refreshResponse.ok) {
    return redirectToLoginPage();
  }

  const newResponse = await fetch(input, init);

  if (newResponse.status === 401) {
    return redirectToLoginPage();
  }

  return newResponse;
}

function redirectToLoginPage(): Promise<never> {
  // Get current path
  const currentPath = window.location.pathname + window.location.search;
  const encodedPath = encodeURIComponent(currentPath);

  // Use SvelteKit's goto for navigation
  goto(`/auth?redirectFrom=${encodedPath}`);

  // Return a promise that never resolves since we're redirecting
  return new Promise(() => {});
}
