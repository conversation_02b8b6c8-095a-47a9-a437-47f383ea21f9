import { Prisma } from "@prisma/client";
import { BaseService } from "src/common/base-service";
import { CurrentUser } from "src/auth/types";
import { PrismaService } from "src/prisma/prisma.service";
import { MinioService, FileInfo } from "src/minio/minio.service";
import { Zod<PERSON>elper } from "src/zod";
export declare class CommuneService extends BaseService {
    private readonly prisma;
    private readonly minioService;
    constructor(prisma: PrismaService, minioService: MinioService);
    canGet(id: string, user: CurrentUser): Promise<true>;
    canChange(id: string, user: CurrentUser): Promise<true>;
    check(ids: string[]): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }[]>;
    getOne(id: string): Promise<({
        images: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        }[];
        name: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
        description: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
        members: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            actorType: import(".prisma/client").$Enums.CommuneMemberType;
            actorId: string;
            isHead: boolean;
            joinedAt: Date;
            leftAt: Date | null;
            communeId: string;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }) | null>;
    getOneOrThrow(id: string): Promise<{
        images: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        }[];
        name: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
        description: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
        members: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            actorType: import(".prisma/client").$Enums.CommuneMemberType;
            actorId: string;
            isHead: boolean;
            joinedAt: Date;
            leftAt: Date | null;
            communeId: string;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    getMany(where: Prisma.CommuneWhereInput, pagination?: {
        page: number;
        size: number;
    }): Promise<({
        images: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        }[];
        name: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
        description: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
        members: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            actorType: import(".prisma/client").$Enums.CommuneMemberType;
            actorId: string;
            isHead: boolean;
            joinedAt: Date;
            leftAt: Date | null;
            communeId: string;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    })[]>;
    createOne(data: Prisma.CommuneCreateInput): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    createMany(data: Prisma.CommuneCreateManyInput[]): Promise<Prisma.BatchPayload>;
    create(data: {
        headUserId?: string;
        name: ZodHelper.Localization[];
        description: ZodHelper.Localization[];
    }, user: CurrentUser): Promise<{
        images: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        }[];
        name: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
        description: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
        members: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            actorType: import(".prisma/client").$Enums.CommuneMemberType;
            actorId: string;
            isHead: boolean;
            joinedAt: Date;
            leftAt: Date | null;
            communeId: string;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    uploadCommuneImages(communeId: string, files: FileInfo[]): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        url: string;
    }[]>;
    updateOne(id: string, data: Prisma.CommuneUpdateInput): Promise<{
        images: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        }[];
        name: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
        description: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
        members: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            actorType: import(".prisma/client").$Enums.CommuneMemberType;
            actorId: string;
            isHead: boolean;
            joinedAt: Date;
            leftAt: Date | null;
            communeId: string;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    getHeadMember(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        actorType: import(".prisma/client").$Enums.CommuneMemberType;
        actorId: string;
        isHead: boolean;
        joinedAt: Date;
        leftAt: Date | null;
        communeId: string;
    }>;
    update(id: string, data: Prisma.CommuneUpdateInput, user: CurrentUser): Promise<{
        images: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        }[];
        name: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
        description: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
        members: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            actorType: import(".prisma/client").$Enums.CommuneMemberType;
            actorId: string;
            isHead: boolean;
            joinedAt: Date;
            leftAt: Date | null;
            communeId: string;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    updateMany(where: Prisma.CommuneWhereInput, data: Prisma.CommuneUpdateInput): Promise<Prisma.BatchPayload>;
    softDeleteOne(id: string): Promise<{
        images: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        }[];
        name: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
        description: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import(".prisma/client").$Enums.Locale;
            value: string;
        }[];
        members: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            actorType: import(".prisma/client").$Enums.CommuneMemberType;
            actorId: string;
            isHead: boolean;
            joinedAt: Date;
            leftAt: Date | null;
            communeId: string;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    softDeleteOneCascade(id: string, user: CurrentUser): Promise<void>;
    softDeleteMany(where: Prisma.CommuneWhereInput): Promise<Prisma.BatchPayload>;
    deleteOne(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    deleteMany(where: Prisma.CommuneWhereInput): Promise<Prisma.BatchPayload>;
}
