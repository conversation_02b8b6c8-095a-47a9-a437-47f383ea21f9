// image
model Image {
    @@map("images")

    id String @id @db.Uuid @default(uuid(7))

    users        User[]        @relation("user_images")
    communes     Commune[]     @relation("commune_images")
    votings      Voting[]      @relation("voting_images")
    posts        Post[]        @relation("post_images")
    merchandises Merchandise[] @relation("merchandise_images")

    url    String

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
    deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)
}
