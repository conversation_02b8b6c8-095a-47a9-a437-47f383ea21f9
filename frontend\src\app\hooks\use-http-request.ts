"use client";

import { useRouter, usePathname, useSearchParams, ReadonlyURLSearchParams } from "next/navigation";
import { useCallback } from "react";

// This hook creates a wrapped variant of doHttpRequest that uses Next.js routing
export function useHttpRequest() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  async function httpRequest(
    input: RequestInfo | URL,
    init?: RequestInit,
  ): Promise<Response> {
    // Call the native fetch function with the provided parameters
    const response = await fetch(input, init);

    if (response.status !== 401) {
      return response;
    }

    // If the response status is 401 (Unauthorized), try to refresh token
    const refreshResponse = await fetch("/api/auth/refresh");

    if (!refreshResponse.ok) {
      return redirectToLoginPage(router, pathname, searchParams);
    }

    const newResponse = await fetch(input, init);

    if (newResponse.status === 401) {
      return redirectToLoginPage(router, pathname, searchParams);
    }

    return newResponse;
  }

  return useCallback(httpRequest, [router, pathname, searchParams]);
}

function redirectToLoginPage(
  router: { push: (path: string) => void },
  pathname: string,
  searchParams: ReadonlyURLSearchParams,
): Promise<never> {
  // Construct current URL from Next.js components
  const query = searchParams.toString();
  const currentPath = query ? `${pathname}?${query}` : pathname;
  const encodedPath = encodeURIComponent(currentPath);

  // Use Next.js router for navigation
  router.push(`/login?redirectFrom=${encodedPath}`);

  // Return a promise that never resolves since we're redirecting
  return new Promise(() => {});
}
