"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Users = exports.User = exports.UserTitle = exports.typename = void 0;
const zod_1 = require("../../zod");
const client_1 = require("@prisma/client");
exports.typename = "User";
exports.UserTitle = zod_1.z.object({
    __typename: zod_1.z.literal("UserTitle").default("UserTitle"),
    id: zod_1.ZodHelper.Uuid,
    color: zod_1.z.string().nullable(),
    isActive: zod_1.z.boolean(),
    createdAt: zod_1.ZodHelper.ToDateTime,
    updatedAt: zod_1.ZodHelper.ToDateTime,
});
exports.User = zod_1.z.object({
    __typename: zod_1.z.literal(exports.typename).default(exports.typename),
    id: zod_1.ZodHelper.Uuid,
    email: zod_1.ZodHelper.Email,
    role: zod_1.z.nativeEnum(client_1.UserRole),
    name: zod_1.ZodHelper.Localizations,
    description: zod_1.ZodHelper.Localizations,
    images: zod_1.z
        .array(zod_1.z.object({
        id: zod_1.ZodHelper.Uuid,
        url: zod_1.z.string(),
    }))
        .optional(),
    titles: zod_1.z.array(exports.UserTitle),
    createdAt: zod_1.ZodHelper.ToDateTime,
    updatedAt: zod_1.ZodHelper.ToDateTime,
});
exports.Users = zod_1.z.array(exports.User);
//# sourceMappingURL=dto.js.map