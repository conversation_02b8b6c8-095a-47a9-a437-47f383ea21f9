.footer {
  background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding-top: 3rem;
  padding-bottom: 1.5rem;
  position: relative;
  overflow: hidden;
  box-shadow: 0 -10px 20px rgba(0, 0, 0, 0.03);
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(to right, #0d6efd, #6610f2, #6f42c1);
  opacity: 0.7;
}

.footer::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23000000' fill-opacity='0.02' fill-rule='evenodd'/%3E%3C/svg%3E");
  opacity: 0.5;
  pointer-events: none;
}

.footerLogo {
  max-width: 80px;
  height: auto;
  margin-bottom: 1rem;
}

.aboutSection {
  padding-right: 2rem;
}

.quickLinks li {
  margin-bottom: 0.5rem;
  transition: transform 0.2s ease;
}

.quickLinks li:hover {
  transform: translateX(5px);
}

.quickLinks a {
  color: #495057;
  transition: color 0.2s ease;
  display: inline-flex;
  align-items: center;
}

.quickLinks a:hover {
  color: #0d6efd;
  text-decoration: none;
}

.quickLinks a::before {
  content: '›';
  margin-right: 0.5rem;
  font-size: 1.2rem;
  line-height: 1;
  opacity: 0.7;
}

.contactInfo li {
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px dashed rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.contactInfo li:last-child {
  border-bottom: none;
}

.contactInfo li:hover {
  transform: translateX(5px);
}

.contactInfo i {
  margin-right: 0.75rem;
  font-size: 1.1rem;
  color: #0d6efd;
  width: 20px;
  text-align: center;
  transition: transform 0.3s ease;
}

.contactInfo li:hover i {
  transform: scale(1.2);
}

.socialIcons {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}

.socialIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 38px;
  height: 38px;
  border-radius: 50%;
  background-color: #f8f9fa;
  color: #495057;
  border: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  font-size: 1.1rem;
}

.socialIcon:hover {
  background-color: #0d6efd;
  color: white;
  transform: translateY(-3px) rotate(8deg);
  box-shadow: 0 6px 12px rgba(13, 110, 253, 0.2);
}

.divider {
  margin: 1rem 0;
  height: 1px;
  background: linear-gradient(to right, transparent, rgba(0, 0, 0, 0.1), transparent);
  border: none;
}

.copyright {
  font-size: 0.9rem;
  color: #6c757d;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  padding-top: 0.5rem;
  position: relative;
}

.copyright::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 2px;
  background: linear-gradient(to right, #0d6efd, #6610f2);
  border-radius: 2px;
}

.copyright p {
  margin-bottom: 0;
  position: relative;
  padding-left: 1.5rem;
  text-align: center;
}

.copyright p::before {
  content: '©';
  position: absolute;
  left: 0;
  top: 0;
  font-size: 1rem;
  color: #0d6efd;
}

.bottomLinks {
  display: flex;
  gap: 1.5rem;
}

.bottomLinks a {
  color: #6c757d;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  position: relative;
  padding: 0.25rem 0;
}

.bottomLinks a::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 1px;
  background-color: #0d6efd;
  transition: width 0.3s ease;
}

.bottomLinks a:hover {
  color: #0d6efd;
}

.bottomLinks a:hover::after {
  width: 100%;
}

@media (max-width: 767.98px) {
  .footer {
    padding-top: 2rem;
    text-align: center;
  }

  .aboutSection {
    padding-right: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .footerLogo {
    margin: 0 auto 1rem;
  }

  .quickLinks {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .quickLinks li {
    text-align: center;
  }

  .quickLinks a {
    justify-content: center;
  }

  .quickLinks a::before {
    position: relative;
    margin-right: 0.5rem;
  }

  .contactInfo {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .contactInfo li {
    justify-content: center;
  }

  .contactInfo i {
    display: inline-block;
    margin-right: 0.75rem;
  }

  .copyright {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
    justify-content: center;
  }

  .copyright p {
    padding-left: 0;
  }

  .copyright p::before {
    position: static;
    margin-right: 0.25rem;
  }

  .bottomLinks {
    justify-content: center;
    flex-wrap: wrap;
  }

  .socialIcons {
    justify-content: center;
  }

  .divider {
    margin: 0;
  }
}
