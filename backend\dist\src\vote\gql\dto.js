"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateVoteInput = exports.Vote = exports.typename = void 0;
const client_1 = require("@prisma/client");
const zod_1 = require("../../zod");
exports.typename = "Vote";
exports.Vote = zod_1.ZodHelper.GqlObject(exports.typename, {
    id: zod_1.ZodHelper.Uuid,
    actorType: zod_1.z.nativeEnum(client_1.VoteActorType),
    actorId: zod_1.ZodHelper.Uuid,
    createdAt: zod_1.ZodHelper.ToDateTime,
    updatedAt: zod_1.ZodHelper.ToDateTime,
});
exports.CreateVoteInput = zod_1.z.object({
    votingId: zod_1.ZodHelper.Uuid,
    optionId: zod_1.ZodHelper.Uuid,
});
//# sourceMappingURL=dto.js.map