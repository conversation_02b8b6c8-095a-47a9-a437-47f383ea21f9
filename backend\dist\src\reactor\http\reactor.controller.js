"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReactorController = void 0;
const common_1 = require("@nestjs/common");
const zod_1 = require("../../zod");
const current_user_decorator_1 = require("../../auth/http/current-user.decorator");
const jwt_auth_guard_1 = require("../../auth/http/jwt-auth.guard");
const reactor_service_1 = require("../reactor.service");
const Dto = require("./dto");
let ReactorController = class ReactorController {
    constructor(reactorService) {
        this.reactorService = reactorService;
    }
    async getPosts(page, size, user) {
        const response = await this.reactorService.getPosts({ page, size }, user);
        return Dto.GetPostsResponse.parse(response);
    }
    async getPost(id, user) {
        const post = await this.reactorService.getPost(id, user);
        return Dto.Post.parse(post);
    }
    async createPost(body, user) {
        return this.reactorService.createPost(body, user);
    }
    async updatePost(id, body, user) {
        return this.reactorService.updatePost(id, body, user);
    }
    async updatePostRating(id, body, user) {
        return this.reactorService.updatePostRating(id, body, user);
    }
    async updatePostUsefulness(id, body, user) {
        return this.reactorService.updatePostUsefulness(id, body, user);
    }
    async deletePost(id, body, user) {
        return this.reactorService.deletePost(id, body, user);
    }
    async getComments(entityType, entityId, user) {
        const response = await this.reactorService.getComments({ entityType, entityId }, user);
        return Dto.GetCommentsResponse.parse(response);
    }
    async getComment(id, user) {
        const comment = await this.reactorService.getComment({ id }, user);
        return Dto.Comment.parse(comment);
    }
    async createComment(body, user) {
        return this.reactorService.createComment(body, user);
    }
    async updateComment(id, body, user) {
        return this.reactorService.updateComment(id, body, user);
    }
    async updateCommentRating(id, body, user) {
        return this.reactorService.updateCommentRating(id, body, user);
    }
    async anonimifyComment(id, body, user) {
        return this.reactorService.anonimifyComment(id, body, user);
    }
    async deleteComment(id, body, user) {
        return this.reactorService.deleteComment(id, body, user);
    }
};
exports.ReactorController = ReactorController;
__decorate([
    (0, common_1.Get)("post"),
    __param(0, (0, common_1.Query)("page", new zod_1.ZodPipe(zod_1.ZodHelper.pagination.page))),
    __param(1, (0, common_1.Query)("size", new zod_1.ZodPipe(zod_1.ZodHelper.pagination.size))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "getPosts", null);
__decorate([
    (0, common_1.Get)("post/:id"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(zod_1.ZodHelper.Uuid))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "getPost", null);
__decorate([
    (0, common_1.Post)("post"),
    __param(0, (0, common_1.Body)(new zod_1.ZodPipe(Dto.CreatePost))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "createPost", null);
__decorate([
    (0, common_1.Put)("post/:id"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(zod_1.ZodHelper.Uuid))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(Dto.UpdatePost))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "updatePost", null);
__decorate([
    (0, common_1.Post)("post/:id/rating"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(zod_1.ZodHelper.Uuid))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(Dto.UpdatePostRating))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "updatePostRating", null);
__decorate([
    (0, common_1.Post)("post/:id/usefulness"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(zod_1.ZodHelper.Uuid))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(Dto.UpdatePostUsefulness))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "updatePostUsefulness", null);
__decorate([
    (0, common_1.Delete)("post/:id"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(zod_1.ZodHelper.Uuid))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(Dto.DeletePost))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "deletePost", null);
__decorate([
    (0, common_1.Get)("comment"),
    __param(0, (0, common_1.Query)("entityType", new zod_1.ZodPipe(Dto.GetCommentsEntityType))),
    __param(1, (0, common_1.Query)("entityId", new zod_1.ZodPipe(zod_1.ZodHelper.Uuid))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "getComments", null);
__decorate([
    (0, common_1.Get)("comment/:id"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(zod_1.ZodHelper.Uuid))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "getComment", null);
__decorate([
    (0, common_1.Post)("comment"),
    __param(0, (0, common_1.Body)(new zod_1.ZodPipe(Dto.CreateComment))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "createComment", null);
__decorate([
    (0, common_1.Put)("comment/:id"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(zod_1.ZodHelper.Uuid))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(Dto.UpdateComment))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "updateComment", null);
__decorate([
    (0, common_1.Post)("comment/:id/rating"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(zod_1.ZodHelper.Uuid))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(Dto.UpdateCommentRating))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "updateCommentRating", null);
__decorate([
    (0, common_1.Put)("comment/:id/anonimify"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(zod_1.ZodHelper.Uuid))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(Dto.AnonimifyComment))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "anonimifyComment", null);
__decorate([
    (0, common_1.Delete)("comment/:id"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(zod_1.ZodHelper.Uuid))),
    __param(1, (0, common_1.Body)(new zod_1.ZodPipe(Dto.DeleteComment))),
    __param(2, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "deleteComment", null);
exports.ReactorController = ReactorController = __decorate([
    (0, common_1.Controller)("reactor"),
    (0, common_1.UseGuards)(jwt_auth_guard_1.HttpJwtAuthGuard),
    __metadata("design:paramtypes", [reactor_service_1.ReactorService])
], ReactorController);
//# sourceMappingURL=reactor.controller.js.map