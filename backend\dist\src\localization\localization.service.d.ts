import { PrismaService } from "src/prisma/prisma.service";
import { Prisma, Localization } from "@prisma/client";
import { <PERSON>od<PERSON><PERSON><PERSON> } from "src/zod";
export declare class LocalizationService {
    private readonly prisma;
    constructor(prisma: PrismaService);
    protected getFirstNonNullValue(localizations: Normalize<Pick<Localization, "locale" | "value">>[], locales: ZodHelper.Locale[]): string | null;
    getOne(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        key: string;
        locale: import(".prisma/client").$Enums.Locale;
        value: string;
    } | null>;
    getMany(where: Prisma.LocalizationWhereInput, pagination?: {
        page: number;
        size: number;
    }): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        key: string;
        locale: import(".prisma/client").$Enums.Locale;
        value: string;
    }[]>;
    createOne(data: Prisma.LocalizationCreateInput): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        key: string;
        locale: import(".prisma/client").$Enums.Locale;
        value: string;
    }>;
    createMany(data: Prisma.LocalizationCreateManyInput[]): Promise<Prisma.BatchPayload>;
    updateOne(id: string, data: Prisma.LocalizationUpdateInput): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        key: string;
        locale: import(".prisma/client").$Enums.Locale;
        value: string;
    }>;
    updateMany(where: Prisma.LocalizationWhereInput, data: Prisma.LocalizationUpdateInput): Promise<Prisma.BatchPayload>;
    softDeleteOne(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        key: string;
        locale: import(".prisma/client").$Enums.Locale;
        value: string;
    }>;
    softDeleteMany(where: Prisma.LocalizationWhereInput): Promise<Prisma.BatchPayload>;
    deleteOne(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        key: string;
        locale: import(".prisma/client").$Enums.Locale;
        value: string;
    }>;
    deleteMany(where: Prisma.LocalizationWhereInput): Promise<Prisma.BatchPayload>;
}
