import { HeroSection } from "./components/hero-section";
import { InitiativesSection } from "./components/initiatives-section";
import { AboutUsSection } from "./components/about-us-section";
import { getLocale } from "@/utils/get-translation";

export default async function HomePage() {
  const locale = await getLocale();

  return (
    <main>
      <HeroSection locale={locale} />
      <AboutUsSection locale={locale} />
      {/* <RoadmapSection /> */}
      {/* <AchievementsSection /> */}
      <InitiativesSection locale={locale} />

      {/* Latest News */}
      {/* <section className="py-5 py-lg-6 bg-white">
        <div className="container">
          <div className="mb-5">
            <h2 className="display-6 fw-bold mb-2">
              Latest News & Initiatives
            </h2>

            <p className="text-secondary col-lg-8">
              Stay updated with our recent activities, breakthroughs, and upcoming events.
            </p>
          </div>

          <div className="row g-4">
            <div className="col-md-4">
              <div className="card h-100 border-0 shadow-sm hover-shadow">
                <div className="position-relative" style={{ height: "200px" }}>
                  <Image
                    src="/images/annual-summit-2025.jpg"
                    alt="Annual Summit 2025"
                    fill
                    className="card-img-top object-fit-cover"
                  />
                </div>

                <div className="card-body">
                  <span className="badge bg-primary-subtle text-primary rounded-pill mb-3">
                    Event
                  </span>

                  <h3 className="card-title h5 fw-bold">
                    Annual Summit 2025
                  </h3>

                  <p className="card-text text-secondary mb-3">
                    Successfully hosted our global partners conference with over 50 participating organizations.
                  </p>

                  <a href="#" className="text-primary fw-semibold text-decoration-none d-inline-flex align-items-center">
                    Read more
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" className="bi bi-arrow-right ms-2" viewBox="0 0 16 16">
                      <path fillRule="evenodd" d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z" />
                    </svg>
                  </a>
                </div>
              </div>
            </div>

            <div className="col-md-4">
              <div className="card h-100 border-0 shadow-sm hover-shadow">
                <div className="position-relative" style={{ height: "200px" }}>
                  <Image
                    src="/images/sustainable-development-program.jpg"
                    alt="Sustainable Development Program"
                    fill
                    className="card-img-top object-fit-cover"
                  />
                </div>

                <div className="card-body">
                  <span className="badge bg-success-subtle text-success rounded-pill mb-3">
                    Initiative
                  </span>

                  <h3 className="card-title h5 fw-bold">
                    Sustainable Development Program
                  </h3>

                  <p className="card-text text-secondary mb-3">
                    Introducing our comprehensive sustainable development program across 3 continents with local partnerships.
                  </p>

                  <a href="#" className="text-primary fw-semibold text-decoration-none d-inline-flex align-items-center">
                    Read more
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" className="bi bi-arrow-right ms-2" viewBox="0 0 16 16">
                      <path fillRule="evenodd" d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z" />
                    </svg>
                  </a>
                </div>
              </div>
            </div>

            <div className="col-md-4">
              <div className="card h-100 border-0 shadow-sm hover-shadow">
                <div className="position-relative" style={{ height: "200px" }}>
                  <Image
                    src="/images/strategic-alliance-with-unicef.jpg"
                    alt="Strategic Alliance with UNICEF"
                    fill
                    className="card-img-top object-fit-cover"
                  />
                </div>

                <div className="card-body">
                  <span className="badge bg-warning-subtle text-warning rounded-pill mb-3">
                    Partnership
                  </span>

                  <h3 className="card-title h5 fw-bold">
                    Strategic Alliance with UNICEF
                  </h3>

                  <p className="card-text text-secondary mb-3">
                    Our new partnership will focus on child health and education initiatives in developing regions.
                  </p>

                  <a href="#" className="text-primary fw-semibold text-decoration-none d-inline-flex align-items-center">
                    Read more
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" className="bi bi-arrow-right ms-2" viewBox="0 0 16 16">
                      <path fillRule="evenodd" d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z" />
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section> */}

      {/* Who We Are */}
      {/* <section className="py-5 py-lg-6 bg-light bg-gradient">
        <div className="container">
          <div className="row g-5 align-items-center">
            <div className="col-lg-6">
              <div className="position-relative rounded-4 overflow-hidden shadow" style={{ height: "400px" }}>
                <Image
                  src="/images/who-we-are.jpg"
                  alt="Our team collaborating"
                  fill
                  className="object-fit-cover"
                />
              </div>
            </div>

            <div className="col-lg-6">
              <h2 className="display-6 fw-bold mb-4" id="about">
                Who We Are
              </h2>

              <p className="fs-5 mb-3">
                We are a dedicated organization committed to fostering international cooperation and sustainable development through innovative solutions and partnerships.
              </p>

              <p className="mb-3">
                For over a decade, we&apos;ve worked at the intersection of policy, technology, and community engagement to create lasting positive change across the globe. Our interdisciplinary team brings together expertise from diverse fields with a shared commitment to building a more equitable and sustainable world.
              </p>

              <p className="mb-4">
                Through strategic partnerships with governments, NGOs, and the private sector, we design and implement programs that address critical challenges in healthcare, education, sustainable agriculture, and clean energy.
              </p>

              <a
                href="#team"
                className="btn btn-primary px-4 py-2 d-inline-flex align-items-center"
              >
                Meet Our Team
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" className="bi bi-arrow-right ms-2" viewBox="0 0 16 16">
                  <path fillRule="evenodd" d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z" />
                </svg>
              </a>
            </div>
          </div>
        </div>
      </section> */}

      {/* Our Projects */}
      {/* <section className="py-5 py-lg-6 bg-white" id="projects">
        <div className="container">
          <div className="text-center mb-5">
            <h2 className="display-6 fw-bold mb-2">
              Our Key Initiatives
            </h2>

            <p className="text-secondary mx-auto" style={{ maxWidth: "700px" }}>
              Impactful projects driving positive change across communities worldwide
            </p>
          </div>

          <div className="row g-4">
            <div className="col-md-6 col-lg-3">
              <div className="card h-100 border-0 bg-success-subtle rounded-4 hover-shadow">
                <div className="card-body p-4">
                  <div className="d-inline-flex align-items-center justify-content-center bg-success-subtle rounded-circle mb-3" style={{ width: "60px", height: "60px" }}>
                    <span className="fs-2">🌱</span>
                  </div>

                  <h3 className="card-title h5 fw-bold">
                    Sustainable Agriculture
                  </h3>

                  <p className="card-text mb-3">
                    Empowering farmers with regenerative practices, climate resilience training, and market access.
                  </p>

                  <a href="#" className="text-success fw-semibold text-decoration-none d-inline-flex align-items-center">
                    Learn more
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" className="bi bi-arrow-right ms-2" viewBox="0 0 16 16">
                      <path fillRule="evenodd" d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z" />
                    </svg>
                  </a>
                </div>
              </div>
            </div>

            <div className="col-md-6 col-lg-3">
              <div className="card h-100 border-0 bg-warning-subtle rounded-4 hover-shadow">
                <div className="card-body p-4">
                  <div className="d-inline-flex align-items-center justify-content-center bg-warning-subtle rounded-circle mb-3" style={{ width: "60px", height: "60px" }}>
                    <span className="fs-2">💡</span>
                  </div>

                  <h3 className="card-title h5 fw-bold">
                    Clean Energy Solutions
                  </h3>

                  <p className="card-text mb-3">
                    Deploying renewable energy technologies and building capacity for sustainable power systems.
                  </p>

                  <a href="#" className="text-warning-emphasis fw-semibold text-decoration-none d-inline-flex align-items-center">
                    Learn more
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" className="bi bi-arrow-right ms-2" viewBox="0 0 16 16">
                      <path fillRule="evenodd" d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z" />
                    </svg>
                  </a>
                </div>
              </div>
            </div>

            <div className="col-md-6 col-lg-3">
              <div className="card h-100 border-0 bg-primary-subtle rounded-4 hover-shadow">
                <div className="card-body p-4">
                  <div className="d-inline-flex align-items-center justify-content-center bg-primary-subtle rounded-circle mb-3" style={{ width: "60px", height: "60px" }}>
                    <span className="fs-2">📚</span>
                  </div>

                  <h3 className="card-title h5 fw-bold">
                    Global Education Access
                  </h3>

                  <p className="card-text mb-3">
                    Expanding quality education opportunities through digital platforms, teacher training, and school infrastructure.
                  </p>

                  <a href="#" className="text-primary fw-semibold text-decoration-none d-inline-flex align-items-center">
                    Learn more
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" className="bi bi-arrow-right ms-2" viewBox="0 0 16 16">
                      <path fillRule="evenodd" d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z" />
                    </svg>
                  </a>
                </div>
              </div>
            </div>

            <div className="col-md-6 col-lg-3">
              <div className="card h-100 border-0 bg-danger-subtle rounded-4 hover-shadow">
                <div className="card-body p-4">
                  <div className="d-inline-flex align-items-center justify-content-center bg-danger-subtle rounded-circle mb-3" style={{ width: "60px", height: "60px" }}>
                    <span className="fs-2">🏥</span>
                  </div>

                  <h3 className="card-title h5 fw-bold">
                    Healthcare Innovation
                  </h3>

                  <p className="card-text mb-3">
                    Developing solutions for healthcare access, preventive care, and medical technology in underserved regions.
                  </p>

                  <a href="#" className="text-danger fw-semibold text-decoration-none d-inline-flex align-items-center">
                    Learn more
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" className="bi bi-arrow-right ms-2" viewBox="0 0 16 16">
                      <path fillRule="evenodd" d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z" />
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section> */}

      {/* Partners */}
      {/* <section className="py-5 py-lg-6 bg-light">
        <div className="container">
          <div className="text-center mb-5">
            <h2 className="display-6 fw-bold mb-2">
              Our Global Partners
            </h2>

            <p className="text-secondary mx-auto" style={{ maxWidth: "700px" }}>
              Collaborating with leading organizations to maximize our impact
            </p>
          </div>

          <div className="row row-cols-2 row-cols-md-3 row-cols-lg-6 g-4">
            {[
              { icon: "🌐", name: "United Nations", color: "primary" },
              { icon: "🏥", name: "World Health Organization", color: "info" },
              { icon: "🏦", name: "World Bank Group", color: "secondary" },
              { icon: "👶", name: "UNICEF", color: "info" },
              { icon: "🛠", name: "Fourth International", color: "danger" },
              { icon: "🍽️", name: "World Food Program", color: "warning" },
            ].map((partner, index) => (
              <div key={index} className="col">
                <div className={`card h-100 border-0 shadow-sm hover-shadow text-center border-top border-${partner.color} border-4 rounded-3`}>
                  <div className="card-body py-4">
                    <div className="fs-1 mb-3">
                      {partner.icon}
                    </div>

                    <h3 className="card-title h6 fw-semibold">
                      {partner.name}
                    </h3>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section> */}

      {/* CTA */}
      {/* <section className="py-5 py-lg-6 bg-primary text-white bg-gradient" id="contact">
        <div className="container text-center">
          <h2 className="display-5 fw-bold mb-4">
            Join Our Mission for Global Change
          </h2>

          <p className="lead text-white-50 mx-auto mb-5" style={{ maxWidth: "700px" }}>
            Together, we can create meaningful impact and build a more sustainable, equitable future for all.
          </p>

          <div className="d-flex flex-column flex-md-row gap-3 justify-content-center">
            <a
              href="#volunteer"
              className="btn btn-light btn-lg px-4 py-3 text-primary fw-semibold shadow-sm"
            >
              Volunteer With Us
            </a>

            <a
              href="#donate"
              className="btn btn-outline-light btn-lg px-4 py-3 fw-semibold"
            >
              Support Our Work
            </a>
          </div>
        </div>
      </section> */}
    </main>
  );
}
