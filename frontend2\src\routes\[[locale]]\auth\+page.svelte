<script lang="ts">
  import { setCurrentUser, User } from "$lib";

  import { dev } from "$app/environment";
  import { goto } from "$app/navigation";
  import { page } from "$app/state";
  import superagent from "superagent";

  const enum OtpStatus {
    None = "none",
    Pending = "pending",
    Sent = "sent",
    SendingDisabledByServer = "sending-disabled-by-server",
    Error = "error",
  }

  const enum SubmitStatus {
    None = "none",
    Pending = "pending",
    Error = "error",
  }

  let activeTab = $state<"login" | "register">("login");
  let email = $state(dev ? "<EMAIL>" : "");
  let otp = $state(dev ? "123456" : "");
  let otpStatus = $state<OtpStatus>(OtpStatus.None);
  let submitStatus = $state<SubmitStatus>(SubmitStatus.None);
  let submitErrorMessage = $state<string | null>(null);

  const isSubmitting = $derived(
    otpStatus === OtpStatus.Pending || submitStatus === SubmitStatus.Pending,
  );

  const handleSendOtp = async (e: Event) => {
    e.preventDefault();

    if (!email) return;

    otpStatus = OtpStatus.Pending;

    try {
      const { isSent } = await superagent
        .post("/api/auth/otp")
        .send({ email })
        .then<{ isSent: boolean }>((r) => r.body);

      otpStatus = isSent ? OtpStatus.Sent : OtpStatus.SendingDisabledByServer;
    } catch (error) {
      console.error("Failed to send OTP:", error);

      otpStatus = OtpStatus.Error;
    }
  };

  const handleSubmit = async (e: Event) => {
    e.preventDefault();

    if (!email || !otp) return;

    submitStatus = SubmitStatus.Pending;

    try {
      if (activeTab === "login") {
        const response = await superagent.post("/api/auth/login").send({ email, otp });

        if (response.status === 200) {
          setCurrentUser(User.parse(response.body));

          goto(page.url.searchParams.get("redirectFrom") ?? "/");
        } else {
          throw new Error("Failed to login", { cause: response.body });
        }
      } else {
        const response = await superagent.post("/api/auth/register").send({ email, otp });

        if (response.status === 200) {
          setCurrentUser(User.parse(response.body));

          goto(page.url.searchParams.get("redirectFrom") ?? "/");
        } else {
          throw new Error("Failed to register", { cause: response.body });
        }
      }
    } catch (error) {
      console.error(error);

      // Handle 401 response or other errors
      const errorMessage = (error as any).response?.body?.error || (error as Error).message;

      submitStatus = SubmitStatus.Error;
      submitErrorMessage = errorMessage;
    }
  };
</script>

<div class="container min-vh-100 d-flex align-items-center justify-content-center">
  <div class="card shadow-lg border-0" style:width="100%" style:max-width="400px">
    <div class="card-header bg-white border-0 pt-4 pb-0">
      <div class="position-relative">
        <ul class="nav nav-tabs border-0 card-header-tabs">
          <li class="nav-item flex-grow-1 text-center">
            <button
              class={`nav-link border-0 w-100 ${activeTab === "login" ? "active" : ""}`}
              onclick={() => (activeTab = "login")}
            >
              Login
            </button>
          </li>
          <li class="nav-item flex-grow-1 text-center">
            <button
              class={`nav-link border-0 w-100 ${activeTab === "register" ? "active" : ""}`}
              onclick={() => (activeTab = "register")}
            >
              Register
            </button>
          </li>
        </ul>
        <div
          class="position-absolute bottom-0 bg-primary"
          style:height="3px"
          style:width="50%"
          style:left={activeTab === "login" ? "0" : "50%"}
          style:transition="left 0.3s ease-in-out"
          style:borderRadius="3px 3px 0 0"
        ></div>
      </div>
    </div>
    <div class="card-body p-4">
      <h5
        class="card-title mb-4"
        style:transition="opacity 0.3s ease-in-out"
        style:animation="fadeIn 0.5s"
      >
        {activeTab === "login" ? "Welcome Back!" : "Create an Account"}
      </h5>
      <form onsubmit={handleSubmit}>
        <div class="mb-3">
          <label for="email" class="form-label">Email address</label>
          <input
            type="email"
            autoComplete="email"
            class="form-control"
            id="email"
            value={email}
            oninput={(e) => (email = (e.target as HTMLInputElement).value)}
            placeholder="<EMAIL>"
            required
          />
        </div>
        <div class="mb-3">
          <button
            type="button"
            class="btn btn-outline-primary w-100"
            onclick={handleSendOtp}
            disabled={!email || isSubmitting}
          >
            {#if otpStatus === OtpStatus.Pending}
              <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"
              ></span>
            {/if}

            Send OTP
          </button>
        </div>
        <div class="mb-3">
          <div class="d-flex justify-content-between align-items-center mb-2">
            <label for="otp" class="form-label mb-0">OTP Verification Code</label>

            {#if otpStatus === OtpStatus.Sent}
              <span class="badge bg-success-subtle text-success px-2 py-1 rounded-pill">
                OTP Sent
              </span>
            {:else if otpStatus === OtpStatus.SendingDisabledByServer}
              <span class="badge bg-warning-subtle text-warning px-2 py-1 rounded-pill">
                OTP sending is disabled by the server
              </span>
            {/if}
          </div>
          <input
            id="otp"
            class="form-control"
            type="text"
            value={otp}
            oninput={(e) => {
              // Only allow numbers and limit to 6 digits
              const value = (e.target as HTMLInputElement).value.replace(/\D/g, "");
              if (value.length <= 6) otp = value;
            }}
            placeholder="6-digit code"
            maxLength={6}
            aria-describedby="otpHelp"
          />
          <div id="otpHelp" class="form-text">Enter the 6-digit code sent to your email.</div>
        </div>
        <div class="d-grid gap-2">
          <button type="submit" class="btn btn-primary" disabled={!email || !otp || isSubmitting}>
            {#if submitStatus === SubmitStatus.Pending && otp}
              <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"
              ></span>
            {/if}

            {#if activeTab === "login"}
              Log In
            {:else}
              Create Account
            {/if}
          </button>

          {#if submitStatus === SubmitStatus.Error}
            <span class="text-danger">
              Failed to submit. Please try again.
              <br />
              {submitErrorMessage}
            </span>
          {/if}
        </div>
      </form>
    </div>
  </div>
</div>

<style>
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .nav-link {
    padding: 0.75rem 1.25rem;
    font-weight: 500;
    color: #6c757d;
    transition: all 0.3s ease;
  }

  .nav-link.active {
    color: #0d6efd;
    font-weight: 600;
    background-color: transparent;
  }

  .nav-link:hover:not(.active) {
    color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.05);
  }
</style>
