"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HttpJwtAuthGuard = void 0;
const passport_1 = require("@nestjs/passport");
class HttpJwtAuthGuard extends (0, passport_1.AuthGuard)("jwt") {
    getRequest(context) {
        const request = context.switchToHttp().getRequest();
        return request;
    }
    canActivate(context) {
        return super.canActivate(context);
    }
}
exports.HttpJwtAuthGuard = HttpJwtAuthGuard;
//# sourceMappingURL=jwt-auth.guard.js.map