"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailOtpService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const zod_1 = require("zod");
const errors_1 = require("../common/errors");
const prisma_service_1 = require("../prisma/prisma.service");
let EmailOtpService = class EmailOtpService {
    constructor(configService, prisma) {
        this.configService = configService;
        this.prisma = prisma;
    }
    async create(createUserOtpDto) {
        const expirationTime = zod_1.z.coerce
            .number()
            .int()
            .positive()
            .parse(this.configService.get("OTP_EXPIRATION_TIME_MS"));
        const otp = await this.prisma.userOtp.create({
            data: {
                email: createUserOtpDto.email,
                otp: createUserOtpDto.otp,
                ipAddress: createUserOtpDto.ipAddress,
                userAgent: createUserOtpDto.userAgent,
                expiresAt: new Date(Date.now() + expirationTime),
            },
        });
        return otp;
    }
    async check(checkUserOtpDto) {
        const where = {
            email: checkUserOtpDto.email,
            otp: checkUserOtpDto.otp,
            expiresAt: {
                gt: new Date(),
            },
            deletedAt: null,
        };
        const otp = await this.prisma.userOtp.findFirst({
            where: where,
        });
        if (!otp) {
            throw new common_1.UnauthorizedException(...(0, errors_1.getError)("otp_invalid"));
        }
        return otp;
    }
    async softDelete(deleteUserOtpDto) {
        await this.prisma.userOtp.update({
            where: {
                id: deleteUserOtpDto.id,
            },
            data: {
                deletedAt: new Date(),
            },
        });
    }
    async delete(deleteUserOtpDto) {
        await this.prisma.userOtp.delete({
            where: {
                id: deleteUserOtpDto.id,
            },
        });
    }
};
exports.EmailOtpService = EmailOtpService;
exports.EmailOtpService = EmailOtpService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        prisma_service_1.PrismaService])
], EmailOtpService);
//# sourceMappingURL=email-otp.service.js.map