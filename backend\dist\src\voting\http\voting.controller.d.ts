import { VotingService } from "../voting.service";
import * as Dto from "./dto";
export declare class VotingController {
    private readonly votingService;
    constructor(votingService: VotingService);
    getVotings(page: number, size: number): Promise<Dto.Voting[]>;
    createVoting(body: Dto.CreateVoting): Promise<Dto.Voting>;
    getVoting(id: string): Promise<Dto.Voting>;
    deleteVoting(id: string): Promise<void>;
}
