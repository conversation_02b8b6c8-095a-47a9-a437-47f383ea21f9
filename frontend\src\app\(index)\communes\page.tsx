"use client";

import { useEffect, useState } from "react";
import { Con<PERSON><PERSON>, <PERSON>, Col, Card, Pagination, Badge, Button } from "react-bootstrap";
import { useHttpRequest } from "@/app/hooks/use-http-request";
import { findLocalizationForUserLocales, Localization } from "@/utils/find-localization";
import { CreateCommuneModal } from "./create-commune-modal";
import { CommuneImageCarousel } from "./commune-image-carousel";
import Link from "next/link";
import { useLocale } from "next-intl";
import { Locale } from "@/app/types";

type CommuneMemberType = "user" | "commune";

interface Commune {
  id: string;
  name: Localization[];
  description: Localization[];
  memberCount: number;
  headMember: {
    actorType: CommuneMemberType;
    actorId: string;
    name: Localization[];
  };
  images?: {
    id: string;
    url: string;
    source: string;
  }[];
}

const i18n = {
  en: {
    communes: "Communes",
    create: "Create",
    loading: "Loading...",
    noCommunes: "No communes found",
    noDescription: "No description",
    member: "member",
    members: "members",
    headMember: "Head Member",
    errorFetchingCommunes: "Failed to fetch communes",
    errorOccurred: "An error occurred while fetching communes",
  },

  ru: {
    communes: "Коммуны",
    create: "Создать",
    loading: "Загрузка...",
    noCommunes: "Коммуны не найдены",
    noDescription: "Нет описания",
    member: "участник",
    members: "участников",
    headMember: "Глава",
    errorFetchingCommunes: "Не удалось загрузить коммуны",
    errorOccurred: "Произошла ошибка при загрузке коммун",
  },
};

export default function Communes() {
  const locale = useLocale() as Locale;
  const t = i18n[locale];

  const [communes, setCommunes] = useState<Commune[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const pageSize = 20;

  // State for the create commune modal
  const [showCreateModal, setShowCreateModal] = useState(false);

  const httpRequest = useHttpRequest();

  useEffect(() => {
    const fetchCommunes = async () => {
      setLoading(true);
      try {
        const response = await httpRequest(`/api/commune?page=${currentPage}&size=${pageSize}`);

        if (!response.ok) {
          throw new Error(`${t.errorFetchingCommunes}: ${response.statusText}`);
        }

        const data: Commune[] = await response.json();
        setCommunes(data);

        // Set total pages based on data length
        setTotalPages(Math.ceil(data.length / pageSize) || 1);
      } catch (err) {
        setError(err instanceof Error ? err.message : t.errorOccurred);
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchCommunes();
  }, [currentPage]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleCreateModalOpen = () => {
    setShowCreateModal(true);
  };

  const handleCreateModalClose = () => {
    setShowCreateModal(false);
  };

  return (
    <Container className="my-4 mb-5">
      <div className="d-flex justify-content-between align-items-center my-4">
        <h1>{t.communes}</h1>
        <Button variant="primary" onClick={handleCreateModalOpen}>
          {t.create}
        </Button>
      </div>

      {loading ? (
        <div className="text-center py-5">
          <div className="spinner-border" role="status">
            <span className="visually-hidden">{t.loading}</span>
          </div>
        </div>
      ) : error ? (
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
      ) : (
        <>
          {communes.length === 0 ? (
            <div className="text-center py-5">
              <p className="text-muted">{t.noCommunes}</p>
            </div>
          ) : (
            <Row xs={1} sm={2} md={3} lg={5} className="g-4">
              {communes.map((commune) => (
                <Col key={commune.id}>
                  <Card
                    className="h-100 commune-card shadow-sm"
                    style={{ cursor: "pointer", transition: "transform 0.2s" }}
                    onMouseOver={(e) => (e.currentTarget.style.transform = "translateY(-5px)")}
                    onMouseOut={(e) => (e.currentTarget.style.transform = "translateY(0)")}
                  >
                    {/* Commune image carousel */}
                    <CommuneImageCarousel
                      images={commune.images ?? []}
                      communeId={commune.id}
                    />

                    <Link href={`/communes/${commune.id}`} className="text-decoration-none text-black">
                      <Card.Body className="d-flex flex-column">
                        <Card.Title className="fs-5 text-truncate">
                          {findLocalizationForUserLocales(commune.name)}
                        </Card.Title>

                        <Card.Text className="text-muted small" style={{ height: "3rem", overflow: "hidden" }}>
                          {findLocalizationForUserLocales(commune.description) || t.noDescription}
                        </Card.Text>

                        <div className="mt-auto">
                          <Badge bg="primary" className="mb-2">
                            {commune.memberCount} {commune.memberCount === 1 ? t.member : t.members}
                          </Badge>

                          <div className="small text-muted">
                            <div>{t.headMember} ({ commune.headMember.actorType }):</div>
                            <div className="d-flex flex-column">
                              {findLocalizationForUserLocales(commune.headMember.name)}
                            </div>
                          </div>
                        </div>
                      </Card.Body>
                    </Link>
                  </Card>
                </Col>
              ))}
            </Row>
          )}

          {totalPages > 1 && (
            <div className="d-flex justify-content-center mt-4">
              <Pagination>
                <Pagination.First
                  onClick={() => handlePageChange(1)}
                  disabled={currentPage === 1}
                />
                <Pagination.Prev
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                />

                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  // Show pagination centered on current page
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else {
                    const start = Math.max(1, Math.min(currentPage - 2, totalPages - 4));
                    pageNum = start + i;
                  }

                  return (
                    <Pagination.Item
                      key={pageNum}
                      active={pageNum === currentPage}
                      onClick={() => handlePageChange(pageNum)}
                    >
                      {pageNum}
                    </Pagination.Item>
                  );
                })}

                <Pagination.Next
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                />
                <Pagination.Last
                  onClick={() => handlePageChange(totalPages)}
                  disabled={currentPage === totalPages}
                />
              </Pagination>
            </div>
          )}
        </>
      )}

      {/* Use the separated CreateCommuneModal component */}
      <CreateCommuneModal
        show={showCreateModal}
        onHide={handleCreateModalClose}
      />
    </Container>
  );
}
