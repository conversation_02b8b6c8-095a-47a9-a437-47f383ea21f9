.carouselContainer {
  position: relative;
  overflow: hidden;
  height: 140px;
}

.carousel {
  height: 100%;
}

.carouselItem {
  height: 140px;
}

.imageContainer {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
}

.carouselImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Semi-transparent navigation arrows */
.carousel :global(.carousel-control-prev),
.carousel :global(.carousel-control-next) {
  background-color: rgba(0, 0, 0, 0.3);
  width: 30px;
  height: 30px;
  border-radius: 50%;
  top: 50%;
  transform: translateY(-50%);
  margin: 0 10px;
}

/* Indicator dots */
.carousel :global(.carousel-indicators) {
  margin-bottom: 0.25rem;
}

.carousel :global(.carousel-indicators button) {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  margin: 0 3px;
}

.carousel :global(.carousel-indicators button.active) {
  background-color: white;
}
