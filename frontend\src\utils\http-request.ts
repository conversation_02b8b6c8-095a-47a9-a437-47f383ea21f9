// Optional standalone version for when hooks aren't available
// Note: This won't have i18n support and other Next.js features
export async function doHttpRequest(
  input: RequestInfo | URL,
  init?: RequestInit,
): Promise<Response> {
  const response = await fetch(input, init);

  if (response.status === 401) {
    // Fallback to non-Next.js approach when used outside of component context
    const currentUrl = typeof window !== "undefined" ? window.location.pathname + window.location.search : "/";
    const encodedUrl = encodeURIComponent(currentUrl);

    if (typeof window !== "undefined") {
      window.location.href = `/login?redirectFrom=${encodedUrl}`;
    }

    return new Promise(() => {});
  }

  return response;
}
