import { Module } from "@nestjs/common";
import { VotingService } from "./voting.service";
import { VotingController } from "./http/voting.controller";
import { VotingResolver } from "./gql/voting.resolver";
import { VotingOptionService } from "./voting-option.service";

@Module({
    controllers: [VotingController],
    providers: [VotingResolver, VotingService, VotingOptionService],
})
export class VotingModule {}
