import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "src/zod";
import { CurrentUser } from "src/auth/types";
import { PostService } from "../post.service";
import * as Dto from "./dto";
export declare class PostController {
    private readonly postService;
    constructor(postService: PostService);
    getPosts(page: number, size: number, user: CurrentUser): Promise<Dto.Post[]>;
    getPost(id: string, user: CurrentUser): Promise<Dto.Post>;
    createPost(body: Dto.CreatePostInput, user: CurrentUser, files?: Array<Express.Multer.File>): Promise<Dto.Post>;
    updatePost(id: string, body: Dto.UpdatePostInput, user: CurrentUser): Promise<Dto.Post>;
    uploadPostImages(id: string, user: CurrentUser, files: Array<Express.Multer.File>): Promise<ZodHelper.Image[]>;
}
