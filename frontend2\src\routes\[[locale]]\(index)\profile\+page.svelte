<script lang="ts">
  import type { Localization } from "$lib";

  import { onMount } from "svelte";
  import { goto } from "$app/navigation";
  import EditProfileModal from "./edit-profile-modal.svelte";
  import ImageGalleryModal from "./image-gallery-modal.svelte";
  import UploadImageModal from "./upload-image-modal.svelte";
  import { fetchWithAuth, findLocalizationForUserLocales } from "$lib";

  type Me = {
    id: string;
    email: string;
    role: "user" | "admin";
    name: Localization[];
    description: Localization[];
    images?: {
      id: string;
      url: string;
      source: string;
    }[];
    joinedAt: string;
  };

  const i18n = {
    en: {
      loading: "Loading...",
      loadingProfile: "Loading your profile...",
      uploadImage: "Upload profile image",
      admin: "Administrator",
      user: "User",
      editProfile: "Edit Profile",
      signOut: "Sign Out",
      joined: "Joined",
      accountSummary: "Account Summary",
      accountType: {
        title: "Account Type",
        values: {
          admin: "Administrator",
          user: "User",
        },
      },
      daysAsMember: "Days as member",
      aboutMe: "About Me",
      noDescription: "No description available yet. Add one to tell others about yourself.",
      addDescription: "Add Description",

      dateFormatLocale: "en-US",
    },

    ru: {
      loading: "Загрузка...",
      loadingProfile: "Загрузка вашего профиля...",
      uploadImage: "Загрузить изображение профиля",
      admin: "Администратор",
      user: "Пользователь",
      editProfile: "Редактировать профиль",
      signOut: "Выйти",
      joined: "Присоединился",
      accountSummary: "Информация об аккаунте",
      accountType: {
        title: "Тип аккаунта",
        values: {
          admin: "Администратор",
          user: "Пользователь",
        },
      },
      daysAsMember: "Дней в качестве участника",
      aboutMe: "Обо мне",
      noDescription: "Нет описания. Добавьте описание, чтобы рассказать другим о себе.",
      addDescription: "Добавить описание",

      dateFormatLocale: "ru-RU",
    },
  };

  const { data } = $props();

  const t = i18n[data.locale];

  let userData = $state<Me | null>(null);
  let showEditModal = $state(false);
  let showGalleryModal = $state(false);
  let showUploadModal = $state(false);
  let activeImageIndex = $state(0);

  const fetchUserData = async () => {
    const response = await fetchWithAuth("/api/auth/me", { cache: "no-cache" });
    const data = await response.json();
    userData = data;
  };

  onMount(() => {
    fetchUserData();
  });

  const name = $derived.by(() => findLocalizationForUserLocales(userData?.name ?? []));
  const description = $derived.by(() =>
    findLocalizationForUserLocales(userData?.description ?? []),
  );

  const handleLogout = () => {
    fetch("/api/auth/sign-out").then(() => goto("/"));
  };

  const joinDate = $derived(new Date(userData?.joinedAt ?? 0));
  const formattedDate = $derived(
    joinDate.toLocaleDateString(t.dateFormatLocale, {
      year: "numeric",
      month: "long",
      day: "numeric",
    }),
  );
</script>

{#if !userData}
  <div class="container my-5">
    <div
      class="d-flex flex-column align-items-center justify-content-center"
      style:min-height="60vh"
    >
      <div class="spinner-border text-primary" style:width="3rem" style:height="3rem" role="status">
        <span class="visually-hidden">{t.loading}</span>
      </div>
      <p class="mt-3 text-muted">{t.loadingProfile}</p>
    </div>
  </div>
{:else}
  <div class="container-fluid py-4">
    <div class="row g-4">
      <!-- Sidebar with user info -->
      <div class="col-lg-3">
        <div class="card border-0 shadow-sm h-100">
          <div class="text-center p-4">
            <div class="position-relative mx-auto mb-3" style:width="120px" style:height="120px">
              <!-- svelte-ignore a11y_click_events_have_key_events -->
              <div
                class="bg-light rounded-circle overflow-hidden border"
                style:width="100%"
                style:height="100%"
                style:cursor={userData?.images?.length ? "pointer" : "default"}
                onclick={() => {
                  if (userData?.images?.length) {
                    showGalleryModal = true;
                    activeImageIndex = 0;
                  }
                }}
                role="button"
                tabindex={0}
              >
                <!-- User avatar or fallback -->
                <div class="position-relative w-100 h-100">
                  <img
                    src={userData?.images?.length
                      ? `/api/images/${userData.images[0].url}`
                      : "/images/default-avatar.png"}
                    alt={`${name}'s avatar`}
                    width={120}
                    height={120}
                    style:width="100%"
                    style:height="100%"
                    style:object-fit="cover"
                  />
                </div>
              </div>
              <button
                class="position-absolute bottom-0 end-0 bg-primary text-white rounded-circle p-1 border-0"
                style:width="30px"
                style:height="30px"
                style:display="flex"
                style:align-items="center"
                style:justify-content="center"
                onclick={() => (showUploadModal = true)}
                title={t.uploadImage}
                aria-label={t.uploadImage}
              >
                <i class="bi bi-plus"></i>
              </button>
            </div>

            <h4 class="fw-bold mb-1">{name}</h4>
            <span class={`badge bg-${userData.role === "admin" ? "danger" : "primary"} mb-3`}>
              {userData.role === "admin" ? t.admin : t.user}
            </span>

            <div class="d-grid gap-2 mt-3">
              <button class="btn btn-primary" onclick={() => (showEditModal = true)}>
                <i class="bi bi-gear me-2"></i>
                {t.editProfile}
              </button>
              <button class="btn btn-outline-danger" onclick={handleLogout}>
                <i class="bi bi-box-arrow-right me-2"></i>
                {t.signOut}
              </button>
            </div>
          </div>

          <div class="card-footer bg-light border-top p-3">
            <div class="d-flex align-items-center mb-2">
              <i class="bi bi-envelope text-muted me-2"></i>
              <div class="text-truncate">
                <small class="text-muted">{userData.email}</small>
              </div>
            </div>
            <div class="d-flex align-items-center">
              <i class="bi bi-calendar3 text-muted me-2"></i>
              <div>
                <small class="text-muted">{t.joined} {formattedDate}</small>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Main content -->
      <div class="col-lg-9">
        <!-- Account summary -->
        <div class="card border-0 shadow-sm mb-4">
          <div class="card-header bg-transparent border-bottom-0 pb-0">
            <h5 class="fw-bold">{t.accountSummary}</h5>
          </div>
          <div class="card-body">
            <div class="row g-4">
              <div class="col-md-4">
                <div class="border rounded p-3 text-center h-100">
                  <div class="mb-2">
                    <i class="bi bi-shield-check fs-3 text-primary"></i>
                  </div>
                  <h2 class="mb-0 fw-bold">{t.accountType.values[userData.role]}</h2>
                  <p class="text-muted mb-0">{t.accountType.title}</p>
                </div>
              </div>
              <div class="col-md-4">
                <div class="border rounded p-3 text-center h-100">
                  <div class="mb-2">
                    <i class="bi bi-calendar-check fs-3 text-primary"></i>
                  </div>
                  <h2 class="mb-0 fw-bold">
                    {Math.floor(
                      (new Date().getTime() - joinDate.getTime()) / (1000 * 60 * 60 * 24),
                    )}
                  </h2>
                  <p class="text-muted mb-0">{t.daysAsMember}</p>
                </div>
              </div>
              <!-- <div class="col-md-4">
              <div class="border rounded p-3 text-center h-100">
                <div class="mb-2">
                  <i class="bi bi-person-badge fs-3 text-primary"></i>
                </div>
                <h2 class="mb-0 fw-bold">{userData.id.slice(0, 6)}</h2>
                <p class="text-muted mb-0">User ID</p>
              </div>
            </div> -->
            </div>
          </div>
        </div>

        <!-- About section -->
        <div class="card border-0 shadow-sm mb-4">
          <div class="card-header d-flex justify-content-between align-items-center bg-transparent">
            <h5 class="fw-bold mb-0">{t.aboutMe}</h5>
          </div>
          <div class="card-body">
            {#if description}
              <p class="mb-0">{description}</p>
            {:else}
              <div class="text-center text-muted py-4">
                <i class="bi bi-file-earmark-text fs-1 mb-2"></i>
                <p>{t.noDescription}</p>
                <button
                  onclick={() => (showEditModal = true)}
                  class="btn btn-sm btn-primary"
                  aria-label={t.addDescription}
                >
                  <i class="bi bi-plus-circle me-1"></i>
                  {t.addDescription}
                </button>
              </div>
            {/if}
          </div>
        </div>
      </div>
    </div>

    <!-- Edit Profile Modal -->
    <EditProfileModal
      locale={data.locale}
      show={showEditModal}
      onHide={() => (showEditModal = false)}
      userData={userData || null}
      onProfileUpdated={fetchUserData}
    />

    <!-- Image Gallery Modal -->
    {#if userData?.images}
      <ImageGalleryModal
        locale={data.locale}
        show={showGalleryModal}
        onHide={() => (showGalleryModal = false)}
        images={userData.images}
        activeIndex={activeImageIndex}
      />
    {/if}

    <!-- Upload Image Modal -->
    <!-- {#if userData} -->
    <UploadImageModal
      locale={data.locale}
      show={showUploadModal}
      onHide={() => (showUploadModal = false)}
      userId={userData.id}
      onImageUploaded={fetchUserData}
    />
    <!-- {/if} -->
  </div>
{/if}
