"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateVoting = exports.Votings = exports.Voting = exports.votesRequired = void 0;
const zod_1 = require("zod");
const zod_2 = require("../../zod");
exports.votesRequired = zod_1.z.number().int().positive();
exports.Voting = zod_1.z.object({
    id: zod_2.ZodHelper.Uuid,
    votesRequired: exports.votesRequired,
    endsAt: zod_2.ZodHelper.ToDateTime,
    title: zod_2.ZodHelper.Localizations,
    description: zod_2.ZodHelper.Localizations,
    options: zod_1.z.array(zod_1.z.object({
        id: zod_2.ZodHelper.Uuid,
        title: zod_2.ZodHelper.Localizations,
    })),
    createdAt: zod_2.ZodHelper.ToDateTime,
    updatedAt: zod_2.ZodHelper.ToDateTime,
});
exports.Votings = zod_1.z.array(exports.Voting);
exports.CreateVoting = zod_1.z.object({
    votesRequired: exports.votesRequired,
    endsAt: zod_2.ZodHelper.ToDateTime,
    title: zod_2.ZodHelper.Localizations,
    description: zod_2.ZodHelper.Localizations,
    options: zod_1.z.array(zod_1.z.object({
        title: zod_2.ZodHelper.Localizations,
    })),
});
//# sourceMappingURL=dto.js.map