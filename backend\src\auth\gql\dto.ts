import { z } from "zod";
import * as prisma from "@prisma/client";
import { <PERSON>od<PERSON><PERSON><PERSON> } from "src/zod";

export const currentUserTypename = "CurrentUser";

export type CurrentUser = ZodHelper.Infer<typeof CurrentUser>;
export const CurrentUser = z.object({
    __typename: z.literal(currentUserTypename).default(currentUserTypename),
    id: ZodHelper.Uuid,
    email: z.string().email(),
    role: z.nativeEnum(prisma.UserRole),
});

export const meTypename = "Me";

export type Me = ZodHelper.Infer<typeof Me>;
export const Me = z.object({
    __typename: z.literal(meTypename).default(meTypename),

    id: ZodHelper.Uuid,
    email: z.string().email(),
    role: z.nativeEnum(prisma.UserRole),

    name: Zod<PERSON>elper.Localizations,
    description: ZodHelper.Localizations,

    joinedAt: z.date().transform((date) => date.toISOString()),
});
