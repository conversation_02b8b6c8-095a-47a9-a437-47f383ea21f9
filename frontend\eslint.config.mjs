import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

/**
 * @type {import("eslint").Linter.Config[]}
 */
const eslintConfig = [
  // {
  //   languageOptions: {
  //     parser: "@typescript-eslint/parser",
  //     parserOptions: {
  //       project: "tsconfig.json",
  //       tsconfigRootDir: __dirname,
  //       sourceType: "module"
  //     }
  //   },
  //   // plugins: [
  //   //   "@typescript-eslint/eslint-plugin"
  //   // ],
  //   rules: {
  //     "@typescript-eslint/interface-name-prefix": "off",
  //     "@typescript-eslint/explicit-function-return-type": "off",
  //     "@typescript-eslint/explicit-module-boundary-types": "off",
  //     "@typescript-eslint/no-explicit-any": "off"
  //   },
  //   ignores: [".eslintrc.js"],
  //   linterOptions: {
  //     noInlineConfig: false,
  //     reportUnusedDisableDirectives: true
  //   }
  // },
  ...compat.extends(
    "next/core-web-vitals",
    "next/typescript",
    "plugin:@typescript-eslint/recommended",
  ),
  {
    plugins: {
      "@typescript-eslint/eslint-plugin": {},
    },
    ignores: [".eslintrc.js"],
    rules: {
      "quotes": ["error", "double"],
      "no-multiple-empty-lines": ["error", { "max": 1, "maxEOF": 0, "maxBOF": 0 }],
      "no-trailing-spaces": ["error"],
      "eol-last": ["error", "always"],
      "semi": ["error", "always"],
      "comma-dangle": ["error", "always-multiline"],
      "indent": [
        "error",
        2,
        {
          "SwitchCase": 1,
        },
      ],

      "@typescript-eslint/interface-name-prefix": "off",
      "@typescript-eslint/explicit-function-return-type": "off",
      "@typescript-eslint/explicit-module-boundary-types": "off",
      "@typescript-eslint/no-explicit-any": "off",
      "@typescript-eslint/no-unused-vars": ["off"],
      "react-hooks/exhaustive-deps": "off",
    },
  },
];

export default eslintConfig;
