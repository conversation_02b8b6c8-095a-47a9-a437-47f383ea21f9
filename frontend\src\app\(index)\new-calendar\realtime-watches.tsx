"use client";

import { useEffect, useState } from "react";
import { NewCalendarDate } from "@/utils/new-calendar";
import { Locale } from "@/app/types";

type Props = {
  locale: Locale;
}

const i18n = {
  en: {
    gregorianCalendar: "Gregorian Calendar",
    newCalendar: "New Calendar",
  },

  ru: {
    gregorianCalendar: "Григорианский календарь",
    newCalendar: "Новый календарь",
  },
};

export function RealtimeWatches({ locale }: Props) {
  const t = i18n[locale];

  const [currentTime, setCurrentTime] = useState(new Date(0));

  // Format new calendar date
  const formatNewCalendarDate = (date: Date) => {
    return new NewCalendarDate(date).toDateString();
  };

  // Format time
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    });
  };

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => {
      clearInterval(timer);
    };
  }, []);

  const formattedTime = formatTime(currentTime);

  return (
    <div className="my-4">
      <div className="row">
        <div className="col-md-6">
          <div className="card mb-3">
            <div className="card-body">
              <h5 className="card-title text-center">{t.gregorianCalendar}</h5>
              <div className="display-4 text-center">
                {currentTime.toLocaleDateString([], {
                  year: "numeric",
                  month: "numeric",
                  day: "numeric",
                })}
              </div>
              <div className="display-6 text-center font-monospace">{formattedTime}</div>
            </div>
          </div>
        </div>

        <div className="col-md-6">
          <div className="card mb-3">
            <div className="card-body">
              <h5 className="card-title text-center">{t.newCalendar}</h5>
              <div className="display-4 text-center">
                {formatNewCalendarDate(currentTime)}
              </div>
              <div className="display-6 text-center font-monospace">{formattedTime}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
