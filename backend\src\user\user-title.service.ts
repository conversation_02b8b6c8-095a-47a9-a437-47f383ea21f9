import { ForbiddenException, Injectable } from "@nestjs/common";
import { Prisma, UserRole } from "@prisma/client";
import { toPrismaPagination } from "src/utils";
import { getError } from "src/common/errors";
import { BaseService } from "src/common/base-service";
import { CurrentUser } from "src/auth/types";
import { PrismaService } from "src/prisma/prisma.service";

@Injectable()
export class UserTitleService extends BaseService {
    constructor(private readonly prisma: PrismaService) {
        super("user-title");
    }

    async update(
        id: string,
        data: Prisma.UserTitleUpdateInput,
        user: CurrentUser,
    ) {
        await this.canChange(id, user);

        return await this.updateOne(id, data);
    }

    async canGet(id: string, user: CurrentUser): Promise<true> {
        await this.getOneOrThrow(id);

        return true;
    }

    async canChange(id: string, user: CurrentUser): Promise<true> {
        const userTitle = await this.getOneOrThrow(id);

        if (user.role !== UserRole.admin) {
            if (userTitle.ownerId !== user.id) {
                throw new ForbiddenException(...getError("must_be_owner"));
            }
        }

        return true;
    }

    async check(ids: string[]) {
        return await this._check(ids, this.prisma.userTitle, "user_title");
    }

    async getOne(id: string) {
        return await this.prisma.userTitle.findUnique({
            where: { id, deletedAt: null },
        });
    }

    async getOneOrThrow(id: string) {
        const userTitle = await this.getOne(id);

        if (!userTitle) {
            throw this.createNotFoundException();
        }

        return userTitle;
    }

    async getMany(
        where: Prisma.UserTitleWhereInput,
        pagination?: { page: number; size: number },
    ) {
        return await this.prisma.userTitle.findMany({
            ...toPrismaPagination(pagination),
            where: {
                ...where,
                deletedAt: null,
            },
        });
    }

    async createOne(data: Prisma.UserTitleCreateInput) {
        return await this.prisma.userTitle.create({
            data,
        });
    }

    async createMany(data: Prisma.UserTitleCreateManyInput[]) {
        return await this.prisma.userTitle.createMany({
            data,
        });
    }

    async updateOne(id: string, data: Prisma.UserTitleUpdateInput) {
        return await this.prisma.userTitle.update({
            where: {
                id,
                deletedAt: null,
            },
            data,
        });
    }

    async updateMany(
        where: Prisma.UserTitleWhereInput,
        data: Prisma.UserTitleUpdateInput,
    ) {
        return await this.prisma.userTitle.updateMany({
            where: {
                ...where,
                deletedAt: null,
            },
            data,
        });
    }

    async softDeleteOne(id: string) {
        return await this.updateOne(id, { deletedAt: new Date() });
    }

    async softDeleteMany(where: Prisma.UserTitleWhereInput) {
        return await this.updateMany(
            {
                ...where,
                deletedAt: null,
            },
            { deletedAt: new Date() },
        );
    }

    async deleteOne(id: string) {
        return await this.prisma.userTitle.delete({
            where: {
                id,
                deletedAt: null,
            },
        });
    }

    async deleteMany(where: Prisma.UserTitleWhereInput) {
        return await this.prisma.userTitle.deleteMany({
            where: {
                ...where,
                deletedAt: null,
            },
        });
    }
}
