.carouselContainer {
  position: relative;
  height: 300px;
  overflow: hidden;
  border-radius: 0.375rem;
  margin-bottom: 1.5rem;
}

.carousel {
  height: 100%;
}

.carousel :global(.carousel-control-prev),
.carousel :global(.carousel-control-next) {
  width: 10%;
  background: rgba(0, 0, 0, 0.2);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.carouselContainer:hover :global(.carousel-control-prev),
.carouselContainer:hover :global(.carousel-control-next) {
  opacity: 1;
}

.carousel :global(.carousel-indicators) {
  display: none;
}

.carouselItem {
  height: 300px;
}

.imageContainer {
  width: 100%;
  height: 100%;
  position: relative;
}

.carouselImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.indicators {
  position: absolute;
  bottom: 15px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: 8px;
  z-index: 2;
}

.indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  border: none;
  padding: 0;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.activeIndicator {
  background-color: #fff;
}
