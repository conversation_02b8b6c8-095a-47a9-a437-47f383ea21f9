// commune
model Commune {
    @@map("communes")

    id String @id @db.Uuid @default(uuid(7))

    images Image[] @relation("commune_images")
    tags   Tag[]   @relation("commune_tags")

    name        Localization[] @relation("commune_name")
    description Localization[] @relation("commune_description")

    members CommuneMember[] @relation("commune_members")

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
    deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)
}

// commune-member-type
enum CommuneMemberType {
    @@map("commune_member_type")

    commune
    user
}

// commune-member
model CommuneMember {
    @@map("commune_members")

    id String @id @db.Uuid @default(uuid(7))

    communeId String  @map("commune_id") @db.Uuid
    commune   Commune @relation("commune_members", fields: [communeId], references: [id])

    actorType CommuneMemberType
    actorId   String @map("actor_id") @db.Uuid

    isHead Boolean @default(false)

    joinedAt DateTime  @map("joined_at") @db.Timestamptz(3) @default(now())
    leftAt   DateTime? @map("left_at")   @db.Timestamptz(3)

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
    deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)
}
