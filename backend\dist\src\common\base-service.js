"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseService = void 0;
const common_1 = require("@nestjs/common");
const errors_1 = require("./errors");
const client_1 = require("@prisma/client");
class BaseService {
    constructor(entityType) {
        this.entityType = entityType;
    }
    async canChange(id, user) {
        if (user.role === client_1.UserRole.admin)
            return true;
        throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin"));
    }
    createNotFoundException() {
        return new common_1.NotFoundException(...(0, errors_1.getError)(`${this.entityType}_not_found`));
    }
    async _check(ids, repo, notFoundDescription) {
        ids = [...new Set(ids)];
        const items = await repo.findMany({
            where: {
                id: { in: ids },
                deletedAt: null,
            },
        });
        if (items.length < ids.length) {
            throw new common_1.NotFoundException(notFoundDescription);
        }
        return items;
    }
}
exports.BaseService = BaseService;
//# sourceMappingURL=base-service.js.map