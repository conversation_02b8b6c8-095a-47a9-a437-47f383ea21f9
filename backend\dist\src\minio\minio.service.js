"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var MinioService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MinioService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const minio_1 = require("minio");
const zod_1 = require("zod");
let MinioService = MinioService_1 = class MinioService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(MinioService_1.name);
        this.buckets = {
            communeImages: "commune-images",
            userImages: "user-images",
            postImages: "post-images",
        };
        const endPoint = zod_1.z
            .string()
            .nonempty()
            .parse(this.configService.get("MINIO_ENDPOINT"));
        const port = zod_1.z.coerce
            .number()
            .int()
            .positive()
            .parse(this.configService.get("MINIO_PORT"));
        const accessKey = zod_1.z
            .string()
            .nonempty()
            .parse(this.configService.get("MINIO_ACCESS_KEY"));
        const secretKey = zod_1.z
            .string()
            .nonempty()
            .parse(this.configService.get("MINIO_SECRET_KEY"));
        const useSSL = zod_1.z.coerce
            .boolean()
            .default(false)
            .parse(this.configService.get("MINIO_USE_SSL"));
        const options = {
            endPoint,
            port,
            useSSL,
            accessKey,
            secretKey,
        };
        this.client = new minio_1.Client(options);
        this.initializeBuckets();
    }
    async initializeBuckets() {
        try {
            for (const bucket of Object.values(this.buckets)) {
                const exists = await this.client.bucketExists(bucket);
                if (!exists) {
                    await this.client.makeBucket(bucket, "us-east-1");
                    this.logger.log(`Bucket '${bucket}' created successfully`);
                    const policy = {
                        Version: "2012-10-17",
                        Statement: [
                            {
                                Effect: "Allow",
                                Principal: { AWS: ["*"] },
                                Action: ["s3:GetObject"],
                                Resource: [`arn:aws:s3:::${bucket}/*`],
                            },
                        ],
                    };
                    await this.client.setBucketPolicy(bucket, JSON.stringify(policy));
                    this.logger.log(`Public read policy set for bucket '${bucket}'`);
                }
            }
        }
        catch (error) {
            this.logger.error("Failed to initialize MinIO buckets", error);
            throw error;
        }
    }
    async uploadFile(file, bucket, objectName) {
        try {
            await this.client.putObject(bucket, objectName, file.buffer, undefined, {
                "Content-Type": file.mimetype,
            });
            return `${bucket}/${objectName}`;
        }
        catch (error) {
            this.logger.error(`Failed to upload file ${objectName} to bucket ${bucket}`, error);
            throw error;
        }
    }
    async uploadCommuneImage(file, communeId, index) {
        const fileExt = file.originalname.split(".").pop();
        const objectName = `${communeId}/${index}.${fileExt}`;
        return this.uploadFile(file, this.buckets.communeImages, objectName);
    }
    async uploadUserImage(file, userId, index) {
        const fileExt = file.originalname.split(".").pop();
        const objectName = `${userId}/${index}.${fileExt}`;
        return this.uploadFile(file, this.buckets.userImages, objectName);
    }
    async deleteFile(bucket, objectName) {
        try {
            await this.client.removeObject(bucket, objectName);
        }
        catch (error) {
            this.logger.error(`Failed to delete file ${objectName} from bucket ${bucket}`, error);
            throw error;
        }
    }
    async deleteCommuneImage(communeId, filename) {
        const objectName = `${communeId}/${filename}`;
        return this.deleteFile(this.buckets.communeImages, objectName);
    }
    async deleteUserImage(userId, filename) {
        const objectName = `${userId}/${filename}`;
        return this.deleteFile(this.buckets.userImages, objectName);
    }
    async uploadPostImage(file, postId, index) {
        const fileExt = file.originalname.split(".").pop();
        const objectName = `${postId}/${index}.${fileExt}`;
        return this.uploadFile(file, this.buckets.postImages, objectName);
    }
    async deletePostImage(postId, filename) {
        const objectName = `${postId}/${filename}`;
        return this.deleteFile(this.buckets.postImages, objectName);
    }
};
exports.MinioService = MinioService;
exports.MinioService = MinioService = MinioService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], MinioService);
//# sourceMappingURL=minio.service.js.map