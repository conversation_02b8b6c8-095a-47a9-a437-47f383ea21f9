.heroBackground {
  background-image: url('/images/home-page-main-image.png');
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center bottom;
}

.heroContent {
  position: relative;
}

@media (max-width: 767.98px) {
  .heroBackground {
    background-image: url('/images/home-page-main-image-mobile.png');
  }

  .heroContent {
    position: absolute;
    top: 35%;
  }
}
