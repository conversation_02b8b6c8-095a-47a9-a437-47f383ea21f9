import {
    Body,
    Controller,
    Get,
    NotFoundException,
    Param,
    Post,
    Put,
    Query,
    UploadedFile,
    UseGuards,
    UseInterceptors,
    ParseFilePipe,
    MaxFileSizeValidator,
    FileTypeValidator,
    BadRequestException,
} from "@nestjs/common";
import { FileInterceptor } from "@nestjs/platform-express";
import { z } from "zod";
import { <PERSON>od<PERSON><PERSON><PERSON>, ZodPipe } from "src/zod";
import { getError } from "src/common/errors";
import { UserService } from "../user.service";
import { UserTitleService } from "../user-title.service";
import * as Dto from "./dto";
import { HttpCurrentUser } from "src/auth/http/current-user.decorator";
import { CurrentUser } from "src/auth/types";
import { HttpJwtAuthGuard } from "src/auth/http/jwt-auth.guard";

// Constants for file upload validation
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ALLOWED_FILE_TYPES = ["image/jpeg", "image/png", "image/webp"];

@Controller("user")
export class UserController {
    constructor(
        private readonly userService: UserService,
        private readonly userTitleService: UserTitleService,
    ) {}

    @Get()
    async getUsers(
        @Query("page", new ZodPipe(ZodHelper.pagination.page)) page: number,
        @Query("size", new ZodPipe(ZodHelper.pagination.size)) size: number,
    ): Promise<Dto.User[]> {
        console.log({ page, size });

        const users = await this.userService.getMany({}, { page, size });

        return ZodHelper.parseInput(Dto.Users, users);
    }

    @Get(":id")
    async getUser(
        @Param("id", new ZodPipe(ZodHelper.Uuid)) id: string,
    ): Promise<Dto.User> {
        const user = await this.userService.getOne(id);

        if (!user) {
            throw new NotFoundException(...getError("user_not_found"));
        }

        return ZodHelper.parseInput(Dto.User, user);
    }

    @Put(":id")
    @UseGuards(HttpJwtAuthGuard)
    async updateUser(
        @Param("id", new ZodPipe(ZodHelper.Uuid)) id: string,
        @Body(new ZodPipe(Dto.UpdateUser)) body: Dto.UpdateUser,
        @HttpCurrentUser() currentUser: CurrentUser,
    ): Promise<Dto.User> {
        const user = await this.userService.update(id, body, currentUser);

        return ZodHelper.parseInput(Dto.User, user);
    }

    @Post(":id/image")
    @UseGuards(HttpJwtAuthGuard)
    @UseInterceptors(FileInterceptor("image"))
    async uploadUserImage(
        @Param("id", new ZodPipe(ZodHelper.Uuid)) id: string,
        @HttpCurrentUser() currentUser: CurrentUser,
        @UploadedFile(
            new ParseFilePipe({
                validators: [
                    new MaxFileSizeValidator({ maxSize: MAX_FILE_SIZE }),
                    new FileTypeValidator({
                        fileType: ALLOWED_FILE_TYPES.join("|"),
                    }),
                ],
            }),
        )
        file: Express.Multer.File,
    ): Promise<ZodHelper.Image> {
        try {
            // Check permissions
            await this.userService.canChange(id, currentUser);

            if (!file) {
                throw new BadRequestException("No file uploaded");
            }

            const image = await this.userService.uploadUserImage(id, file);

            return ZodHelper.parseInput(ZodHelper.Image, image);
        } catch (error) {
            console.error("Error uploading image:", error);

            if (error instanceof BadRequestException) {
                throw error;
            }

            throw new BadRequestException("Failed to upload image");
        }
    }

    @Get(":id/title")
    async getUserTitles(
        @Param("id", new ZodPipe(ZodHelper.Uuid)) id: string,
        @Query("active", new ZodPipe(z.boolean())) active: boolean,
        @Query("page", new ZodPipe(ZodHelper.pagination.page)) page: number,
        @Query("size", new ZodPipe(ZodHelper.pagination.size)) size: number,
    ): Promise<Dto.UserTitle[]> {
        const userTitles = await this.userTitleService.getMany(
            {
                ownerId: id,
                isActive: active,
            },
            { page, size },
        );

        return ZodHelper.parseInput(Dto.UserTitles, userTitles);
    }

    @Put(":id/title/:titleId")
    async updateUserTitle(
        @Param("id", new ZodPipe(ZodHelper.Uuid)) id: string,
        @Param("titleId", new ZodPipe(ZodHelper.Uuid)) titleId: string,
        @Body(new ZodPipe(Dto.UpdateUserTitle)) body: Dto.UpdateUserTitle,
        @HttpCurrentUser() user: CurrentUser,
    ): Promise<Dto.UserTitle> {
        const userTitle = await this.userTitleService.update(id, body, user);

        return ZodHelper.parseInput(Dto.UserTitle, userTitle);
    }
}
