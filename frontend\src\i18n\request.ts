import Negotiator from "negotiator";
import { cookies, headers } from "next/headers";
import { getRequestConfig } from "next-intl/server";
import { match } from "@formatjs/intl-localematcher";

// Define supported locales
const supportedLocales = ["en", "ru"];
const defaultLocale = "en";

// The function must get header Accept-Language and negotiate the locale
export default getRequestConfig(async () => {
  const [headersList, cookiesList] = await Promise.all([headers(), cookies()]);

  // Check for preferred locale cookie first
  const preferredLocale = cookiesList.get("PREFERRED_LOCALE");

  if (preferredLocale && supportedLocales.includes(preferredLocale.value)) {
    return { locale: preferredLocale.value };
  }

  // Otherwise, use Accept-Language header
  const acceptLanguage = headersList.get("Accept-Language");

  // If no Accept-Language header is present, return default locale
  if (!acceptLanguage) {
    return { locale: defaultLocale };
  }

  // Create a Negotiator instance with mock request object
  const negotiatorHeaders = { "accept-language": acceptLanguage };
  const languages = new Negotiator({ headers: negotiatorHeaders }).languages();

  // Use intl-localematcher to find the best matching locale
  try {
    const matchedLocale = match(languages, supportedLocales, defaultLocale);
    return { locale: matchedLocale };
  } catch (error) {
    // In case of any error during negotiation, fall back to default locale
    console.error("Error during locale negotiation:", error);
    return { locale: defaultLocale };
  }
});
