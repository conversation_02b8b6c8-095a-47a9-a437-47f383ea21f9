"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const client_1 = require("@prisma/client");
const consts_1 = require("../../src/consts");
const prismaClient = new client_1.PrismaClient();
const UUID_NIL_1 = consts_1.UUID_NIL.slice(0, -1) + "1";
const UUID_NIL_2 = consts_1.UUID_NIL.slice(0, -1) + "2";
const users = [
    {
        id: consts_1.UUID_NIL,
        email: "<EMAIL>",
        role: "user",
        name: {
            en: "Test User",
            ru: "Тестовый Пользователь",
        },
        description: {
            en: "Test description.",
            ru: "Тестовое описание.",
        },
    },
    {
        id: UUID_NIL_1,
        email: "<EMAIL>",
        role: "admin",
        name: {
            en: "Admin",
            ru: "Администратор",
        },
        description: {
            en: "Admin description.",
            ru: "Описание администратора.",
        },
    },
    {
        id: UUID_NIL_2,
        email: "<EMAIL>",
        role: "user",
        name: {
            en: "User",
            ru: "Пользователь",
        },
        description: {
            en: "User description.",
            ru: "Описание пользователя.",
        },
    },
];
async function main() {
    await prismaClient.$transaction(async (trx) => {
        await Promise.all(users.map((user) => trx.user.create({
            data: {
                id: user.id,
                email: user.email,
                role: user.role,
                name: {
                    create: [
                        {
                            locale: "en",
                            key: "name",
                            value: user.name.en,
                        },
                        {
                            locale: "ru",
                            key: "name",
                            value: user.name.ru,
                        },
                    ],
                },
                description: {
                    create: [
                        {
                            locale: "en",
                            key: "description",
                            value: user.description.en,
                        },
                        {
                            locale: "ru",
                            key: "description",
                            value: user.description.ru,
                        },
                    ],
                },
            },
        })));
        await trx.commune.create({
            data: {
                id: consts_1.UUID_NIL,
                name: {
                    create: [
                        {
                            locale: "en",
                            key: "name",
                            value: "Test Commune",
                        },
                        {
                            locale: "ru",
                            key: "name",
                            value: "Тестовая Коммуна",
                        },
                    ],
                },
                description: {
                    create: [
                        {
                            locale: "en",
                            key: "description",
                            value: "Test description.",
                        },
                        {
                            locale: "ru",
                            key: "description",
                            value: "Тестовое описание.",
                        },
                    ],
                },
                members: {
                    create: [
                        {
                            id: consts_1.UUID_NIL,
                            actorType: "user",
                            actorId: consts_1.UUID_NIL,
                            isHead: true,
                        },
                    ],
                },
            },
        });
    });
}
main()
    .then(async () => {
    await prismaClient.$disconnect();
})
    .catch(async (e) => {
    console.error(e);
    await prismaClient.$disconnect();
    process.exit(1);
});
//# sourceMappingURL=seed.js.map