"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Me = exports.meTypename = exports.CurrentUser = exports.currentUserTypename = void 0;
const zod_1 = require("zod");
const prisma = require("@prisma/client");
const zod_2 = require("../../zod");
exports.currentUserTypename = "CurrentUser";
exports.CurrentUser = zod_1.z.object({
    __typename: zod_1.z.literal(exports.currentUserTypename).default(exports.currentUserTypename),
    id: zod_2.ZodHelper.Uuid,
    email: zod_1.z.string().email(),
    role: zod_1.z.nativeEnum(prisma.UserRole),
});
exports.meTypename = "Me";
exports.Me = zod_1.z.object({
    __typename: zod_1.z.literal(exports.meTypename).default(exports.meTypename),
    id: zod_2.Zod<PERSON>elper.Uuid,
    email: zod_1.z.string().email(),
    role: zod_1.z.nativeEnum(prisma.UserRole),
    name: zod_2.ZodHelper.Localizations,
    description: zod_2.ZodHelper.Localizations,
    joinedAt: zod_1.z.date().transform((date) => date.toISOString()),
});
//# sourceMappingURL=dto.js.map