"use client";

import { useState } from "react";
import { Carousel } from "react-bootstrap";
import styles from "./user-image-carousel.module.css";
import { useLocale } from "next-intl";
import { Locale } from "@/app/types";

interface UserImage {
  id: string;
  url: string;
  source: string;
}

interface UserImageCarouselProps {
  images: UserImage[];
}

const i18n = {
  en: {
    noImage: "No image",
    userImageAlt: "User image",
  },
  ru: {
    noImage: "Нет изображения",
    userImageAlt: "Изображение пользователя",
  },
};

export function UserImageCarousel({ images }: UserImageCarouselProps) {
  const locale = useLocale() as Locale;
  const t = i18n[locale];

  const [index, setIndex] = useState(0);

  const handleSelect = (selectedIndex: number) => {
    setIndex(selectedIndex);
  };

  // If no images, show placeholder
  if (!images || images.length === 0) {
    return (
      <div
        className="bg-light text-center"
        style={{ height: "140px", display: "flex", alignItems: "center", justifyContent: "center" }}
      >
        <span className="text-muted">{t.noImage}</span>
      </div>
    );
  }

  return (
    <div className={styles.carouselContainer}>
      <Carousel
        activeIndex={index}
        onSelect={handleSelect}
        interval={null}
        indicators={true}
        controls={images.length > 1}
        className={styles.carousel}
      >
        {images.map((image, idx) => (
          <Carousel.Item key={image.id || idx} className={styles.carouselItem}>
            <div className={styles.imageContainer}>
              { /* eslint-disable-next-line @next/next/no-img-element */ }
              <img
                src={`/api/images/${image.url}`}
                alt={`${t.userImageAlt} ${idx + 1}`}
                className={styles.carouselImage}
              />
            </div>
          </Carousel.Item>
        ))}
      </Carousel>
    </div>
  );
}
