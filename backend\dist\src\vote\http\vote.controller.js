"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VoteController = void 0;
const common_1 = require("@nestjs/common");
const zod_1 = require("../../zod");
const current_user_decorator_1 = require("../../auth/http/current-user.decorator");
const vote_service_1 = require("../vote.service");
const Dto = require("./dto");
let VoteController = class VoteController {
    constructor(voteService) {
        this.voteService = voteService;
    }
    async createVote(body, user) {
        await this.voteService.create({
            userId: user.id,
            votingId: body.votingId,
            optionId: body.optionId,
        });
        return true;
    }
};
exports.VoteController = VoteController;
__decorate([
    (0, common_1.Put)(),
    __param(0, (0, common_1.Body)(new zod_1.ZodPipe(Dto.CreateVote))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], VoteController.prototype, "createVote", null);
exports.VoteController = VoteController = __decorate([
    (0, common_1.Controller)("vote"),
    __metadata("design:paramtypes", [vote_service_1.VoteService])
], VoteController);
//# sourceMappingURL=vote.controller.js.map