"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const crypto = require("node:crypto");
const zod_1 = require("zod");
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const jwt_1 = require("@nestjs/jwt");
const errors_1 = require("../common/errors");
const email_service_1 = require("../email/email.service");
const user_service_1 = require("../user/user.service");
const email_otp_service_1 = require("../email/email-otp.service");
const user_refresh_token_service_1 = require("../user/user-refresh-token.service");
const coerceBoolean = (data) => zod_1.z.coerce.boolean().parse(data);
const nonEmptyString = (data) => zod_1.z.string().nonempty().parse(data);
let AuthService = class AuthService {
    constructor(configService, jwtService, emailService, userService, userOtpService, userRefreshTokenService) {
        this.configService = configService;
        this.jwtService = jwtService;
        this.emailService = emailService;
        this.userService = userService;
        this.userOtpService = userOtpService;
        this.userRefreshTokenService = userRefreshTokenService;
        this.disableRegisterOtpCheck = coerceBoolean(this.configService.get("DISABLE_REGISTER_OTP_CHECK"));
        this.disableLoginOtpCheck = coerceBoolean(this.configService.get("DISABLE_LOGIN_OTP_CHECK"));
        this.instanceName = nonEmptyString(this.configService.get("INSTANCE_NAME"));
        this.domain = nonEmptyString(this.configService.get("INSTANCE_EMAIL_DOMAIN"));
        this.otpSender = nonEmptyString(this.configService.get("OTP_EMAIL_SENDER"));
    }
    async generateTokenPair(dto) {
        const accessTokenSecret = zod_1.z
            .string()
            .min(32)
            .parse(this.configService.get("ACCESS_TOKEN_SECRET"));
        const accessTokenExpiresInMinutes = zod_1.z.coerce
            .number()
            .catch(10)
            .parse(this.configService.get("ACCESS_TOKEN_EXPIRES_IN_MINUTES"));
        const refreshTokenSecret = zod_1.z
            .string()
            .min(32)
            .parse(this.configService.get("REFRESH_TOKEN_SECRET"));
        const refreshTokenExpiresInDays = zod_1.z.coerce
            .number()
            .catch(7)
            .parse(this.configService.get("REFRESH_TOKEN_EXPIRES_IN_DAYS"));
        const accessTokenExpiresAt = new Date(Date.now() + accessTokenExpiresInMinutes * 60 * 1000);
        const refreshTokenExpiresAt = new Date(Date.now() + refreshTokenExpiresInDays * 24 * 60 * 60 * 1000);
        const { user, ipAddress, userAgent } = dto;
        const accessTokenPayload = {
            id: user.id,
            email: user.email,
            role: user.role,
        };
        const accessTokenValue = this.jwtService.sign(accessTokenPayload, {
            secret: accessTokenSecret,
            expiresIn: `${accessTokenExpiresInMinutes}m`,
        });
        {
            const decoded = this.jwtService.verify(accessTokenValue, {
                secret: accessTokenSecret,
            });
        }
        const accessToken = {
            value: accessTokenValue,
            expiresAt: accessTokenExpiresAt,
        };
        const refreshTokenPayload = {
            id: user.id,
            email: user.email,
            role: user.role,
            ipAddress,
            userAgent,
            createdAt: new Date().toISOString(),
            expiresAt: refreshTokenExpiresAt.toISOString(),
        };
        const refreshTokenData = JSON.stringify(refreshTokenPayload);
        const iv = crypto.randomBytes(16);
        const cipher = crypto.createCipheriv("aes-256-cbc", crypto.createHash("sha256").update(refreshTokenSecret).digest(), iv);
        let encrypted = cipher.update(refreshTokenData, "utf8", "base64");
        encrypted += cipher.final("base64");
        const refreshToken = {
            value: `${iv.toString("base64")}.${encrypted}`,
            expiresAt: refreshTokenExpiresAt,
        };
        return { accessToken, refreshToken };
    }
    generateOtp() {
        return Math.floor(100000 + Math.random() * 900000).toString();
    }
    async otp(dto) {
        const otp = this.generateOtp();
        await this.userOtpService.create({
            email: dto.email,
            otp,
            ipAddress: dto.ipAddress,
            userAgent: dto.userAgent,
        });
        console.log({ otp, email: dto.email });
        return await this.emailService.send({
            from: this.emailService.joinAddress(this.otpSender, this.domain),
            to: [dto.email],
            subject: `${this.instanceName} - OTP`,
            text: `Your OTP is ${otp}.`,
        });
    }
    async login(dto) {
        const user = await this.userService.getByEmail(dto.email);
        if (!user) {
            throw new common_1.UnauthorizedException(...(0, errors_1.getError)("user_not_found"));
        }
        if (!this.disableLoginOtpCheck) {
            const userOtp = await this.userOtpService.check({
                email: dto.email,
                otp: dto.otp,
            });
            await this.userOtpService.softDelete({
                id: userOtp.id,
            });
        }
        const tokenPair = await this.generateTokenPair({
            user,
            ipAddress: dto.ipAddress,
            userAgent: dto.userAgent,
        });
        await this.userRefreshTokenService.create({
            userId: user.id,
            token: tokenPair.refreshToken.value,
            expiresAt: tokenPair.refreshToken.expiresAt,
            ipAddress: dto.ipAddress,
            userAgent: dto.userAgent,
        });
        return {
            ...tokenPair,
            user: {
                id: user.id,
                email: user.email,
                role: user.role,
            },
        };
    }
    async register(dto) {
        {
            const user = await this.userService.getByEmail(dto.email);
            if (user) {
                throw new common_1.UnauthorizedException(...(0, errors_1.getError)("user_already_exists"));
            }
        }
        if (!this.disableRegisterOtpCheck) {
            const otp = await this.userOtpService.check({
                email: dto.email,
                otp: dto.otp,
            });
            await this.userOtpService.softDelete({
                id: otp.id,
            });
        }
        const user = await this.userService.create({
            referrerId: dto.referrerId,
            email: dto.email,
        });
        const tokenPair = await this.generateTokenPair({
            user,
            ipAddress: dto.ipAddress,
            userAgent: dto.userAgent,
        });
        await this.userRefreshTokenService.create({
            userId: user.id,
            token: tokenPair.refreshToken.value,
            expiresAt: tokenPair.refreshToken.expiresAt,
            ipAddress: dto.ipAddress,
            userAgent: dto.userAgent,
        });
        return {
            ...tokenPair,
            user: {
                id: user.id,
                email: user.email,
                role: user.role,
            },
        };
    }
    async refresh(dto) {
        const refreshToken = await this.userRefreshTokenService.findByValue(dto.refreshToken);
        if (!refreshToken) {
            throw new common_1.UnauthorizedException(...(0, errors_1.getError)("refresh_token_invalid"));
        }
        const tokenPair = await this.generateTokenPair({
            user: refreshToken.user,
            ipAddress: dto.ipAddress,
            userAgent: dto.userAgent,
        });
        await this.userRefreshTokenService.create({
            userId: refreshToken.user.id,
            token: tokenPair.refreshToken.value,
            expiresAt: tokenPair.refreshToken.expiresAt,
            ipAddress: dto.ipAddress,
            userAgent: dto.userAgent,
        });
        return tokenPair;
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        jwt_1.JwtService,
        email_service_1.EmailService,
        user_service_1.UserService,
        email_otp_service_1.EmailOtpService,
        user_refresh_token_service_1.UserRefreshTokenService])
], AuthService);
//# sourceMappingURL=auth.service.js.map