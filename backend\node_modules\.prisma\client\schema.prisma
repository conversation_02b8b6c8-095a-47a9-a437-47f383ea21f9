// commune
model Commune {

  id String @id @default(uuid(7)) @db.Uuid

  images Image[] @relation("commune_images")
  tags   Tag[]   @relation("commune_tags")

  name        Localization[] @relation("commune_name")
  description Localization[] @relation("commune_description")

  members CommuneMember[] @relation("commune_members")

  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz(3)
  updatedAt DateTime  @updatedAt @map("updated_at") @db.Timestamptz(3)
  deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)

  @@map("communes")
}

// commune-member-type
enum CommuneMemberType {

  commune
  user

  @@map("commune_member_type")
}

// commune-member
model CommuneMember {

  id String @id @default(uuid(7)) @db.Uuid

  communeId String  @map("commune_id") @db.Uuid
  commune   Commune @relation("commune_members", fields: [communeId], references: [id])

  actorType CommuneMemberType
  actorId   String            @map("actor_id") @db.Uuid

  isHead Boolean @default(false)

  joinedAt DateTime  @default(now()) @map("joined_at") @db.Timestamptz(3)
  leftAt   DateTime? @map("left_at") @db.Timestamptz(3)

  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz(3)
  updatedAt DateTime  @updatedAt @map("updated_at") @db.Timestamptz(3)
  deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)

  @@map("commune_members")
}

// image
model Image {

  id String @id @default(uuid(7)) @db.Uuid

  users        User[]        @relation("user_images")
  communes     Commune[]     @relation("commune_images")
  votings      Voting[]      @relation("voting_images")
  posts        Post[]        @relation("post_images")
  merchandises Merchandise[] @relation("merchandise_images")

  url String

  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz(3)
  updatedAt DateTime  @updatedAt @map("updated_at") @db.Timestamptz(3)
  deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)

  @@map("images")
}

// locale
enum Locale {

  en
  ru

  @@map("locale")
}

// localization
model Localization {

  id String @id @default(uuid(7)) @db.Uuid

  userName        User[] @relation("user_name")
  userDescription User[] @relation("user_description")

  communeName        Commune[] @relation("commune_name")
  communeDescription Commune[] @relation("commune_description")

  votingTitle             Voting[]       @relation("voting_title")
  votingDescription       Voting[]       @relation("voting_description")
  votingOptionTitle       VotingOption[] @relation("voting_option_title")
  votingOptionDescription VotingOption[] @relation("voting_option_description")

  postTitle       Post[] @relation("post_title")
  postDescription Post[] @relation("post_description")

  reactorPostTitle ReactorPost[] @relation("reactor_post_title")
  reactorPostBody  ReactorPost[] @relation("reactor_post_body")

  reactorCommentBody ReactorComment[] @relation("reactor_comment_body")

  key String

  locale Locale
  value  String

  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz(3)
  updatedAt DateTime  @updatedAt @map("updated_at") @db.Timestamptz(3)
  deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)

  @@map("localizations")
}

// merchandise-status
enum MerchandiseStatus {

  hidden
  shown

  @@map("merchandise_status")
}

// merchandise
model Merchandise {

  id String @id @default(uuid(7)) @db.Uuid

  category String

  price Int @map("price")

  images Image[] @relation("merchandise_images")
  tags   Tag[]   @relation("merchandise_tags")

  status MerchandiseStatus @default(hidden)

  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz(3)
  updatedAt DateTime  @updatedAt @map("updated_at") @db.Timestamptz(3)
  deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)

  @@index([category])
  @@map("merchandises")
}

// post-status
enum PostStatus {

  draft
  published
  archived

  @@map("post_status")
}

// post
model Post {

  id String @id @default(uuid(7)) @db.Uuid

  images Image[] @relation("post_images")
  tags   Tag[]   @relation("post_tags")

  title       Localization[] @relation("post_title")
  description Localization[] @relation("post_description")

  status PostStatus @default(draft)

  publishedAt DateTime? @map("published_at") @db.Timestamptz(3)

  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz(3)
  updatedAt DateTime  @updatedAt @map("updated_at") @db.Timestamptz(3)
  deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)

  @@map("posts")
}

// reactor-post
model ReactorPost {

  id String @id @default(uuid(7)) @db.Uuid

  tags Tag[] @relation("reactor_post_tags")

  authorId String @map("author_id") @db.Uuid
  author   User   @relation("user_reactor_posts", fields: [authorId], references: [id])

  title Localization[] @relation("reactor_post_title")
  body  Localization[] @relation("reactor_post_body")

  isAnonymous     Boolean @default(false) @map("is_anonymous")
  anonimityReason String? @map("anonimity_reason")

  deleteReason String? @map("delete_reason")

  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz(3)
  updatedAt DateTime  @updatedAt @map("updated_at") @db.Timestamptz(3)
  deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)

  @@index([authorId])
  @@map("reactor_posts")
}

model ReactorPostInternalNumber {

  id String @id @default(uuid(7)) @db.Uuid

  postId String @unique @map("post_id") @db.Uuid

  internalNumber Int @map("internal_number")

  createdAt DateTime @default(now()) @map("created_at") @db.Timestamptz(3)
  updatedAt DateTime @updatedAt @map("updated_at") @db.Timestamptz(3)

  @@map("reactor_post_internal_numbers")
}

// reactor-comment
model ReactorComment {

  id String @id @default(uuid(7)) @db.Uuid

  authorId String @map("author_id") @db.Uuid
  author   User   @relation("user_reactor_comments", fields: [authorId], references: [id])

  postId String @map("post_id") @db.Uuid

  path           String
  internalNumber Int    @map("internal_number")

  isAnonymous     Boolean @default(false) @map("is_anonymous")
  anonimityReason String? @map("anonimity_reason")

  body Localization[] @relation("reactor_comment_body")

  deleteReason String? @map("delete_reason")

  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz(3)
  updatedAt DateTime  @updatedAt @map("updated_at") @db.Timestamptz(3)
  deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)

  @@unique([postId, internalNumber])
  @@unique([postId, path])
  @@index([postId, path])
  @@map("reactor_comments")
}

// reactor-entity-type
enum ReactorEntityType {

  post
  comment

  @@map("reactor_entity_type")
}

enum ReactorRatingType {

  like
  dislike

  @@map("reactor_rating_type")
}

model ReactorRating {

  id String @id @default(uuid(7)) @db.Uuid

  userId String @map("user_id") @db.Uuid
  user   User   @relation("user_reactor_ratings", fields: [userId], references: [id])

  entityType ReactorEntityType @map("entity_type")
  entityId   String            @map("entity_id") @db.Uuid

  type ReactorRatingType

  createdAt DateTime @default(now()) @map("created_at") @db.Timestamptz(3)
  updatedAt DateTime @updatedAt @map("updated_at") @db.Timestamptz(3)

  @@unique([userId, entityType, entityId])
  @@index([userId])
  @@index([entityType, entityId])
  @@map("reactor_ratings")
}

model ReactorUsefulness {

  id String @id @default(uuid(7)) @db.Uuid

  userId String @map("user_id") @db.Uuid
  user   User   @relation("user_reactor_usefulnesses", fields: [userId], references: [id])

  entityType ReactorEntityType @map("entity_type")
  entityId   String            @map("entity_id") @db.Uuid

  value Int

  createdAt DateTime @default(now()) @map("created_at") @db.Timestamptz(3)
  updatedAt DateTime @updatedAt @map("updated_at") @db.Timestamptz(3)

  @@unique([userId, entityType, entityId])
  @@index([userId])
  @@index([entityType, entityId])
  @@map("reactor_usefulnesses")
}

// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// id String @id @db.Uuid @default(uuid(7))

// tag
model Tag {

  id String @id @default(uuid(7)) @db.Uuid

  communes     Commune[]     @relation("commune_tags")
  votings      Voting[]      @relation("voting_tags")
  posts        Post[]        @relation("post_tags")
  merchandises Merchandise[] @relation("merchandise_tags")
  reactorPosts ReactorPost[] @relation("reactor_post_tags")

  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz(3)
  updatedAt DateTime  @updatedAt @map("updated_at") @db.Timestamptz(3)
  deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)

  @@map("tags")
}

// user-role
enum UserRole {

  admin
  moderator
  user

  @@map("user_role")
}

// user
model User {

  id String @id @default(uuid(7)) @db.Uuid

  referrerId String? @map("referrer_id") @db.Uuid
  referrer   User?   @relation("referrer", fields: [referrerId], references: [id])
  referrals  User[]  @relation("referrer")

  email String @unique

  role UserRole @default(user)

  images Image[] @relation("user_images")

  name        Localization[] @relation("user_name")
  description Localization[] @relation("user_description")

  titles UserTitle[] @relation("user_titles")

  refreshTokens UserRefreshToken[] @relation("user_refresh_tokens")

  reactorPosts      ReactorPost[]       @relation("user_reactor_posts")
  reactorComments   ReactorComment[]    @relation("user_reactor_comments")
  reactorRatings    ReactorRating[]     @relation("user_reactor_ratings")
  reactorUsefulness ReactorUsefulness[] @relation("user_reactor_usefulnesses")

  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz(3)
  updatedAt DateTime  @updatedAt @map("updated_at") @db.Timestamptz(3)
  deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)

  @@map("users")
}

// user-title
model UserTitle {

  id String @id @default(uuid(7)) @db.Uuid

  ownerId String? @map("owner_id") @db.Uuid
  owner   User?   @relation("user_titles", fields: [ownerId], references: [id])

  isActive Boolean @default(false)

  color String?

  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz(3)
  updatedAt DateTime  @updatedAt @map("updated_at") @db.Timestamptz(3)
  deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)

  @@map("user_titles")
}

model UserRefreshToken {

  id String @id @default(uuid(7)) @db.Uuid

  userId String @map("user_id") @db.Uuid
  user   User   @relation("user_refresh_tokens", fields: [userId], references: [id])

  token     String
  expiresAt DateTime @map("expires_at") @db.Timestamptz(3)

  ipAddress String? @map("ip_address")
  userAgent String? @map("user_agent")

  revokedAt    DateTime? @map("revoked_at") @db.Timestamptz(3)
  revokeReason String?   @map("revoke_reason")

  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz(3)
  updatedAt DateTime  @updatedAt @map("updated_at") @db.Timestamptz(3)
  deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)

  @@map("user_refresh_tokens")
}

// user-otp
model UserOtp {

  id String @id @default(uuid(7)) @db.Uuid

  email String @map("email")
  // user  User?  @relation("user_otps", fields: [email], references: [email])

  otp String

  ipAddress String? @map("ip_address")
  userAgent String? @map("user_agent")

  expiresAt DateTime @map("expires_at") @db.Timestamptz(3)

  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz(3)
  updatedAt DateTime  @updatedAt @map("updated_at") @db.Timestamptz(3)
  deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)

  @@map("user_otps")
}

// voting
model Voting {

  id String @id @default(uuid(7)) @db.Uuid

  images  Image[]        @relation("voting_images")
  options VotingOption[] @relation("voting_options")
  votes   Vote[]         @relation("voting_votes")
  tags    Tag[]          @relation("voting_tags")

  title       Localization[] @relation("voting_title")
  description Localization[] @relation("voting_description")

  votesRequired Int @map("votes_required")

  endsAt DateTime @map("ends_at") @db.Timestamptz(3)

  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz(3)
  updatedAt DateTime  @updatedAt @map("updated_at") @db.Timestamptz(3)
  deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)

  @@map("votings")
}

model VotingOption {

  id String @id @default(uuid(7)) @db.Uuid

  votingId String @map("voting_id") @db.Uuid
  voting   Voting @relation("voting_options", fields: [votingId], references: [id])

  votes Vote[] @relation("voting_votes")

  title       Localization[] @relation("voting_option_title")
  description Localization[] @relation("voting_option_description")

  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz(3)
  updatedAt DateTime  @updatedAt @map("updated_at") @db.Timestamptz(3)
  deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)

  @@map("voting_options")
}

// vote-actor-type
enum VoteActorType {

  commune
  user

  @@map("vote_actor_type")
}

// vote
model Vote {

  id String @id @default(uuid(7)) @db.Uuid

  actorType VoteActorType
  actorId   String        @map("actor_id") @db.Uuid

  votingId String? @map("voting_id") @db.Uuid
  voting   Voting? @relation("voting_votes", fields: [votingId], references: [id])

  optionId String?       @map("option_id") @db.Uuid
  option   VotingOption? @relation("voting_votes", fields: [optionId], references: [id])

  createdAt DateTime  @default(now()) @map("created_at") @db.Timestamptz(3)
  updatedAt DateTime  @updatedAt @map("updated_at") @db.Timestamptz(3)
  deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)

  @@map("votes")
}
