import * as crypto from "node:crypto";
import { z } from "zod";
import { Injectable, UnauthorizedException } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { JwtService } from "@nestjs/jwt";
import * as prisma from "@prisma/client";
import { getError } from "src/common/errors";
import { EmailService } from "src/email/email.service";
import { UserService } from "src/user/user.service";
import { EmailOtpService } from "src/email/email-otp.service";
import { UserRefreshTokenService } from "src/user/user-refresh-token.service";

type InfoDto = {
    ipAddress: string | null;
    userAgent: string | null;
};

type OtpDto = InfoDto & {
    email: string;
};

type RegisterDto = InfoDto & {
    referrerId: string | null;
    email: string;
    otp: string;
};

type LoginDto = InfoDto & {
    email: string;
    otp: string;
};

// type LogoutDto = InfoDto & {
//     refreshToken: string;
// };

type RefreshDto = InfoDto & {
    refreshToken: string;
};

const coerceBoolean = (data: unknown) => z.coerce.boolean().parse(data);
const nonEmptyString = (data: unknown) => z.string().nonempty().parse(data);

@Injectable()
export class AuthService {
    private readonly disableRegisterOtpCheck: boolean;
    private readonly disableLoginOtpCheck: boolean;

    private readonly instanceName: string;
    private readonly domain: string;
    private readonly otpSender: string;

    constructor(
        private readonly configService: ConfigService,
        private readonly jwtService: JwtService,
        private readonly emailService: EmailService,
        private readonly userService: UserService,
        private readonly userOtpService: EmailOtpService,
        private readonly userRefreshTokenService: UserRefreshTokenService,
    ) {
        this.disableRegisterOtpCheck = coerceBoolean(
            this.configService.get("DISABLE_REGISTER_OTP_CHECK"),
        );

        this.disableLoginOtpCheck = coerceBoolean(
            this.configService.get("DISABLE_LOGIN_OTP_CHECK"),
        );

        this.instanceName = nonEmptyString(
            this.configService.get("INSTANCE_NAME"),
        );

        this.domain = nonEmptyString(
            this.configService.get("INSTANCE_EMAIL_DOMAIN"),
        );

        this.otpSender = nonEmptyString(
            this.configService.get("OTP_EMAIL_SENDER"),
        );
    }

    protected async generateTokenPair(dto: {
        user: prisma.User;
        ipAddress: string | null;
        userAgent: string | null;
    }): Promise<{
        accessToken: {
            value: string;
            expiresAt: Date;
        };
        refreshToken: {
            value: string;
            expiresAt: Date;
        };
    }> {
        // Get configuration values
        const accessTokenSecret = z
            .string()
            .min(32)
            .parse(this.configService.get("ACCESS_TOKEN_SECRET"));

        const accessTokenExpiresInMinutes = z.coerce
            .number()
            .catch(10)
            .parse(this.configService.get("ACCESS_TOKEN_EXPIRES_IN_MINUTES"));

        const refreshTokenSecret = z
            .string()
            .min(32)
            .parse(this.configService.get("REFRESH_TOKEN_SECRET"));

        const refreshTokenExpiresInDays = z.coerce
            .number()
            .catch(7)
            .parse(this.configService.get("REFRESH_TOKEN_EXPIRES_IN_DAYS"));

        // console.log({
        //     accessTokenSecret,
        //     accessTokenExpiresInMinutes,
        // });
        // console.log({
        //     refreshTokenSecret,
        //     refreshTokenExpiresInDays,
        // });

        // Calculate expiration dates
        const accessTokenExpiresAt = new Date(
            Date.now() + accessTokenExpiresInMinutes * 60 * 1000,
        );

        const refreshTokenExpiresAt = new Date(
            Date.now() + refreshTokenExpiresInDays * 24 * 60 * 60 * 1000,
        );

        const { user, ipAddress, userAgent } = dto;

        // Generate access token (JWT)
        const accessTokenPayload = {
            id: user.id,
            email: user.email,
            role: user.role,
        };

        const accessTokenValue = this.jwtService.sign(accessTokenPayload, {
            secret: accessTokenSecret,
            expiresIn: `${accessTokenExpiresInMinutes}m`,
        });

        // console.log("accessTokenValue", accessTokenValue);

        {
            const decoded = this.jwtService.verify(accessTokenValue, {
                secret: accessTokenSecret,
            });

            // console.log("accessTokenValue.decoded", decoded);
        }

        const accessToken = {
            value: accessTokenValue,
            expiresAt: accessTokenExpiresAt,
        };

        // Generate refresh token (encrypted)
        const refreshTokenPayload = {
            id: user.id,
            email: user.email,
            role: user.role,
            ipAddress,
            userAgent,
            createdAt: new Date().toISOString(),
            expiresAt: refreshTokenExpiresAt.toISOString(),
        };

        // Encrypt the JSON payload with AES-256-CBC
        const refreshTokenData = JSON.stringify(refreshTokenPayload);
        const iv = crypto.randomBytes(16); // Initialization vector for AES
        const cipher = crypto.createCipheriv(
            "aes-256-cbc",
            // Create a fixed-length key from the secret
            crypto.createHash("sha256").update(refreshTokenSecret).digest(),
            iv,
        );

        let encrypted = cipher.update(refreshTokenData, "utf8", "base64");
        encrypted += cipher.final("base64");

        // Combine IV with the encrypted data (IV needs to be stored for decryption)
        const refreshToken = {
            value: `${iv.toString("base64")}.${encrypted}`,
            expiresAt: refreshTokenExpiresAt,
        };

        return { accessToken, refreshToken };
    }

    protected generateOtp() {
        return Math.floor(100000 + Math.random() * 900000).toString();
    }

    async otp(dto: OtpDto) {
        const otp = this.generateOtp();

        await this.userOtpService.create({
            email: dto.email,
            otp,
            ipAddress: dto.ipAddress,
            userAgent: dto.userAgent,
        });

        console.log({ otp, email: dto.email });

        return await this.emailService.send({
            from: this.emailService.joinAddress(this.otpSender, this.domain),
            to: [dto.email],
            subject: `${this.instanceName} - OTP`,
            text: `Your OTP is ${otp}.`,
        });
    }

    async login(dto: LoginDto) {
        const user = await this.userService.getByEmail(dto.email);

        if (!user) {
            throw new UnauthorizedException(...getError("user_not_found"));
        }

        if (!this.disableLoginOtpCheck) {
            const userOtp = await this.userOtpService.check({
                email: dto.email,
                otp: dto.otp,
            });

            await this.userOtpService.softDelete({
                id: userOtp.id,
            });
        }

        const tokenPair = await this.generateTokenPair({
            user,
            ipAddress: dto.ipAddress,
            userAgent: dto.userAgent,
        });

        await this.userRefreshTokenService.create({
            userId: user.id,
            token: tokenPair.refreshToken.value,
            expiresAt: tokenPair.refreshToken.expiresAt,
            ipAddress: dto.ipAddress,
            userAgent: dto.userAgent,
        });

        return {
            ...tokenPair,
            user: {
                id: user.id,
                email: user.email,
                role: user.role,
            },
        };
    }

    async register(dto: RegisterDto) {
        // check for duplicates
        {
            const user = await this.userService.getByEmail(dto.email);

            if (user) {
                throw new UnauthorizedException(
                    ...getError("user_already_exists"),
                );
            }
        }

        if (!this.disableRegisterOtpCheck) {
            const otp = await this.userOtpService.check({
                email: dto.email,
                otp: dto.otp,
            });

            await this.userOtpService.softDelete({
                id: otp.id,
            });
        }

        const user = await this.userService.create({
            referrerId: dto.referrerId,
            email: dto.email,
        });

        const tokenPair = await this.generateTokenPair({
            user,
            ipAddress: dto.ipAddress,
            userAgent: dto.userAgent,
        });

        await this.userRefreshTokenService.create({
            userId: user.id,
            token: tokenPair.refreshToken.value,
            expiresAt: tokenPair.refreshToken.expiresAt,
            ipAddress: dto.ipAddress,
            userAgent: dto.userAgent,
        });

        return {
            ...tokenPair,
            user: {
                id: user.id,
                email: user.email,
                role: user.role,
            },
        };
    }

    async refresh(dto: RefreshDto) {
        const refreshToken = await this.userRefreshTokenService.findByValue(
            dto.refreshToken,
        );

        if (!refreshToken) {
            throw new UnauthorizedException(
                ...getError("refresh_token_invalid"),
            );
        }

        const tokenPair = await this.generateTokenPair({
            user: refreshToken.user,
            ipAddress: dto.ipAddress,
            userAgent: dto.userAgent,
        });

        await this.userRefreshTokenService.create({
            userId: refreshToken.user.id,
            token: tokenPair.refreshToken.value,
            expiresAt: tokenPair.refreshToken.expiresAt,
            ipAddress: dto.ipAddress,
            userAgent: dto.userAgent,
        });

        return tokenPair;
    }
}
