"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Localization, useLocalization } from "@/app/hooks/use-localization";
import { useHttpRequest } from "@/app/hooks/use-http-request";
import { EditProfileModal } from "./edit-profile-modal";
import { ImageGalleryModal } from "./image-gallery-modal";
import { UploadImageModal } from "./upload-image-modal";
import { useLocale } from "next-intl";
import { Locale } from "@/app/types";
import Image from "next/image";

type Me = {
  id: string;
  email: string;
  role: "user" | "admin";
  name: Localization[];
  description: Localization[];
  images?: {
    id: string;
    url: string;
    source: string;
  }[];
  joinedAt: string;
};

const i18n = {
  en: {
    loading: "Loading...",
    loadingProfile: "Loading your profile...",
    uploadImage: "Upload profile image",
    admin: "Administrator",
    user: "User",
    editProfile: "Edit Profile",
    signOut: "Sign Out",
    joined: "Joined",
    accountSummary: "Account Summary",
    accountType: {
      title: "Account Type",
      values: {
        admin: "Administrator",
        user: "User",
      },
    },
    daysAsMember: "Days as member",
    aboutMe: "About Me",
    noDescription: "No description available yet. Add one to tell others about yourself.",
    addDescription: "Add Description",

    dateFormatLocale: "en-US",
  },

  ru: {
    loading: "Загрузка...",
    loadingProfile: "Загрузка вашего профиля...",
    uploadImage: "Загрузить изображение профиля",
    admin: "Администратор",
    user: "Пользователь",
    editProfile: "Редактировать профиль",
    signOut: "Выйти",
    joined: "Присоединился",
    accountSummary: "Информация об аккаунте",
    accountType: {
      title: "Тип аккаунта",
      values: {
        admin: "Администратор",
        user: "Пользователь",
      },
    },
    daysAsMember: "Дней в качестве участника",
    aboutMe: "Обо мне",
    noDescription: "Нет описания. Добавьте описание, чтобы рассказать другим о себе.",
    addDescription: "Добавить описание",

    dateFormatLocale: "ru-RU",
  },
};

export default function AccountPage() {
  const locale = useLocale() as Locale;
  const t = i18n[locale];

  const router = useRouter();
  const [userData, setUserData] = useState<Me | null>();
  const [showEditModal, setShowEditModal] = useState(false);
  const [showGalleryModal, setShowGalleryModal] = useState(false);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [activeImageIndex, setActiveImageIndex] = useState(0);
  const httpRequest = useHttpRequest();

  const fetchUserData = async () => {
    const response = await httpRequest("/api/auth/me", { cache: "no-cache" });
    const data = await response.json();
    setUserData(data);
  };

  useEffect(() => {
    fetchUserData();
  }, []);

  const name = useLocalization(userData?.name ?? []);
  const description = useLocalization(userData?.description ?? []);

  const handleLogout = () => {
    fetch("/api/auth/sign-out")
      .then(() => router.push("/"));
  };

  if (!userData) {
    return (
      <div className="container my-5">
        {/* <Navigation currentTab="main" /> */}

        <div className="d-flex flex-column align-items-center justify-content-center" style={{ minHeight: "60vh" }}>
          <div className="spinner-border text-primary" style={{ width: "3rem", height: "3rem" }} role="status">
            <span className="visually-hidden">{t.loading}</span>
          </div>
          <p className="mt-3 text-muted">{t.loadingProfile}</p>
        </div>
      </div>
    );
  }

  // Format join date
  const joinDate = new Date(userData.joinedAt);
  const formattedDate = joinDate.toLocaleDateString(t.dateFormatLocale, {
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  return (
    <div className="container-fluid py-4">
      {/* <Navigation currentTab="main" /> */}

      <div className="row g-4">
        {/* Sidebar with user info */}
        <div className="col-lg-3">
          <div className="card border-0 shadow-sm h-100">
            <div className="text-center p-4">
              <div className="position-relative mx-auto mb-3" style={{ width: "120px", height: "120px" }}>
                <div
                  className="bg-light rounded-circle overflow-hidden border"
                  style={{ width: "100%", height: "100%", cursor: userData?.images?.length ? "pointer" : "default" }}
                  onClick={() => {
                    if (userData?.images?.length) {
                      setShowGalleryModal(true);
                      setActiveImageIndex(0);
                    }
                  }}
                >
                  {/* User avatar or fallback */}
                  <div className="position-relative w-100 h-100">
                    <Image
                      src={userData?.images?.length ? `/api/images/${userData.images[0].url}` : "/images/default-avatar.png"}
                      alt={`${name}'s avatar`}
                      width={120}
                      height={120}
                      style={{ width: "100%", height: "100%", objectFit: "cover" }}
                      unoptimized
                    />
                  </div>
                </div>
                <button
                  className="position-absolute bottom-0 end-0 bg-primary text-white rounded-circle p-1 border-0"
                  style={{ width: "30px", height: "30px", display: "flex", alignItems: "center", justifyContent: "center" }}
                  onClick={() => setShowUploadModal(true)}
                  title={t.uploadImage}
                >
                  <i className="bi bi-plus" />
                </button>
              </div>

              <h4 className="fw-bold mb-1">{name}</h4>
              <span className={`badge bg-${userData.role === "admin" ? "danger" : "primary"} mb-3`}>
                {userData.role === "admin" ? t.admin : t.user}
              </span>

              <div className="d-grid gap-2 mt-3">
                <button className="btn btn-primary" onClick={() => setShowEditModal(true)}>
                  <i className="bi bi-gear me-2" />
                  {t.editProfile}
                </button>
                <button className="btn btn-outline-danger" onClick={handleLogout}>
                  <i className="bi bi-box-arrow-right me-2" />
                  {t.signOut}
                </button>
              </div>
            </div>

            <div className="card-footer bg-light border-top p-3">
              <div className="d-flex align-items-center mb-2">
                <i className="bi bi-envelope text-muted me-2"></i>
                <div className="text-truncate">
                  <small className="text-muted">{userData.email}</small>
                </div>
              </div>
              <div className="d-flex align-items-center">
                <i className="bi bi-calendar3 text-muted me-2"></i>
                <div>
                  <small className="text-muted">{t.joined} {formattedDate}</small>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main content */}
        <div className="col-lg-9">
          {/* Account summary */}
          <div className="card border-0 shadow-sm mb-4">
            <div className="card-header bg-transparent border-bottom-0 pb-0">
              <h5 className="fw-bold">{t.accountSummary}</h5>
            </div>
            <div className="card-body">
              <div className="row g-4">
                <div className="col-md-4">
                  <div className="border rounded p-3 text-center h-100">
                    <div className="mb-2">
                      <i className="bi bi-shield-check fs-3 text-primary"></i>
                    </div>
                    <h2 className="mb-0 fw-bold">{t.accountType.values[userData.role]}</h2>
                    <p className="text-muted mb-0">{t.accountType.title}</p>
                  </div>
                </div>
                <div className="col-md-4">
                  <div className="border rounded p-3 text-center h-100">
                    <div className="mb-2">
                      <i className="bi bi-calendar-check fs-3 text-primary"></i>
                    </div>
                    <h2 className="mb-0 fw-bold">
                      {Math.floor((new Date().getTime() - joinDate.getTime()) / (1000 * 60 * 60 * 24))}
                    </h2>
                    <p className="text-muted mb-0">{t.daysAsMember}</p>
                  </div>
                </div>
                {/* <div className="col-md-4">
                  <div className="border rounded p-3 text-center h-100">
                    <div className="mb-2">
                      <i className="bi bi-person-badge fs-3 text-primary"></i>
                    </div>
                    <h2 className="mb-0 fw-bold">{userData.id.slice(0, 6)}</h2>
                    <p className="text-muted mb-0">User ID</p>
                  </div>
                </div> */}
              </div>
            </div>
          </div>

          {/* About section */}
          <div className="card border-0 shadow-sm mb-4">
            <div className="card-header d-flex justify-content-between align-items-center bg-transparent">
              <h5 className="fw-bold mb-0">{t.aboutMe}</h5>
              {/* <Link href="/account/settings" className="btn btn-sm btn-outline-primary">
                <i className="bi bi-pencil me-1"></i> Edit
              </Link> */}
            </div>
            <div className="card-body">
              {description ? (
                <p className="mb-0">{description}</p>
              ) : (
                <div className="text-center text-muted py-4">
                  <i className="bi bi-file-earmark-text fs-1 mb-2"></i>
                  <p>{t.noDescription}</p>
                  <button onClick={() => setShowEditModal(true)} className="btn btn-sm btn-primary">
                    <i className="bi bi-plus-circle me-1"></i>
                    {t.addDescription}
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Quick Links */}
          {/* <div className="card border-0 shadow-sm">
            <div className="card-header bg-transparent">
              <h5 className="fw-bold mb-0">Quick Links</h5>
            </div>
            <div className="card-body">
              <div className="row g-3">
                <div className="col-md-4">
                  <Link href="/account/settings" className="text-decoration-none">
                    <div className="border rounded p-3 text-center h-100 quick-link-card">
                      <i className="bi bi-gear-fill fs-3 mb-2 text-primary"></i>
                      <h6 className="mb-0">Settings</h6>
                    </div>
                  </Link>
                </div>
                <div className="col-md-4">
                  <Link href="/account/notifications" className="text-decoration-none">
                    <div className="border rounded p-3 text-center h-100 quick-link-card">
                      <i className="bi bi-bell-fill fs-3 mb-2 text-primary"></i>
                      <h6 className="mb-0">Notifications</h6>
                    </div>
                  </Link>
                </div>
                <div className="col-md-4">
                  <Link href="/account/subscriptions" className="text-decoration-none">
                    <div className="border rounded p-3 text-center h-100 quick-link-card">
                      <i className="bi bi-bookmark-fill fs-3 mb-2 text-primary"></i>
                      <h6 className="mb-0">Subscriptions</h6>
                    </div>
                  </Link>
                </div>
              </div>
            </div>
          </div> */}
        </div>
      </div>

      {/* Edit Profile Modal */}
      <EditProfileModal
        show={showEditModal}
        onHide={() => setShowEditModal(false)}
        userData={userData || null}
        onProfileUpdated={fetchUserData}
      />

      {/* Image Gallery Modal */}
      {userData?.images && (
        <ImageGalleryModal
          show={showGalleryModal}
          onHide={() => setShowGalleryModal(false)}
          images={userData.images}
          activeIndex={activeImageIndex}
        />
      )}

      {/* Upload Image Modal */}
      {userData && (
        <UploadImageModal
          show={showUploadModal}
          onHide={() => setShowUploadModal(false)}
          userId={userData.id}
          onImageUploaded={fetchUserData}
        />
      )}
    </div>
  );
}
