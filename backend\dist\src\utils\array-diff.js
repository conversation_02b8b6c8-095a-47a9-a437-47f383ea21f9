"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.symmetricArrayDiff = symmetricArrayDiff;
exports.asymmetricArrayDiff = asymmetricArrayDiff;
function symmetricArrayDiff(a, b) {
    return [
        ...new Set([
            ...a.filter((x) => !b.includes(x)),
            ...b.filter((x) => !a.includes(x)),
        ]),
    ];
}
function asymmetricArrayDiff(a, b) {
    return [...new Set(a.filter((x) => !b.includes(x)))];
}
//# sourceMappingURL=array-diff.js.map