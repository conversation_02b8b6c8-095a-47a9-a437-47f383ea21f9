import * as Gql from "src/graphql";
import { VotingService } from "../voting.service";
export type IVotingResolver = Gql.IResolver<"getVoting" | "getVotings", "createVoting">;
export declare class VotingResolver implements IVotingResolver {
    private readonly votingService;
    constructor(votingService: VotingService);
    getVotings(): Promise<Gql.Voting[]>;
    getVoting(id: string): Promise<Gql.Voting>;
    createVoting(input: any): Promise<Gql.Voting>;
}
