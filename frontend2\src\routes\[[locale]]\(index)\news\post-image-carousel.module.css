.carouselContainer {
  position: relative;
  height: 140px;
  overflow: hidden;
}

.carousel {
  height: 100%;
}

.carousel :global(.carousel-control-prev),
.carousel :global(.carousel-control-next) {
  width: 15%;
  background: rgba(0, 0, 0, 0.2);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.carouselContainer:hover :global(.carousel-control-prev),
.carouselContainer:hover :global(.carousel-control-next) {
  opacity: 1;
}

.carousel :global(.carousel-indicators) {
  display: none;
}

.carouselItem {
  height: 140px;
}

.imageContainer {
  width: 100%;
  height: 100%;
  position: relative;
}

.carouselImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.indicators {
  position: absolute;
  bottom: 10px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: 5px;
  z-index: 2;
}

.indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  border: none;
  padding: 0;
  cursor: pointer;
}

.activeIndicator {
  background-color: white;
}
