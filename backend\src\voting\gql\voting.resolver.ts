import { Args, Mutation, Query, Resolver } from "@nestjs/graphql";
import { <PERSON>od<PERSON><PERSON><PERSON>, ZodPipe } from "src/zod";
import * as Gql from "src/graphql";
import { VotingService } from "../voting.service";
import * as Dto from "./dto";

export type IVotingResolver = Gql.IResolver<
    "getVoting" | "getVotings",
    "createVoting"
>;

@Resolver()
export class VotingResolver implements IVotingResolver {
    constructor(private readonly votingService: VotingService) {}

    @Query("getVotings")
    async getVotings(): Promise<Gql.Voting[]> {
        const votings = await this.votingService.getMany({});

        return ZodHelper.parseInput(Dto.Votings, votings);
    }

    @Query("getVoting")
    async getVoting(
        @Args("id", new ZodPipe(ZodHelper.Uuid)) id: string,
    ): Promise<Gql.Voting> {
        const voting = await this.votingService.getOneOrThrow(id);

        return ZodHelper.parseInput(Dto.Voting, voting);
    }

    @Mutation("createVoting")
    async createVoting(
        @Args("input", new ZodPipe(Dto.CreateVotingInput))
        input: any, // Dto.CreateVotingInput,
    ): Promise<Gql.Voting> {
        const voting = await this.votingService.create(input);

        return ZodHelper.parseInput(Dto.Voting, {
            ...voting,
            endsAt: voting.endsAt.toISOString(),
        });
    }
}
