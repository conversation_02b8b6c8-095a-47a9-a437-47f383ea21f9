export const errors = {
    user_not_found: "User not found",
    user_email_is_busy: "User email is busy",
    refresh_token_invalid: "Refresh token is invalid",
    otp_invalid: "OTP is invalid",
    email_already_exists: "Email already taken",
    post_not_found: "Post not found",
    must_be_admin: "Must be an admin to perform this action",
};

export type ErrorKey = keyof typeof errors;

export type ErrorData<T extends SuggestableString<ErrorKey>> = readonly [
    T,
    string,
];

export function getError<T extends SuggestableString<ErrorKey>>(
    errorCode: T,
    additionalInfo?: string,
): Normalize<ErrorData<T>> {
    const error = (errors as Record<string, string>)[errorCode];
    const errorMessage = error ?? errorCode;

    return [
        errorCode,
        errorMessage + (additionalInfo ? ` (${additionalInfo})` : ""),
    ];
}
