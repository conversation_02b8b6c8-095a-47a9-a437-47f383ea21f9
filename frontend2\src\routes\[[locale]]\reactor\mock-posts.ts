export interface Post {
  id: string;
  rating: number;
  usefulness: number;
  title: string;
  content: string;
  tags: string[];
  commentCount: number;
}

export const mockPosts: Post[] = [
  {
    id: "1",
    rating: 42,
    usefulness: 4,
    title: "Introduction to Svelte 5 with Runes",
    content:
      "Svelte 5 introduces a new reactive primitive called runes. Runes are special $ prefixed variables and functions that unlock new capabilities. The $state rune creates a reactive variable. When the variable changes, any part of the UI that depends on it will update. This is similar to Svelte 3's reactive statements, but more flexible and powerful.",
    tags: ["svelte", "javascript", "frontend", "programming"],
    commentCount: 15,
  },
  {
    id: "2",
    rating: 127,
    usefulness: 5,
    title: "The Future of Web Development",
    content:
      "The web development landscape is constantly evolving. From the early days of static HTML pages to the complex single-page applications we build today, the journey has been remarkable. Looking ahead, we can expect even more exciting changes. WebAssembly is gaining traction, allowing developers to run high-performance code in the browser. Edge computing is bringing computation closer to users, reducing latency. AI-powered development tools are making developers more productive than ever before. The rise of no-code and low-code platforms is democratizing web development, allowing non-developers to build simple applications. Progressive Web Apps continue to blur the line between web and native applications, offering offline capabilities and native-like experiences. The metaverse and AR/VR technologies are opening up new possibilities for immersive web experiences. As developers, it's crucial to stay updated with these trends and continuously learn new skills to remain relevant in this fast-paced industry.",
    tags: ["webdev", "future", "technology", "trends"],
    commentCount: 42,
  },
  {
    id: "3",
    rating: -5,
    usefulness: 2,
    title: "Why I Switched from React to Vue",
    content:
      "After years of using React for all my projects, I decided to give Vue a try. Here's my experience and why I ultimately decided to switch for most of my new projects.",
    tags: ["vue", "react", "javascript", "frontend"],
    commentCount: 87,
  },
  {
    id: "4",
    rating: 310,
    usefulness: 5,
    title: "Understanding TypeScript's Type System",
    content:
      "TypeScript's type system is powerful but can be complex for beginners. In this post, I'll break down the key concepts and show you how to leverage TypeScript to write safer, more maintainable code. We'll cover basic types, interfaces, type aliases, generics, and advanced type manipulations. By the end, you'll have a solid understanding of how to use TypeScript effectively in your projects.",
    tags: ["typescript", "javascript", "programming", "webdev"],
    commentCount: 28,
  },
  {
    id: "5",
    rating: 73,
    usefulness: 3,
    title: "Building a REST API with Node.js and Express",
    content:
      "In this tutorial, we'll build a complete REST API using Node.js and Express. We'll cover setting up the project, defining routes, connecting to a database, implementing authentication, and testing our API. By the end, you'll have a solid foundation for building your own APIs.",
    tags: ["nodejs", "express", "api", "backend"],
    commentCount: 19,
  },
  {
    id: "6",
    rating: -12,
    usefulness: 1,
    title: "My Controversial Opinion on CSS Frameworks",
    content:
      "CSS frameworks like Bootstrap and Tailwind have become incredibly popular, but are they really the best choice for every project? In this post, I share my controversial take on why I prefer writing custom CSS for most of my projects.",
    tags: ["css", "webdev", "opinion", "frontend"],
    commentCount: 156,
  },
  {
    id: "7",
    rating: 89,
    usefulness: 4,
    title: "Getting Started with Docker for Web Developers",
    content:
      "Docker can significantly improve your development workflow by providing consistent environments across your team. In this beginner-friendly guide, I'll show you how to get started with Docker as a web developer. We'll cover basic concepts, setting up a development environment, and deploying your applications using Docker.",
    tags: ["docker", "devops", "webdev", "tutorial"],
    commentCount: 31,
  },
];
