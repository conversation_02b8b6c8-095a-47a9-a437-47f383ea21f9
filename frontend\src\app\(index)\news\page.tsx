"use client";

import Link from "next/link";
import { useEffect, useState } from "react";
import { Container, Row, Col, Card, Pagination, But<PERSON> } from "react-bootstrap";
import { useHttpRequest } from "@/app/hooks/use-http-request";
import { formatDate } from "@/utils/format-date";
import { findLocalizationForUserLocales, Localization } from "@/utils/find-localization";
import { PostImageCarousel } from "./post-image-carousel";
import { CreatePostModal } from "./create-post-modal";
import { useLocale } from "next-intl";
import { Locale } from "@/app/types";

interface Post {
  id: string;
  title: Localization[];
  description: Localization[];
  status: "draft" | "published" | "archived";
  publishedAt: string | null;
  createdAt: string;
  updatedAt: string;
  images?: {
    id: string;
    url: string;
    source: string;
  }[];
}

const i18n = {
  en: {
    posts: "Posts",
    createPost: "Create Post",
    loading: "Loading...",
    noPostsFound: "No posts found",
    noDescription: "No description",
    dateFormatLocale: "en-US",
    errorFetchingUser: "Failed to fetch user",
    errorFetchingPosts: "Failed to fetch posts",
    errorOccurred: "An error occurred while fetching posts",
  },

  ru: {
    posts: "Публикации",
    createPost: "Создать публикацию",
    loading: "Загрузка...",
    noPostsFound: "Публикации не найдены",
    noDescription: "Нет описания",
    dateFormatLocale: "ru-RU",
    errorFetchingUser: "Не удалось загрузить пользователя",
    errorFetchingPosts: "Не удалось загрузить публикации",
    errorOccurred: "Произошла ошибка при загрузке публикаций",
  },
};

export default function News() {
  const locale = useLocale() as Locale;
  const t = i18n[locale];

  const [posts, setPosts] = useState<Post[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAdmin, setIsAdmin] = useState(false);
  const pageSize = 20;

  // State for the create post modal
  const [showCreateModal, setShowCreateModal] = useState(false);

  const httpRequest = useHttpRequest();

  // Fetch current user to check if admin
  useEffect(() => {
    const fetchCurrentUser = async () => {
      try {
        const response = await httpRequest("/api/auth/me");
        if (response.ok) {
          const userData = await response.json();
          setIsAdmin(userData.role === "admin");
        }
      } catch (err) {
        console.error(`${t.errorFetchingUser}:`, err);
      }
    };

    fetchCurrentUser();
  }, []);

  // Function to fetch posts
  const fetchPosts = async () => {
    setLoading(true);
    try {
      const response = await httpRequest(`/api/post?page=${currentPage}&size=${pageSize}`);

      if (!response.ok) {
        throw new Error(`${t.errorFetchingPosts}: ${response.statusText}`);
      }

      const data: Post[] = await response.json();
      setPosts(data);

      // For now, we'll just set a placeholder for total pages
      // In a real app, you'd get this from the API response headers or metadata
      setTotalPages(Math.ceil(data.length / pageSize) || 1);
    } catch (err) {
      setError(err instanceof Error ? err.message : t.errorOccurred);

      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Fetch posts when the page changes

  useEffect(() => {
    fetchPosts();
  }, [currentPage, httpRequest]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleCreateModalOpen = () => {
    setShowCreateModal(true);
  };

  const handleCreateModalClose = () => {
    setShowCreateModal(false);
  };

  const handlePostCreated = () => {
    setCurrentPage(1);
    fetchPosts();
  };

  return (
    <Container>
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h1>{t.posts}</h1>
        {isAdmin && (
          <Button variant="primary" onClick={handleCreateModalOpen}>
            {t.createPost}
          </Button>
        )}
      </div>

      {loading ? (
        <div className="text-center py-5">
          <div className="spinner-border" role="status">
            <span className="visually-hidden">{t.loading}</span>
          </div>
        </div>
      ) : error ? (
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
      ) : (
        <>
          {posts.length === 0 ? (
            <div className="text-center py-5">
              <p className="text-muted">{t.noPostsFound}</p>
            </div>
          ) : (
            <Row xs={1} sm={2} md={3} lg={4} className="g-4">
              {posts.map((post) => (
                <Col key={post.id}>
                  <Card
                    className="h-100 post-card shadow-sm"
                    style={{ cursor: "pointer", transition: "transform 0.2s" }}
                    onMouseOver={(e) => (e.currentTarget.style.transform = "translateY(-5px)")}
                    onMouseOut={(e) => (e.currentTarget.style.transform = "translateY(0)")}
                  >
                    <Link href={`/news/${post.id}`} className="text-decoration-none text-black">
                      {/* Post image carousel */}
                      <PostImageCarousel
                        images={post.images || []}
                        postId={post.id}
                      />

                      <Card.Body className="d-flex flex-column">
                        <Card.Title className="fs-5 text-truncate">
                          {findLocalizationForUserLocales(post.title)}
                        </Card.Title>

                        <Card.Text className="text-muted small" style={{ height: "3rem", overflow: "hidden" }}>
                          {findLocalizationForUserLocales(post.description) || t.noDescription}
                        </Card.Text>

                        <div className="mt-auto pt-2 text-muted small">
                          <div>
                            {post.publishedAt
                              ? formatDate(new Date(post.publishedAt), t.dateFormatLocale)
                              : formatDate(new Date(post.createdAt), t.dateFormatLocale)}
                          </div>
                          {post.status !== "published" && (
                            <span className="badge bg-secondary me-1">{post.status}</span>
                          )}
                        </div>
                      </Card.Body>
                    </Link>
                  </Card>
                </Col>
              ))}
            </Row>
          )}

          {totalPages > 1 && (
            <div className="d-flex justify-content-center mt-4">
              <Pagination>
                <Pagination.First
                  onClick={() => handlePageChange(1)}
                  disabled={currentPage === 1}
                />
                <Pagination.Prev
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                />

                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  // Show pagination centered on current page
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else {
                    const start = Math.max(1, Math.min(currentPage - 2, totalPages - 4));
                    pageNum = start + i;
                  }

                  return (
                    <Pagination.Item
                      key={pageNum}
                      active={pageNum === currentPage}
                      onClick={() => handlePageChange(pageNum)}
                    >
                      {pageNum}
                    </Pagination.Item>
                  );
                })}

                <Pagination.Next
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                />
                <Pagination.Last
                  onClick={() => handlePageChange(totalPages)}
                  disabled={currentPage === totalPages}
                />
              </Pagination>
            </div>
          )}
        </>
      )}

      {/* Create Post Modal */}
      <CreatePostModal
        show={showCreateModal}
        onHide={handleCreateModalClose}
        onPostCreated={handlePostCreated}
      />
    </Container>
  );
}
