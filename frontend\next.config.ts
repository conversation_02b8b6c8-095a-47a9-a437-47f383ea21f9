import type { NextConfig } from "next";
import createNextIntlPlugin from "next-intl/plugin";

const nextConfig: NextConfig = {
  async rewrites() {
    return [
      {
        source: "/api/images/:path*",
        destination: `http://${process.env.MINIO_HOST}:9000/:path*`,
      },
      {
        source: "/api/:path*",
        destination: `http://${process.env.BACKEND_HOST}:3001/:path*`,
      },
    ];
  },
};

const withNextIntl = createNextIntlPlugin();

export default withNextIntl(nextConfig);
