"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const graphql_1 = require("@nestjs/graphql");
const promises_1 = require("fs/promises");
const path_1 = require("path");
const debug = process.argv.includes("--debug");
const watch = process.argv.includes("--watch");
const definitionsFactory = new graphql_1.GraphQLDefinitionsFactory();
const additionalHeader = `
import type { z } from "zod";

export type IResolver<
    TQuery extends keyof IQuery,
    TMutation extends keyof IMutation,
> = Pick<IQuery, TQuery> & Pick<IMutation, TMutation>;
type Satisfies<T, U extends T> = U;

type Satisfies<
    TGql extends Record<string, unknown>,
    TZod extends z.ZodTypeAny,
> =
    TGql extends z.infer<TZod>
        ? z.infer<TZod> extends TGql
            ? TGql
            : never
        : never;
`;
function generateZodSatisfies(typename) {
    return `
export type Satisfies${typename}<T extends z.ZodTypeAny> = Satisfies<${typename}, T>;
    `;
}
definitionsFactory
    .generate({
    typePaths: ["./src/**/*.graphql"],
    path: "./src/graphql.d.ts",
    outputAs: "interface",
    customScalarTypeMapping: {
        DateTime: "string | number | Date",
    },
    additionalHeader,
    enumsAsTypes: true,
    emitTypenameField: true,
    debug,
    watch,
})
    .then(async () => {
    const filePath = path_1.default.join(process.cwd(), "src/graphql.d.ts");
    const fileContent = await promises_1.default.readFile(filePath, "utf8");
    const interfaces = [
        ...fileContent.matchAll(/export interface (.+?) \{/g),
    ];
    const satisfiesBlocks = interfaces.map((match) => {
        const typename = match[1];
        if (!typename) {
            throw new Error("No typename found for: " + match[0]);
        }
        return generateZodSatisfies(typename);
    });
    const updatedFileContent = [
        fileContent.replaceAll("?:", ":"),
        ...satisfiesBlocks,
    ].join("\n");
    await promises_1.default.writeFile(filePath, updatedFileContent);
});
//# sourceMappingURL=generate-graphql.js.map