"use client";

import { useState } from "react";
import { Mo<PERSON>, But<PERSON>, Form, Alert } from "react-bootstrap";
import { useHttpRequest } from "@/app/hooks/use-http-request";
import { useLocale } from "next-intl";
import { Locale } from "@/app/types";

interface AddMemberModalProps {
  show: boolean;
  onHide: () => void;
  communeId: string;
  onMemberAdded: () => void;
}

const i18n = {
  en: {
    addMember: "Add Member",
    memberAddedSuccess: "Member added successfully!",
    email: "Email",
    enterEmail: "Enter user email",
    cancel: "Cancel",
    add: "Add",
    adding: "Adding...",
    provideEmail: "Please provide an email address.",
    failedToAdd: "Failed to add member",
    unexpectedError: "An unexpected error occurred. Please try again.",
  },

  ru: {
    addMember: "Добавить участника",
    memberAddedSuccess: "Участник успешно добавлен!",
    email: "Email",
    enterEmail: "Введите email пользователя",
    cancel: "Отмена",
    add: "Добавить",
    adding: "Добавление...",
    provideEmail: "Пожалуйста, укажите email адрес.",
    failedToAdd: "Не удалось добавить участника",
    unexpectedError: "Произошла непредвиденная ошибка. Пожалуйста, попробуйте снова.",
  },
};

export function AddMemberModal({ show, onHide, communeId, onMemberAdded }: AddMemberModalProps) {
  const locale = useLocale() as Locale;
  const t = i18n[locale];

  const [userId, setUserId] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const httpRequest = useHttpRequest();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!userId.trim()) {
      setError(t.provideEmail);
      return;
    }

    setIsSubmitting(true);
    setError(null);
    setSuccess(false);

    try {
      const response = await httpRequest(`/api/commune/${communeId}/member`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          actorType: "user",
          actorId: userId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || t.failedToAdd);
      }

      setSuccess(true);
      setUserId("");
      onMemberAdded();

      // Close modal after a short delay
      setTimeout(() => {
        onHide();
      }, 1500);
    } catch (err) {
      setError(err instanceof Error ? err.message : t.unexpectedError);
      console.error(err);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setUserId("");
    setError(null);
    setSuccess(false);
    onHide();
  };

  return (
    <Modal show={show} onHide={handleClose} centered>
      <Modal.Header closeButton>
        <Modal.Title>{t.addMember}</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        {error && (
          <Alert variant="danger" className="mb-3">
            {error}
          </Alert>
        )}

        {success && (
          <Alert variant="success" className="mb-3">
            {t.memberAddedSuccess}
          </Alert>
        )}

        <Form onSubmit={handleSubmit}>
          <Form.Group className="mb-3" controlId="userId">
            <Form.Label>{t.email}</Form.Label>
            <Form.Control
              type="text"
              placeholder={t.enterEmail}
              value={userId}
              onChange={(e) => setUserId(e.target.value)}
              disabled={isSubmitting || success}
              required
            />
          </Form.Group>
        </Form>
      </Modal.Body>
      <Modal.Footer>
        <Button
          variant="secondary"
          onClick={handleClose}
          disabled={isSubmitting}
        >
          {t.cancel}
        </Button>
        <Button
          variant="primary"
          onClick={handleSubmit}
          disabled={!userId.trim() || isSubmitting || success}
        >
          {isSubmitting ? t.adding : t.add}
        </Button>
      </Modal.Footer>
    </Modal>
  );
}
