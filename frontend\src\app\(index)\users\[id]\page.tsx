"use client";

import { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, Spin<PERSON>, Badge } from "react-bootstrap";
import { useParams } from "next/navigation";
import { useHttpRequest } from "@/app/hooks/use-http-request";
import { findLocalizationForUserLocales, Localization } from "@/utils/find-localization";
import { DetailImageCarousel } from "./detail-image-carousel";
import { useLocale } from "next-intl";
import { Locale } from "@/app/types";

interface UserImage {
  id: string;
  url: string;
  source: string;
}

interface User {
  id: string;
  email: string;
  role: "user" | "admin" | "moderator";
  name: Localization[];
  description: Localization[];
  images?: UserImage[];
  createdAt: string;
  updatedAt: string;
}

const i18n = {
  en: {
    loading: "Loading...",
    userNotFound: "User not found",
    noDescription: "No description available",
    userDetails: "User Details",
    joinedOn: "Joined on",
    errorFetchingUser: "Failed to fetch user",
    errorOccurred: "An error occurred while fetching user",
    dateFormatLocale: "en-US",
  },

  ru: {
    loading: "Загрузка...",
    userNotFound: "Пользователь не найден",
    noDescription: "Нет описания",
    userDetails: "Информация о пользователе",
    joinedOn: "Дата регистрации",
    errorFetchingUser: "Не удалось загрузить пользователя",
    errorOccurred: "Произошла ошибка при загрузке пользователя",
    dateFormatLocale: "ru-RU",
  },
};

export default function UserDetailPage() {
  const locale = useLocale() as Locale;
  const t = i18n[locale];

  const params = useParams();
  const userId = params.id as string;

  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const httpRequest = useHttpRequest();

  useEffect(() => {
    const fetchUser = async () => {
      setLoading(true);
      try {
        const response = await httpRequest(`/api/user/${userId}`);

        if (!response.ok) {
          throw new Error(`${t.errorFetchingUser}: ${response.statusText}`);
        }

        const data = await response.json();
        setUser(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : t.errorOccurred);
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, [userId]);

  if (loading) {
    return (
      <Container className="py-5 text-center">
        <Spinner animation="border" role="status">
          <span className="visually-hidden">{t.loading}</span>
        </Spinner>
      </Container>
    );
  }

  if (error || !user) {
    return (
      <Container className="py-5">
        <Alert variant="danger">
          {error || t.userNotFound}
        </Alert>
      </Container>
    );
  }

  const userName = findLocalizationForUserLocales(user.name) || user.email;
  const userDescription = findLocalizationForUserLocales(user.description);
  const joinDate = new Date(user.createdAt);
  const formattedDate = joinDate.toLocaleDateString(t.dateFormatLocale, {
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  return (
    <Container className="py-4">
      <Row>
        <Col lg={8}>
          {/* Image Carousel */}
          <DetailImageCarousel images={user.images || []} />

          {/* User Information */}
          <div className="mb-4">
            <div className="d-flex justify-content-between align-items-center mb-3">
              <h2 className="mb-0">{userName}</h2>
              <Badge bg={user.role === "admin" ? "danger" : user.role === "moderator" ? "warning" : "primary"}>
                {user.role}
              </Badge>
            </div>
            <p className="lead text-muted">{userDescription || t.noDescription}</p>
          </div>
        </Col>

        <Col lg={4}>
          <Card className="shadow-sm mb-4">
            <Card.Body>
              <Card.Title>{t.userDetails}</Card.Title>
              <hr />
              <div className="d-flex align-items-center mb-3">
                <i className="bi bi-envelope-fill me-2 text-primary"></i>
                <span>{user.email}</span>
              </div>
              <div className="d-flex align-items-center">
                <i className="bi bi-calendar-date me-2 text-primary"></i>
                <span>{t.joinedOn} {formattedDate}</span>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
}
