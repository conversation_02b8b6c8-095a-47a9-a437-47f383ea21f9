import Link from "next/link";
import { Locale } from "@/app/types";

const i18n = {
  en: {
    title: "Our Initiatives",
    description: "Innovative projects we're developing to improve communication and organization",
    initiatives: {
      theLaw: {
        title: "The Law",
        description: "An adaptive system of principles aimed at building a rational, free, and just society based on reason and human dignity.",
      },
      rules: {
        title: "Rules",
        description: "Practical guidelines for our digital community that promote constructive interaction and a positive environment.",
      },
      newCalendar: {
        title: "New Calendar",
        description: "A revolutionary fixed calendar designed for the modern digital age with consistent structure and intuitive design.",
      },
      newEnglish: {
        title: "New English",
        description: "A systematic approach to reforming English spelling and pronunciation for improved consistency and clarity.",
      },
    },
  },

  ru: {
    title: "Наши инициативы",
    description: "Инновационные проекты, которые мы разрабатываем для улучшения коммуникаций и организации",
    initiatives: {
      theLaw: {
        title: "Право",
        description: "Адаптивная система принципов, направленных на создание рационального, свободного и справедливого общества на основе разума и человеческой достоинства.",
      },
      rules: {
        title: "Правила",
        description: "Практические рекомендации для нашей цифровой общины, которые способствуют конструктивному взаимодействию и положительной среде.",
      },
      newCalendar: {
        title: "Новый календарь",
        description: "Новый календарь, разработанный для современной цифровой эпохи с последовательной структурой и интуитивным дизайном.",
      },
      newEnglish: {
        title: "Новый английский",
        description: "Систематический подход к реформированию английской орфографии и произношения для улучшения согласованности и ясности.",
      },
    },
  },
};

type Props = {
  locale: Locale;
}

export async function InitiativesSection({ locale }: Props) {
  const t = i18n[locale];

  return (
    <section className="py-5 py-lg-6 bg-light" id="initiatives">
      <div className="container">
        <div className="row justify-content-center mb-5">
          <div className="col-lg-8 text-center">
            <h2 className="display-5 fw-bold mb-4">{t.title}</h2>
            <p className="text-secondary fs-5">{t.description}</p>
          </div>
        </div>

        <div className="row justify-content-center">
          <div className="col-lg-10">
            <div className="row g-4">
              <Initiative
                title={t.initiatives.theLaw.title}
                description={t.initiatives.theLaw.description}
                link="/the-law"
                icon="⚖️"
                color="danger"
              />

              <Initiative
                title={t.initiatives.rules.title}
                description={t.initiatives.rules.description}
                link="/rules"
                icon="📋"
                color="info"
              />

              <Initiative
                title={t.initiatives.newCalendar.title}
                description={t.initiatives.newCalendar.description}
                link="/new-calendar"
                icon="📅"
                color="primary"
              />

              <Initiative
                title={t.initiatives.newEnglish.title}
                description={t.initiatives.newEnglish.description}
                link="/new-english"
                icon="🔤"
                color="success"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

function Initiative({
  title,
  description,
  link,
  icon,
  color,
}: {
  title: string,
  description: string,
  link: string,
  icon: string,
  color: string,
}) {
  return (
    <div className="col-md-6 col-lg-6">
      <Link
        href={link}
        className="text-decoration-none"
      >
        <div className={`card h-100 border-0 shadow-sm hover-shadow rounded-4 overflow-hidden bg-${color}-subtle`}>
          <div className="card-body p-4">
            <div className="d-flex align-items-center mb-4">
              <div className={`bg-${color}-subtle rounded-circle p-3 me-3`}>
                <span className="fs-2">{icon}</span>
              </div>
              <h3 className={`h4 fw-bold mb-0 text-${color}-emphasis`}>
                {title}
              </h3>
            </div>
            <p className="mb-0">{description}</p>
          </div>
          <div className={`card-footer bg-${color}-subtle border-0 py-3`}>
            <div className="d-flex align-items-center">
              <span className={`text-${color}-emphasis fw-semibold`}>
                Learn more
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" className="bi bi-arrow-right ms-2" viewBox="0 0 16 16">
                  <path fillRule="evenodd" d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z" />
                </svg>
              </span>
            </div>
          </div>
        </div>
      </Link>
    </div>
  );
}
