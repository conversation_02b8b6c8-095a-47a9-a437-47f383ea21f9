"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const promises_1 = require("node:fs/promises");
const node_path_1 = require("node:path");
const node_crypto_1 = require("node:crypto");
const root = node_path_1.default.join(process.cwd(), "src/test/eds");
const PRIVATE_KEY_PASSPHRASE = "secret";
function generatePair(privateKeyPassphrase) {
    const { publicKey, privateKey } = (0, node_crypto_1.generateKeyPairSync)("rsa", {
        modulusLength: 4096,
        publicKeyEncoding: {
            type: "spki",
            format: "pem",
        },
        privateKeyEncoding: {
            type: "pkcs8",
            format: "pem",
            cipher: "aes-256-cbc",
            passphrase: privateKeyPassphrase,
        },
    });
    return { publicKey, privateKey };
}
function sign(content, privateKey, privateKeyPassphrase) {
    const signer = (0, node_crypto_1.createSign)("rsa-sha256");
    signer.update(content);
    const privateKeyObject = (0, node_crypto_1.createPrivateKey)({
        key: privateKey,
        type: "pkcs8",
        format: "pem",
        passphrase: privateKeyPassphrase,
    });
    const signature = signer.sign(privateKeyObject);
    return { signature };
}
function verify(content, publicKey, signature) {
    const verifier = (0, node_crypto_1.createVerify)("rsa-sha256");
    verifier.update(content);
    const isValid = verifier.verify(publicKey, signature);
    return { isValid };
}
(async () => {
    const publicKey = await promises_1.default.readFile(node_path_1.default.join(root, "pair1-public.pem"), "utf-8");
    const privateKey = await promises_1.default.readFile(node_path_1.default.join(root, "pair1-private.pem"), "utf-8");
    const content = await promises_1.default.readFile(node_path_1.default.join(root, "test1.txt"), "utf-8");
    const { signature } = sign(content, privateKey, PRIVATE_KEY_PASSPHRASE);
    console.log(signature);
    console.log(signature.toString("base64"));
    const { isValid } = verify(content, publicKey, signature);
    console.log();
    console.log({ isValid, v: 2 });
})();
//# sourceMappingURL=test.js.map