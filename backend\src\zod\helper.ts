import { z } from "zod";

export type Infer<T extends z.ZodTypeAny> = Normalize<z.infer<T>>;

export type InferObject<T extends Record<string, z.ZodType>> = {
    [K in keyof T]: z.infer<T[K]>;
};

export function Typename<const T extends string>(name: T) {
    return z.literal(name).default(name);
}

export const ToDateTime = z
    .union([z.number(), z.string(), z.date()])
    .pipe(z.coerce.date());

export function JsonToObject<T extends z.ZodRawShape>(schema: T) {
    return z
        .string()
        .transform((value) => JSON.parse(value))
        .pipe(z.object(schema));
}

export function FormDataToObject<T extends z.ZodRawShape>(schema: T) {
    return z.object({
        data: JsonToObject(schema),
    });
}

export type Locale = z.infer<typeof Locale>;
export const Locale = z.enum(["en", "ru"]).describe("Locale");

export const Locales = z.array(Locale).min(1);

export type Localization = z.infer<typeof Localization>;
export const Localization = z.object({
    locale: Locale,
    value: z.string().nonempty(),
});

export const Localizations = z.array(Localization);

export type Uuid = z.infer<typeof Uuid>;
export const Uuid = z.string().uuid().describe("UUID");

export type Email = z.infer<typeof Email>;
export const Email = z.string().email().describe("Email");

export type Image = Infer<typeof Image>;
export const Image = z.object({
    id: Uuid,
    url: z.string(),
    createdAt: ToDateTime,
    updatedAt: ToDateTime,
});

export const intSchema = z.coerce.number().int();
export const positiveIntSchema = intSchema.positive();

export const pagination = {
    offset: intSchema.default(0).describe("Offset"),
    limit: positiveIntSchema.max(100).default(20).describe("Limit"),

    page: positiveIntSchema.default(1).describe("Page number"),
    size: positiveIntSchema.max(100).default(20).describe("Page size"),
};

export type Pagination = z.infer<typeof Pagination>;
export const Pagination = z
    .union([
        z.object({
            page: pagination.page,
            size: pagination.size,
        }),

        z.object({
            limit: pagination.limit,
            offset: pagination.offset,
        }),
    ])
    .optional();

export function GqlObject<
    TTypename extends string,
    TShape extends z.ZodRawShape,
>(
    typename: TTypename,
    shape: TShape,
): z.ZodObject<{ __typename: z.ZodDefault<z.ZodLiteral<TTypename>> } & TShape> {
    return z.object({
        __typename: z.literal(typename).default(typename),
        ...shape,
    });
}

export function parseInput<T extends z.ZodTypeAny>(
    schema: T,
    value: z.input<T>,
): z.output<T> {
    return schema.parse(value);
}

export function parseUnknown<T extends z.ZodTypeAny>(
    schema: T,
    value: unknown,
): z.output<T> {
    return schema.parse(value);
}
