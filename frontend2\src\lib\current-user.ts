import { browser } from "$app/environment";
import { User } from "./dto";

export function getCurrentUser(): User | null {
  if (!browser) return null;

  const savedUser = localStorage.getItem("user");

  if (!savedUser) return null;

  const parsedUser = User.safeParse(JSON.parse(savedUser));

  if (!parsedUser.success) {
    localStorage.removeItem("user");

    return null;
  }

  return parsedUser.data;
}

export function setCurrentUser(user: User) {
  if (!browser) return;

  localStorage.setItem("user", JSON.stringify(user));
}
