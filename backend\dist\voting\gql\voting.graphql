"voting"
type Voting {
    id: ID!

    votesRequired: Int!
    endsAt: DateTime!

    title: [Localization!]!
    description: [Localization!]!

    options: [VotingOption!]!

    createdAt: DateTime!
    updatedAt: DateTime!
}

type VotingOption {
    id: ID!

    title: [Localization!]!

    createdAt: DateTime!
    updatedAt: DateTime!
}

type Query {
    getVoting(id: ID!): Voting!
    getVotings: [Voting!]!
}

input CreateVotingInput {
    votesRequired: Int!
    endsAt: DateTime!

    title: [LocalizationInput!]!
    description: [LocalizationInput!]!

    options: [CreateVotingOptionInput!]!
}

input CreateVotingOptionInput {
    title: [LocalizationInput!]!
}

type Mutation {
    createVoting(input: CreateVotingInput!): Voting!
}
