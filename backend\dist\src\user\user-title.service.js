"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserTitleService = void 0;
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
const utils_1 = require("../utils");
const errors_1 = require("../common/errors");
const base_service_1 = require("../common/base-service");
const prisma_service_1 = require("../prisma/prisma.service");
let UserTitleService = class UserTitleService extends base_service_1.BaseService {
    constructor(prisma) {
        super("user-title");
        this.prisma = prisma;
    }
    async update(id, data, user) {
        await this.canChange(id, user);
        return await this.updateOne(id, data);
    }
    async canGet(id, user) {
        await this.getOneOrThrow(id);
        return true;
    }
    async canChange(id, user) {
        const userTitle = await this.getOneOrThrow(id);
        if (user.role !== client_1.UserRole.admin) {
            if (userTitle.ownerId !== user.id) {
                throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_owner"));
            }
        }
        return true;
    }
    async check(ids) {
        return await this._check(ids, this.prisma.userTitle, "user_title");
    }
    async getOne(id) {
        return await this.prisma.userTitle.findUnique({
            where: { id, deletedAt: null },
        });
    }
    async getOneOrThrow(id) {
        const userTitle = await this.getOne(id);
        if (!userTitle) {
            throw this.createNotFoundException();
        }
        return userTitle;
    }
    async getMany(where, pagination) {
        return await this.prisma.userTitle.findMany({
            ...(0, utils_1.toPrismaPagination)(pagination),
            where: {
                ...where,
                deletedAt: null,
            },
        });
    }
    async createOne(data) {
        return await this.prisma.userTitle.create({
            data,
        });
    }
    async createMany(data) {
        return await this.prisma.userTitle.createMany({
            data,
        });
    }
    async updateOne(id, data) {
        return await this.prisma.userTitle.update({
            where: {
                id,
                deletedAt: null,
            },
            data,
        });
    }
    async updateMany(where, data) {
        return await this.prisma.userTitle.updateMany({
            where: {
                ...where,
                deletedAt: null,
            },
            data,
        });
    }
    async softDeleteOne(id) {
        return await this.updateOne(id, { deletedAt: new Date() });
    }
    async softDeleteMany(where) {
        return await this.updateMany({
            ...where,
            deletedAt: null,
        }, { deletedAt: new Date() });
    }
    async deleteOne(id) {
        return await this.prisma.userTitle.delete({
            where: {
                id,
                deletedAt: null,
            },
        });
    }
    async deleteMany(where) {
        return await this.prisma.userTitle.deleteMany({
            where: {
                ...where,
                deletedAt: null,
            },
        });
    }
};
exports.UserTitleService = UserTitleService;
exports.UserTitleService = UserTitleService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], UserTitleService);
//# sourceMappingURL=user-title.service.js.map