"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HttpCurrentUser = void 0;
const common_1 = require("@nestjs/common");
const consts_1 = require("../../consts");
exports.HttpCurrentUser = (0, common_1.createParamDecorator)((_, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    const user = request.user;
    if (!user) {
        throw new common_1.UnauthorizedException();
    }
    request[consts_1.HTTP_CONTEXT_USER_SYMBOL] = user;
    return user;
});
//# sourceMappingURL=current-user.decorator.js.map