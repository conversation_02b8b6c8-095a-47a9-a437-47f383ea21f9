// reactor-post
model ReactorPost {
    @@map("reactor_posts")

    id String @id @db.Uuid @default(uuid(7))

    tags Tag[] @relation("reactor_post_tags")

    authorId String @db.Uuid @map("author_id")
    author   User   @relation("user_reactor_posts", fields: [authorId], references: [id])

    title Localization[] @relation("reactor_post_title")
    body  Localization[] @relation("reactor_post_body")

    isAnonymous     Boolean @map("is_anonymous")     @default(false)
    anonimityReason String? @map("anonimity_reason")

    deleteReason String? @map("delete_reason")

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
    deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)

    @@index([authorId])
}

model ReactorPostInternalNumber {
    @@map("reactor_post_internal_numbers")

    id String @id @db.Uuid @default(uuid(7))

    postId String @map("post_id") @db.Uuid @unique

    internalNumber Int @map("internal_number")

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
}

// reactor-comment
model ReactorComment {
    @@map("reactor_comments")

    id String @id @db.Uuid @default(uuid(7))

    authorId String @db.Uuid @map("author_id")
    author   User   @relation("user_reactor_comments", fields: [authorId], references: [id])

    postId String @map("post_id") @db.Uuid

    path           String
    internalNumber Int    @map("internal_number")

    isAnonymous     Boolean @map("is_anonymous")     @default(false)
    anonimityReason String? @map("anonimity_reason")

    body Localization[] @relation("reactor_comment_body")

    deleteReason String? @map("delete_reason")

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
    deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)

    @@unique([postId, internalNumber])
    @@unique([postId, path])

    @@index([postId, path])
}

// reactor-entity-type
enum ReactorEntityType {
    @@map("reactor_entity_type")

    post
    comment
}

enum ReactorRatingType {
    @@map("reactor_rating_type")

    like
    dislike
}

model ReactorRating {
    @@map("reactor_ratings")

    id String @id @db.Uuid @default(uuid(7))

    userId String @map("user_id") @db.Uuid
    user   User   @relation("user_reactor_ratings", fields: [userId], references: [id])

    entityType ReactorEntityType @map("entity_type")
    entityId   String            @map("entity_id")   @db.Uuid

    type ReactorRatingType

    createdAt DateTime @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime @map("updated_at") @db.Timestamptz(3) @updatedAt

    @@unique([userId, entityType, entityId])

    @@index([userId])
    @@index([entityType, entityId])
}

model ReactorUsefulness {
    @@map("reactor_usefulnesses")

    id String @id @db.Uuid @default(uuid(7))

    userId String @map("user_id") @db.Uuid
    user   User   @relation("user_reactor_usefulnesses", fields: [userId], references: [id])

    entityType ReactorEntityType @map("entity_type")
    entityId   String            @map("entity_id")   @db.Uuid

    value Int

    createdAt DateTime @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime @map("updated_at") @db.Timestamptz(3) @updatedAt

    @@unique([userId, entityType, entityId])

    @@index([userId])
    @@index([entityType, entityId])
}
