import { z, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "src/zod";
export declare const color: z.ZodNullable<z.ZodString>;
export declare const isActive: z.ZodBoolean;
export type User = ZodHelper.Infer<typeof User>;
export declare const User: z.ZodObject<{
    id: z.ZodString;
    email: z.ZodString;
    role: z.<PERSON><{
        admin: "admin";
        moderator: "moderator";
        user: "user";
    }>;
    name: z.<PERSON><z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    description: z.<PERSON><PERSON><z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodType<PERSON>ny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    images: z.Zod<PERSON>al<z.Zod<PERSON>y<z.ZodObject<{
        id: z.ZodString;
        url: z.ZodString;
        createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
        updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        url: string;
    }, {
        id: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
        url: string;
    }>, "many">>;
    createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    id: string;
    email: string;
    role: "user" | "admin" | "moderator";
    createdAt: Date;
    updatedAt: Date;
    name: {
        locale: "en" | "ru";
        value: string;
    }[];
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    images?: {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        url: string;
    }[] | undefined;
}, {
    id: string;
    email: string;
    role: "user" | "admin" | "moderator";
    createdAt: string | number | Date;
    updatedAt: string | number | Date;
    name: {
        locale: "en" | "ru";
        value: string;
    }[];
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    images?: {
        id: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
        url: string;
    }[] | undefined;
}>;
export declare const Users: z.ZodArray<z.ZodObject<{
    id: z.ZodString;
    email: z.ZodString;
    role: z.ZodNativeEnum<{
        admin: "admin";
        moderator: "moderator";
        user: "user";
    }>;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">;
    images: z.ZodOptional<z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        url: z.ZodString;
        createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
        updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        url: string;
    }, {
        id: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
        url: string;
    }>, "many">>;
    createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    id: string;
    email: string;
    role: "user" | "admin" | "moderator";
    createdAt: Date;
    updatedAt: Date;
    name: {
        locale: "en" | "ru";
        value: string;
    }[];
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    images?: {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        url: string;
    }[] | undefined;
}, {
    id: string;
    email: string;
    role: "user" | "admin" | "moderator";
    createdAt: string | number | Date;
    updatedAt: string | number | Date;
    name: {
        locale: "en" | "ru";
        value: string;
    }[];
    description: {
        locale: "en" | "ru";
        value: string;
    }[];
    images?: {
        id: string;
        createdAt: string | number | Date;
        updatedAt: string | number | Date;
        url: string;
    }[] | undefined;
}>, "many">;
export type UpdateUser = ZodHelper.Infer<typeof UpdateUser>;
export declare const UpdateUser: z.ZodObject<{
    name: z.ZodOptional<z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">>;
    description: z.ZodOptional<z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        locale: "en" | "ru";
        value: string;
    }, {
        locale: "en" | "ru";
        value: string;
    }>, "many">>;
}, "strip", z.ZodTypeAny, {
    name?: {
        locale: "en" | "ru";
        value: string;
    }[] | undefined;
    description?: {
        locale: "en" | "ru";
        value: string;
    }[] | undefined;
}, {
    name?: {
        locale: "en" | "ru";
        value: string;
    }[] | undefined;
    description?: {
        locale: "en" | "ru";
        value: string;
    }[] | undefined;
}>;
export type UserTitle = ZodHelper.Infer<typeof UserTitle>;
export declare const UserTitle: z.ZodObject<{
    id: z.ZodString;
    ownerId: z.ZodNullable<z.ZodString>;
    isActive: z.ZodBoolean;
    color: z.ZodNullable<z.ZodString>;
    createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    ownerId: string | null;
    isActive: boolean;
    color: string | null;
}, {
    id: string;
    createdAt: string | number | Date;
    updatedAt: string | number | Date;
    ownerId: string | null;
    isActive: boolean;
    color: string | null;
}>;
export declare const UserTitles: z.ZodArray<z.ZodObject<{
    id: z.ZodString;
    ownerId: z.ZodNullable<z.ZodString>;
    isActive: z.ZodBoolean;
    color: z.ZodNullable<z.ZodString>;
    createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    ownerId: string | null;
    isActive: boolean;
    color: string | null;
}, {
    id: string;
    createdAt: string | number | Date;
    updatedAt: string | number | Date;
    ownerId: string | null;
    isActive: boolean;
    color: string | null;
}>, "many">;
export type UpdateUserTitle = ZodHelper.Infer<typeof UpdateUserTitle>;
export declare const UpdateUserTitle: z.ZodObject<{
    color: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    isActive: z.ZodOptional<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    isActive?: boolean | undefined;
    color?: string | null | undefined;
}, {
    isActive?: boolean | undefined;
    color?: string | null | undefined;
}>;
