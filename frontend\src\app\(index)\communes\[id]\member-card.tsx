"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>, Modal } from "react-bootstrap";
import { CommuneImageCarousel } from "../commune-image-carousel";
import { findLocalizationForUserLocales, Localization } from "@/utils/find-localization";
import Link from "next/link";
import styles from "./member-card.module.css";
import { useHttpRequest } from "@/app/hooks/use-http-request";
import { useLocale } from "next-intl";
import { Locale } from "@/app/types";

type CommuneMemberType = "user" | "commune";

interface MemberCardProps {
  id: string;
  actorType: CommuneMemberType;
  actorId: string;
  name: Localization[];
  isHead: boolean;
  joinedAt: string;
  communeId: string;
  isCurrentUserHead: boolean;
  onMemberRemoved: () => void;
  images?: {
    id: string;
    url: string;
    source: string;
  }[];
}

const i18n = {
  en: {
    head: "Head",
    joined: "Joined",
    remove: "Remove",
    removing: "Removing...",
    confirmRemoval: "Confirm Removal",
    confirmRemoveMessage: "Are you sure you want to remove",
    fromCommune: "from this commune?",
    cannotUndo: "This action cannot be undone.",
    cancel: "Cancel",
    removeMember: "Remove Member",
    errorRemovingMember: "Failed to remove member",
    errorOccurred: "An error occurred while removing member",
    dateFormatLocale: "en-US",
  },

  ru: {
    head: "Глава",
    joined: "Присоединился",
    remove: "Удалить",
    removing: "Удаление...",
    confirmRemoval: "Подтвердите удаление",
    confirmRemoveMessage: "Вы уверены, что хотите удалить",
    fromCommune: "из этой коммуны?",
    cannotUndo: "Это действие нельзя отменить.",
    cancel: "Отмена",
    removeMember: "Удалить участника",
    errorRemovingMember: "Не удалось удалить участника",
    errorOccurred: "Произошла ошибка при удалении участника",
    dateFormatLocale: "ru-RU",
  },
};

export function MemberCard({
  id,
  actorType,
  actorId,
  name,
  isHead,
  joinedAt,
  communeId,
  isCurrentUserHead,
  onMemberRemoved,
  images = [],
}: MemberCardProps) {
  const locale = useLocale() as Locale;
  const t = i18n[locale];

  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [isRemoving, setIsRemoving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const httpRequest = useHttpRequest();
  const memberName = findLocalizationForUserLocales(name);
  const profileUrl = actorType === "user" ? `/users/${actorId}` : `/communes/${actorId}`;

  // Format join date
  const joinDate = new Date(joinedAt);
  const formattedDate = joinDate.toLocaleDateString(t.dateFormatLocale, {
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  // Don't allow removing the head member
  const canRemove = isCurrentUserHead && !isHead;

  const handleRemoveClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setShowConfirmModal(true);
  };

  const handleConfirmRemove = async () => {
    setIsRemoving(true);
    setError(null);

    try {
      const response = await httpRequest(`/api/commune?id=${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || t.errorRemovingMember);
      }

      setShowConfirmModal(false);
      onMemberRemoved();
    } catch (err) {
      setError(err instanceof Error ? err.message : t.errorOccurred);
      console.error(err);
    } finally {
      setIsRemoving(false);
    }
  };

  return (
    <>
      <Card
        className={`h-100 shadow-sm ${isHead ? styles.headMember : ""}`}
      >
        <CommuneImageCarousel
          images={images}
          communeId={actorId}
        />
        <Link href={profileUrl} className="text-decoration-none text-black">
          <Card.Body className="d-flex flex-column">
            <Card.Title className="fs-5 text-truncate">
              {memberName}
            </Card.Title>
            <div className="mt-auto d-flex justify-content-between align-items-center">
              <small className="text-muted">
                {t.joined} {formattedDate}
              </small>
              {isHead && (
                <span className="badge bg-warning text-dark">{t.head}</span>
              )}
            </div>

            {canRemove && (
              <div className="mt-2 text-end">
                <Button
                  variant="outline-danger"
                  size="sm"
                  onClick={handleRemoveClick}
                  className="w-100"
                >
                  {t.remove}
                </Button>
              </div>
            )}
          </Card.Body>
        </Link>
      </Card>

      {/* Confirmation Modal */}
      <Modal show={showConfirmModal} onHide={() => setShowConfirmModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>{t.confirmRemoval}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {error && <div className="alert alert-danger">{error}</div>}
          <p>{t.confirmRemoveMessage} <strong>{memberName}</strong> {t.fromCommune}</p>
          <p className="text-muted small">{t.cannotUndo}</p>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowConfirmModal(false)} disabled={isRemoving}>
            {t.cancel}
          </Button>
          <Button variant="danger" onClick={handleConfirmRemove} disabled={isRemoving}>
            {isRemoving ? t.removing : t.removeMember}
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
}
