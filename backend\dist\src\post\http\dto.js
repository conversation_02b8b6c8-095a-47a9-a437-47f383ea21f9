"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdatePostInput = exports.CreatePostInput = exports.Posts = exports.Post = exports.PostStatus = void 0;
const prisma = require("@prisma/client");
const zod_1 = require("../../zod");
exports.PostStatus = zod_1.z.nativeEnum(prisma.PostStatus);
exports.Post = zod_1.z.object({
    id: zod_1.ZodHelper.Uuid,
    title: zod_1.ZodHelper.Localizations,
    description: zod_1.ZodHelper.Localizations,
    status: exports.PostStatus,
    publishedAt: zod_1.ZodHelper.ToDateTime.nullable(),
    images: zod_1.z.array(zod_1.ZodHelper.Image).optional(),
    createdAt: zod_1.ZodHelper.ToDateTime,
    updatedAt: zod_1.ZodHelper.ToDateTime,
});
exports.Posts = zod_1.z.array(exports.Post);
exports.CreatePostInput = zod_1.ZodHelper.JsonToObject({
    title: zod_1.ZodHelper.Localizations,
    description: zod_1.ZodHelper.Localizations,
    status: exports.PostStatus,
    publishedAt: zod_1.ZodHelper.ToDateTime.nullable(),
});
exports.UpdatePostInput = zod_1.z
    .object({
    title: zod_1.ZodHelper.Localizations,
    description: zod_1.ZodHelper.Localizations,
    status: exports.PostStatus,
})
    .partial();
//# sourceMappingURL=dto.js.map