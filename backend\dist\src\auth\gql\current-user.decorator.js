"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GqlCurrentUser = void 0;
const common_1 = require("@nestjs/common");
const consts_1 = require("../../consts");
const graphql_1 = require("@nestjs/graphql");
exports.GqlCurrentUser = (0, common_1.createParamDecorator)((_, ctx) => {
    const context = graphql_1.GqlExecutionContext.create(ctx).getContext();
    const user = context.req.user;
    if (!user) {
        throw new common_1.UnauthorizedException();
    }
    context.req[consts_1.GQL_CONTEXT_USER_SYMBOL] = user;
    return user;
});
//# sourceMappingURL=current-user.decorator.js.map