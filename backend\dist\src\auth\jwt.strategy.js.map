{"version": 3, "file": "jwt.strategy.js", "sourceRoot": "", "sources": ["../../../src/auth/jwt.strategy.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,6BAAwB;AACxB,2CAA4C;AAC5C,2CAA+C;AAC/C,+CAAoD;AACpD,+CAAoD;AAI7C,IAAM,WAAW,mBAAjB,MAAM,WAAY,SAAQ,IAAA,2BAAgB,EAAC,uBAAQ,EAAE,KAAK,CAAC;IAC9D,YAAY,aAA4B;QACpC,KAAK,CAAC;YACF,cAAc,EAAE,yBAAU,CAAC,cAAc,CAAC;gBACtC,aAAW,CAAC,oBAAoB;gBAChC,yBAAU,CAAC,2BAA2B,EAAE;aAC3C,CAAC;YACF,gBAAgB,EAAE,KAAK;YACvB,WAAW,EAAE,OAAC;iBACT,MAAM,EAAE;iBACR,GAAG,CAAC,EAAE,CAAC;iBACP,KAAK,CAAC,aAAa,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;SACvD,CAAC,CAAC;IACP,CAAC;IAEO,MAAM,CAAC,oBAAoB,CAAC,GAAY;QAC5C,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;QAE1C,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;IAChC,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,OAAY;QACvB,OAAO,OAAO,CAAC;IACnB,CAAC;CACJ,CAAA;AAxBY,kCAAW;sBAAX,WAAW;IADvB,IAAA,mBAAU,GAAE;qCAEkB,sBAAa;GAD/B,WAAW,CAwBvB"}