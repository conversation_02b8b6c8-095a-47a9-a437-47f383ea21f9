import styles from "./hero-section.module.css";
import { Locale } from "@/app/types";

const i18n = {
  en: {
    title: "Commune",
    subtitle: "Construction through technological progress",
  },

  ru: {
    title: "Коммуна",
    subtitle: "Созидание через технологический прогресс",
  },
};

type Props = {
  locale: Locale;
}

export function HeroSection({ locale }: Props) {
  const t = i18n[locale];

  return (
    <section className="position-relative vh-100 d-flex align-items-center justify-content-center overflow-hidden">
      <div className="position-absolute top-0 start-0 w-100 h-100 bg-primary-gradient opacity-90 z-index-1" />

      <div
        className={`position-absolute top-0 start-0 w-100 h-100 z-index-0 ${styles.heroBackground}`}
      />

      <div className={`container z-index-2 text-center ${styles.heroContent}`}>
        <div className="col-lg-10 mx-auto">
          <h1
            className="display-1 fw-bold text-white mb-3 lh-sm"
            style={{
              textShadow: "2px 2px 4px rgba(0, 0, 0, 0.8)",
            }}
          >
            {t.title}
          </h1>

          <h3
            className="fs-2 text-light mb-5"
            style={{
              textShadow: "1px 1px 3px rgba(0, 0, 0, 0.8)",
            }}
          >
            {t.subtitle}
          </h3>
        </div>
      </div>
    </section>
  );
}
