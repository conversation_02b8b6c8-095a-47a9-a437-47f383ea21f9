server {
  listen 80;
  server_name dev.commune.my;

  location /.well-known/acme-challenge/ {
    root /var/www/html;
  }

  # Block search engine indexing
  location = /robots.txt {
    add_header Content-Type text/plain;
    return 200 "User-agent: *\nDisallow: /";
  }

  location / {
    return 301 https://$host$request_uri;
  }
}

server {
  listen 443 ssl http2;
  server_name dev.commune.my;

  ssl_certificate /etc/letsencrypt/live/dev.commune.my/fullchain.pem;
  ssl_certificate_key /etc/letsencrypt/live/dev.commune.my/privkey.pem;

  # Block search engine indexing
  location = /robots.txt {
    add_header Content-Type text/plain;
    return 200 "User-agent: *\nDisallow: /";
  }

  location / {
    proxy_pass http://localhost:3000;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto https;
  }
}