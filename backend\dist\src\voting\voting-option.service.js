"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VotingOptionService = void 0;
const common_1 = require("@nestjs/common");
const utils_1 = require("../utils");
const base_service_1 = require("../common/base-service");
const prisma_service_1 = require("../prisma/prisma.service");
let VotingOptionService = class VotingOptionService extends base_service_1.BaseService {
    constructor(prisma) {
        super("voting-option");
        this.prisma = prisma;
    }
    async canGet(id, user) {
        return true;
    }
    async check(ids) {
        return await this._check(ids, this.prisma.votingOption, "voting_option");
    }
    async getOne(id) {
        return await this.prisma.votingOption.findUnique({
            where: { id, deletedAt: null },
        });
    }
    async getOneOrThrow(id) {
        const votingOption = await this.getOne(id);
        if (!votingOption) {
            throw this.createNotFoundException();
        }
        return votingOption;
    }
    async getMany(where, pagination) {
        return await this.prisma.votingOption.findMany({
            ...(0, utils_1.toPrismaPagination)(pagination),
            where: {
                ...where,
                deletedAt: null,
            },
        });
    }
    async createOne(data) {
        return await this.prisma.votingOption.create({ data });
    }
    async createMany(data) {
        return await this.prisma.votingOption.createMany({ data });
    }
    async updateOne(id, data) {
        return await this.prisma.votingOption.update({
            where: {
                id,
                deletedAt: null,
            },
            data,
        });
    }
    async updateMany(where, data) {
        return await this.prisma.votingOption.updateMany({ where, data });
    }
    async softDeleteOne(id) {
        return await this.updateOne(id, { deletedAt: new Date() });
    }
    async softDeleteMany(where) {
        return await this.updateMany(where, { deletedAt: new Date() });
    }
    async deleteOne(id) {
        return await this.prisma.votingOption.delete({ where: { id } });
    }
    async deleteMany(where) {
        return await this.prisma.votingOption.deleteMany({ where });
    }
};
exports.VotingOptionService = VotingOptionService;
exports.VotingOptionService = VotingOptionService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], VotingOptionService);
//# sourceMappingURL=voting-option.service.js.map