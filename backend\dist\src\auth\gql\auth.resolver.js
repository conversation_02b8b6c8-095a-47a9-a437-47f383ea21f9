"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthResolver = void 0;
const common_1 = require("@nestjs/common");
const graphql_1 = require("@nestjs/graphql");
const zod_1 = require("../../zod");
const user_service_1 = require("../../user/user.service");
const Dto = require("./dto");
const jwt_auth_guard_1 = require("./jwt-auth.guard");
const current_user_decorator_1 = require("./current-user.decorator");
let AuthResolver = class AuthResolver {
    constructor(userService) {
        this.userService = userService;
    }
    test() {
        console.log("AuthResolver.test");
        return true;
    }
    async me(currentUser) {
        console.log("AuthResolver.me.currentUser", currentUser);
        const user = await this.userService.getOne(currentUser.id);
        if (!user) {
            throw new common_1.UnauthorizedException();
        }
        return zod_1.ZodHelper.parseInput(Dto.Me, {
            id: user.id,
            email: user.email,
            role: user.role,
            name: user.name,
            description: user.description,
            joinedAt: user.createdAt,
        });
    }
};
exports.AuthResolver = AuthResolver;
__decorate([
    (0, graphql_1.Query)("test"),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], AuthResolver.prototype, "test", null);
__decorate([
    (0, graphql_1.Query)("me"),
    (0, common_1.UseGuards)(jwt_auth_guard_1.GqlJwtAuthGuard),
    __param(0, (0, current_user_decorator_1.GqlCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthResolver.prototype, "me", null);
exports.AuthResolver = AuthResolver = __decorate([
    (0, graphql_1.Resolver)(),
    __metadata("design:paramtypes", [user_service_1.UserService])
], AuthResolver);
//# sourceMappingURL=auth.resolver.js.map