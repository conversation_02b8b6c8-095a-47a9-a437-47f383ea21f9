"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateUserTitle = exports.UserTitles = exports.UserTitle = exports.UpdateUser = exports.Users = exports.User = exports.isActive = exports.color = void 0;
const zod_1 = require("../../zod");
const client_1 = require("@prisma/client");
exports.color = zod_1.z.string().nonempty().nullable();
exports.isActive = zod_1.z.boolean();
exports.User = zod_1.z.object({
    id: zod_1.ZodHelper.Uuid,
    email: zod_1.ZodHelper.Email,
    role: zod_1.z.nativeEnum(client_1.UserRole),
    name: zod_1.ZodHelper.Localizations,
    description: zod_1.ZodHelper.Localizations,
    images: zod_1.z.array(zod_1.ZodHelper.Image).optional(),
    createdAt: zod_1.ZodHelper.ToDateTime,
    updatedAt: zod_1.ZodHelper.ToDateTime,
});
exports.Users = zod_1.z.array(exports.User);
exports.UpdateUser = zod_1.z
    .object({
    name: zod_1.ZodHelper.Localizations,
    description: zod_1.ZodHelper.Localizations,
})
    .partial();
exports.UserTitle = zod_1.z.object({
    id: zod_1.ZodHelper.Uuid,
    ownerId: zod_1.ZodHelper.Uuid.nullable(),
    isActive: exports.isActive,
    color: exports.color,
    createdAt: zod_1.ZodHelper.ToDateTime,
    updatedAt: zod_1.ZodHelper.ToDateTime,
});
exports.UserTitles = zod_1.z.array(exports.UserTitle);
exports.UpdateUserTitle = zod_1.z
    .object({
    color: exports.color,
    isActive: exports.isActive,
})
    .partial();
//# sourceMappingURL=dto.js.map