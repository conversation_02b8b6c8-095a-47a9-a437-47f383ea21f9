{"version": 3, "file": "user-title.service.js", "sourceRoot": "", "sources": ["../../../src/user/user-title.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAAgE;AAChE,2CAAkD;AAClD,oCAA+C;AAC/C,6CAA6C;AAC7C,yDAAsD;AAEtD,6DAA0D;AAGnD,IAAM,gBAAgB,GAAtB,MAAM,gBAAiB,SAAQ,0BAAW;IAC7C,YAA6B,MAAqB;QAC9C,KAAK,CAAC,YAAY,CAAC,CAAC;QADK,WAAM,GAAN,MAAM,CAAe;IAElD,CAAC;IAED,KAAK,CAAC,MAAM,CACR,EAAU,EACV,IAAiC,EACjC,IAAiB;QAEjB,MAAM,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;QAE/B,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;IAC1C,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU,EAAE,IAAiB;QACtC,MAAM,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;QAE7B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,EAAU,EAAE,IAAiB;QACzC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;QAE/C,IAAI,IAAI,CAAC,IAAI,KAAK,iBAAQ,CAAC,KAAK,EAAE,CAAC;YAC/B,IAAI,SAAS,CAAC,OAAO,KAAK,IAAI,CAAC,EAAE,EAAE,CAAC;gBAChC,MAAM,IAAI,2BAAkB,CAAC,GAAG,IAAA,iBAAQ,EAAC,eAAe,CAAC,CAAC,CAAC;YAC/D,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,GAAa;QACrB,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;IACvE,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QACnB,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC;YAC1C,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE;SACjC,CAAC,CAAC;IACP,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,EAAU;QAC1B,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAExC,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;QACzC,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAED,KAAK,CAAC,OAAO,CACT,KAAiC,EACjC,UAA2C;QAE3C,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;YACxC,GAAG,IAAA,0BAAkB,EAAC,UAAU,CAAC;YACjC,KAAK,EAAE;gBACH,GAAG,KAAK;gBACR,SAAS,EAAE,IAAI;aAClB;SACJ,CAAC,CAAC;IACP,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,IAAiC;QAC7C,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;YACtC,IAAI;SACP,CAAC,CAAC;IACP,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,IAAuC;QACpD,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC;YAC1C,IAAI;SACP,CAAC,CAAC;IACP,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,EAAU,EAAE,IAAiC;QACzD,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;YACtC,KAAK,EAAE;gBACH,EAAE;gBACF,SAAS,EAAE,IAAI;aAClB;YACD,IAAI;SACP,CAAC,CAAC;IACP,CAAC;IAED,KAAK,CAAC,UAAU,CACZ,KAAiC,EACjC,IAAiC;QAEjC,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC;YAC1C,KAAK,EAAE;gBACH,GAAG,KAAK;gBACR,SAAS,EAAE,IAAI;aAClB;YACD,IAAI;SACP,CAAC,CAAC;IACP,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,EAAU;QAC1B,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC,CAAC;IAC/D,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,KAAiC;QAClD,OAAO,MAAM,IAAI,CAAC,UAAU,CACxB;YACI,GAAG,KAAK;YACR,SAAS,EAAE,IAAI;SAClB,EACD,EAAE,SAAS,EAAE,IAAI,IAAI,EAAE,EAAE,CAC5B,CAAC;IACN,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,EAAU;QACtB,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;YACtC,KAAK,EAAE;gBACH,EAAE;gBACF,SAAS,EAAE,IAAI;aAClB;SACJ,CAAC,CAAC;IACP,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,KAAiC;QAC9C,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC;YAC1C,KAAK,EAAE;gBACH,GAAG,KAAK;gBACR,SAAS,EAAE,IAAI;aAClB;SACJ,CAAC,CAAC;IACP,CAAC;CACJ,CAAA;AApIY,4CAAgB;2BAAhB,gBAAgB;IAD5B,IAAA,mBAAU,GAAE;qCAE4B,8BAAa;GADzC,gBAAgB,CAoI5B"}