import type { Localization } from "$lib";

export type RatingStatus = "like" | "dislike";

export type PostEntity = {
  id: string;

  path: string;

  title: Localization[];
  body: Localization[];

  author: {
    id: string;
    name: Localization[];
    avatar: string | null;
  };

  rating: {
    likes: number;
    dislikes: number;
    status: RatingStatus | null;
  };

  usefulness: {
    value: number | null;
    count: number;
    totalValue: number | null;
  };

  tags: string[];

  createdAt: Date;
  updatedAt: Date;
};

export type CommentEntity = {
  id: string;

  path: string;

  isAnonymous: boolean;
  anonimityReason: string | null;
  author: {
    id: string;
    name: Localization[];
    avatar: string | null;
  } | null;

  rating: {
    likes: number;
    dislikes: number;
    status: RatingStatus | null;
  };

  body: Localization[] | null;

  childrenCount: number;

  deleteReason: string | null;

  /**
   * Used when the comment is newly created and must be placed at the top of siblings.
   * Server doesn't return this field, it's client-side only.
   */
  isMustBeTop?: boolean;

  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;
};
