import { ConfigService } from "@nestjs/config";
import { JwtService } from "@nestjs/jwt";
import * as prisma from "@prisma/client";
import { EmailService } from "src/email/email.service";
import { UserService } from "src/user/user.service";
import { EmailOtpService } from "src/email/email-otp.service";
import { UserRefreshTokenService } from "src/user/user-refresh-token.service";
type InfoDto = {
    ipAddress: string | null;
    userAgent: string | null;
};
type OtpDto = InfoDto & {
    email: string;
};
type RegisterDto = InfoDto & {
    referrerId: string | null;
    email: string;
    otp: string;
};
type LoginDto = InfoDto & {
    email: string;
    otp: string;
};
type RefreshDto = InfoDto & {
    refreshToken: string;
};
export declare class AuthService {
    private readonly configService;
    private readonly jwtService;
    private readonly emailService;
    private readonly userService;
    private readonly userOtpService;
    private readonly userRefreshTokenService;
    private readonly disableRegisterOtpCheck;
    private readonly disableLoginOtpCheck;
    private readonly instanceName;
    private readonly domain;
    private readonly otpSender;
    constructor(configService: ConfigService, jwtService: JwtService, emailService: EmailService, userService: UserService, userOtpService: EmailOtpService, userRefreshTokenService: UserRefreshTokenService);
    protected generateTokenPair(dto: {
        user: prisma.User;
        ipAddress: string | null;
        userAgent: string | null;
    }): Promise<{
        accessToken: {
            value: string;
            expiresAt: Date;
        };
        refreshToken: {
            value: string;
            expiresAt: Date;
        };
    }>;
    protected generateOtp(): string;
    otp(dto: OtpDto): Promise<boolean>;
    login(dto: LoginDto): Promise<{
        user: {
            id: string;
            email: string;
            role: prisma.$Enums.UserRole;
        };
        accessToken: {
            value: string;
            expiresAt: Date;
        };
        refreshToken: {
            value: string;
            expiresAt: Date;
        };
    }>;
    register(dto: RegisterDto): Promise<{
        user: {
            id: string;
            email: string;
            role: prisma.$Enums.UserRole;
        };
        accessToken: {
            value: string;
            expiresAt: Date;
        };
        refreshToken: {
            value: string;
            expiresAt: Date;
        };
    }>;
    refresh(dto: RefreshDto): Promise<{
        accessToken: {
            value: string;
            expiresAt: Date;
        };
        refreshToken: {
            value: string;
            expiresAt: Date;
        };
    }>;
}
export {};
