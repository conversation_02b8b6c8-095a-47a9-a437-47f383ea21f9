"use client";

import { useEffect, useState } from "react";
import { NewCalendarDate } from "@/utils/new-calendar";
import { Locale } from "@/app/types";

type Props = {
  locale: Locale;
}

const i18n = {
  en: {
    description: "Convert any date to our new calendar system",
    selectDate: "Select a date",
    isLeapYear: {
      title: "Is Leap Year",
      yes: "yes",
      no: "no",
    },
    dayOfYear: "Day of Year",
    day: "Day",
    month: "Month",
    year: "Year",
    commonString: "Common Format",
    isoString: "ISO Format",
    peaceDay: {
      title: "Peace Day",
      description: "The 365th day of the year that falls outside the regular month structure.",
    },
    leapDay: {
      title: "Leap Day",
      description: "The 366th day of the year that only occurs in leap years.",
    },
  },
  ru: {
    description: "Конвертировать любую дату в наш новый календарь",
    selectDate: "Выберите дату",
    isLeapYear: {
      title: "Високосный год",
      yes: "да",
      no: "нет",
    },
    dayOfYear: "День года",
    day: "День",
    month: "Месяц",
    year: "Год",
    commonString: "Бытовой формат",
    isoString: "Формат ISO",
    peaceDay: {
      title: "День мира",
      description: "365-й день года, который выпадает за пределы регулярной структуры месяцев.",
    },
    leapDay: {
      title: "Високосный день",
      description: "366-й день года, который встречается только в високосных годах.",
    },
  },
};

export function DateCalculator({ locale }: Props) {
  const t = i18n[locale];

  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [calculatedDate, setCalculatedDate] = useState<{
    newCalendarDate: NewCalendarDate;
    isLeapYear: boolean;
    dayOfYear: number;
    parsed: { year: number; month: number; day: number };
    commonString: string;
    isoString: string;
  } | null>(null);

  // Calculate new calendar date when selected date changes
  useEffect(() => {
    if (selectedDate) {
      const ncDate = new NewCalendarDate(selectedDate);
      const isLeapYear = ncDate.getIsLeapYear();
      const dayOfYear = ncDate.getDayOfYear();
      const parsed = ncDate.getParsed();
      const commonString = ncDate.toString().slice(0, 10);
      const isoString = ncDate.toISOString().slice(0, 10);

      setCalculatedDate({
        newCalendarDate: ncDate,
        isLeapYear,
        dayOfYear,
        parsed,
        commonString,
        isoString,
      });
    }
  }, [selectedDate]);

  // Handle date change
  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newDate = new Date(e.target.value);

    if (!isNaN(newDate.getTime())) {
      setSelectedDate(newDate);
    }
  };

  return (
    <div className="card">
      <div className="card-body">
        <p className="lead">{t.description}</p>

        <div className="row mb-4">
          <div className="col-md-6">
            <label htmlFor="date-picker" className="form-label">{t.selectDate}:</label>
            <input
              type="date"
              id="date-picker"
              className="form-control"
              value={selectedDate.toISOString().split("T")[0]}
              onChange={handleDateChange}
            />
          </div>
        </div>

        {calculatedDate && (
          <div className="row">
            <div className="col-md-12">
              <div className="alert alert-success">
                <h4 className="alert-heading">
                  {selectedDate.toLocaleDateString()} → {calculatedDate.commonString}
                </h4>
                <hr />
                <div className="row">
                  <div className="col-md-6">
                    <ul className="list-unstyled">
                      <li>
                        <strong>{t.isLeapYear.title}:</strong>
                        {" "}
                        {calculatedDate.isLeapYear ? t.isLeapYear.yes : t.isLeapYear.no}
                      </li>
                      <li>
                        <strong>{t.dayOfYear}:</strong>
                        {" "}
                        {calculatedDate.dayOfYear}
                      </li>
                      <li>
                        <strong>{t.day}:</strong>
                        {" "}
                        {calculatedDate.parsed.day}
                      </li>
                      <li>
                        <strong>{t.month}:</strong>
                        {" "}
                        {calculatedDate.parsed.month}
                      </li>
                      <li>
                        <strong>{t.year}:</strong>
                        {" "}
                        {calculatedDate.parsed.year}
                      </li>
                      <li>
                        <strong>{t.commonString}:</strong>
                        {" "}
                        {calculatedDate.commonString}
                      </li>
                      <li>
                        <strong>{t.isoString}:</strong>
                        {" "}
                        {calculatedDate.isoString}
                      </li>
                    </ul>
                  </div>
                  <div className="col-md-6">
                    {calculatedDate.parsed.month === 14 ? (
                      <div className="special-day-info">
                        <h5>
                          {calculatedDate.parsed.day === 1 ? t.peaceDay.title : t.leapDay.title}
                        </h5>
                        <p>
                          {calculatedDate.parsed.day === 1 ? t.peaceDay.description : t.leapDay.description}
                        </p>
                      </div>
                    ) : null}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );

}
