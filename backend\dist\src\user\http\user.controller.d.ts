import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "src/zod";
import { UserService } from "../user.service";
import { UserTitleService } from "../user-title.service";
import * as Dto from "./dto";
import { CurrentUser } from "src/auth/types";
export declare class UserController {
    private readonly userService;
    private readonly userTitleService;
    constructor(userService: UserService, userTitleService: UserTitleService);
    getUsers(page: number, size: number): Promise<Dto.User[]>;
    getUser(id: string): Promise<Dto.User>;
    updateUser(id: string, body: Dto.UpdateUser, currentUser: CurrentUser): Promise<Dto.User>;
    uploadUserImage(id: string, currentUser: CurrentUser, file: Express.Multer.File): Promise<ZodHelper.Image>;
    getUserTitles(id: string, active: boolean, page: number, size: number): Promise<Dto.UserTitle[]>;
    updateUserTitle(id: string, titleId: string, body: Dto.UpdateUserTitle, user: CurrentUser): Promise<Dto.UserTitle>;
}
