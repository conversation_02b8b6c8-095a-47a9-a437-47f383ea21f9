import * as prisma from "@prisma/client";
import { z, <PERSON>od<PERSON><PERSON><PERSON> } from "src/zod";

const otp = z.string().nonempty().length(6);

export type Otp = ZodHelper.Infer<typeof Otp>;
export const Otp = z.object({
    email: z.string().email(),
});

export type Me = ZodHelper.Infer<typeof Me>;
export const Me = z.object({
    id: ZodHelper.Uuid,
    email: z.string().email(),
    role: z.nativeEnum(prisma.UserRole),

    name: <PERSON>od<PERSON>elper.Localizations,
    description: ZodHelper.Localizations,
    images: z.array(ZodHelper.Image).optional(),

    joinedAt: z.date(),
});

export type Register = ZodHelper.Infer<typeof Register>;
export const Register = z.object({
    referrerId: z.string().uuid().nullable().optional(),
    email: z.string().email(),
    otp,
});

export type Login = ZodHelper.Infer<typeof Login>;
export const Login = z.object({
    email: z.string().email(),
    otp,
});

export type Logout = ZodHelper.Infer<typeof Logout>;
export const Logout = z.object({
    refreshToken: z.string().nonempty(),
});
