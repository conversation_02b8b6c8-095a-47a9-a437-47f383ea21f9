import { GraphQLDefinitionsFactory } from "@nestjs/graphql";
import fs from "fs/promises";
import path from "path";

const debug = process.argv.includes("--debug");
const watch = process.argv.includes("--watch");

const definitionsFactory = new GraphQLDefinitionsFactory();

const additionalHeader = `
import type { z } from "zod";

export type IResolver<
    TQuery extends keyof IQuery,
    TMutation extends keyof IMutation,
> = Pick<IQuery, TQuery> & Pick<IMutation, TMutation>;
type Satisfies<T, U extends T> = U;

type Satisfies<
    TGql extends Record<string, unknown>,
    TZod extends z.ZodTypeAny,
> =
    TGql extends z.infer<TZod>
        ? z.infer<TZod> extends TGql
            ? TGql
            : never
        : never;
`;

function generateZodSatisfies(typename: string) {
    //     return `
    // export type ${typename}SatisfiesZodInput<T extends ${typename}> = T;
    // export type ${typename}SatisfiesZodOutput<T extends ${typename}> = T;
    // export type ${typename}SatisfiesZod<T extends ${typename}> = ${typename}SatisfiesZodInput<T> | ${typename}SatisfiesZodOutput<T>;
    // `;

    return `
export type Satisfies${typename}<T extends z.ZodTypeAny> = Satisfies<${typename}, T>;
    `;

    //     return `
    // export type ${typename}SatisfiesZodInput<T extends z.ZodTypeAny> = Satisfies<
    //     z.input<T>,
    //     ${typename}
    // >;
    // export type ${typename}SatisfiesZodOutput<T extends z.ZodTypeAny> = Satisfies<
    //     ${typename},
    //     z.output<T>
    // >;
    // export type ${typename}SatisfiesZod<T extends z.ZodTypeAny> = ${typename}SatisfiesZodInput<T> | ${typename}SatisfiesZodOutput<T>;
    // `;
}

definitionsFactory
    .generate({
        typePaths: ["./src/**/*.graphql"],
        // typePaths: ["./src/schema.graphql"],
        path: "./src/graphql.d.ts",
        // path: join(process.cwd(), "src/graphql.d.ts"),
        outputAs: "interface",
        customScalarTypeMapping: {
            DateTime: "string | number | Date",
        },
        additionalHeader,
        enumsAsTypes: true,
        emitTypenameField: true,
        debug,
        watch,
    })
    .then(async () => {
        const filePath = path.join(process.cwd(), "src/graphql.d.ts");
        const fileContent = await fs.readFile(filePath, "utf8");

        const interfaces = [
            ...fileContent.matchAll(/export interface (.+?) \{/g),
        ];

        const satisfiesBlocks = interfaces.map((match) => {
            const typename = match[1];

            if (!typename) {
                throw new Error("No typename found for: " + match[0]);
            }

            return generateZodSatisfies(typename);
        });

        const updatedFileContent = [
            fileContent.replaceAll("?:", ":"),
            ...satisfiesBlocks,
        ].join("\n");

        await fs.writeFile(filePath, updatedFileContent);
    });
