"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateCommuneMemberInput = exports.CreateCommuneMemberInput = exports.UpdateCommuneInput = exports.CreateCommuneInput = exports.Communes = exports.Commune = exports.communeTypename = exports.CommuneMembers = exports.CommuneMember = exports.CommuneMemberType = exports.communeMemberTypename = void 0;
const prisma = require("@prisma/client");
const zod_1 = require("../../zod");
exports.communeMemberTypename = "CommuneMember";
exports.CommuneMemberType = zod_1.z.nativeEnum(prisma.CommuneMemberType);
exports.CommuneMember = zod_1.z.object({
    __typename: zod_1.ZodHelper.Typename(exports.communeMemberTypename),
    id: zod_1.ZodHelper.Uuid,
    actorType: exports.CommuneMemberType,
    actorId: zod_1.ZodHelper.Uuid,
    joinedAt: zod_1.ZodHelper.ToDateTime,
    leftAt: zod_1.ZodHelper.ToDateTime.nullable(),
    createdAt: zod_1.ZodHelper.ToDateTime,
    updatedAt: zod_1.ZodHelper.ToDateTime,
});
exports.CommuneMembers = zod_1.z.array(exports.CommuneMember);
exports.communeTypename = "Commune";
exports.Commune = zod_1.z.object({
    __typename: zod_1.ZodHelper.Typename(exports.communeTypename),
    id: zod_1.ZodHelper.Uuid,
    members: zod_1.z.array(exports.CommuneMember).min(1),
    createdAt: zod_1.ZodHelper.ToDateTime,
    updatedAt: zod_1.ZodHelper.ToDateTime,
});
exports.Communes = zod_1.z.array(exports.Commune);
exports.CreateCommuneInput = zod_1.z.object({
    headUserId: zod_1.ZodHelper.Uuid,
    name: zod_1.ZodHelper.Localizations,
    description: zod_1.ZodHelper.Localizations,
});
exports.UpdateCommuneInput = zod_1.z.object({
    id: zod_1.ZodHelper.Uuid,
});
exports.CreateCommuneMemberInput = zod_1.z.object({
    communeId: zod_1.ZodHelper.Uuid,
    actorType: exports.CommuneMemberType,
    actorId: zod_1.ZodHelper.Uuid,
});
exports.UpdateCommuneMemberInput = zod_1.z.object({
    id: zod_1.ZodHelper.Uuid,
});
//# sourceMappingURL=dto.js.map