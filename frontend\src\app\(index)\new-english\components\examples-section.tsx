"use client";

import { useState } from "react";
import { examples } from "../examples";
import { DictionaryKey } from "../dictionaries";
import { Locale } from "@/app/types";
import { darkDictionaryColors, lightDictionaryColors } from "../dictionary-colors";

type Props = {
  locale: Locale;
}

const i18n = {
  en: {
    title: "Word Examples",
    description: "See how common English words transform in our phonetic system",
    hideDetails: "Hide Details",
    showDetails: "Show Details",
    soundBreakdown: "Sound Breakdown",
    phoneticSounds: "Phonetic Sounds",
    newEnglishLetter: "New English Letter(s)",
  },

  ru: {
    title: "Примеры слов",
    description: "Посмотрите, как обычные английские слова преобразуются в нашей фонетической системе",
    hideDetails: "Скрыть детали",
    showDetails: "Показать детали",
    soundBreakdown: "Разбор звуков",
    phoneticSounds: "Фонетические звуки",
    newEnglishLetter: "Буквы Нового английского",
  },
};

export function ExamplesSection({ locale }: Props) {
  const t = i18n[locale];

  const [expandedExample, setExpandedExample] = useState<number | null>(null);

  const toggleExample = (index: number) => {
    if (expandedExample === index) {
      setExpandedExample(null);
    } else {
      setExpandedExample(index);
    }
  };

  return (
    <section className="mb-5">
      <h2>{t.title}</h2>
      <div className="card">
        <div className="card-body">
          <p className="lead">{t.description}:</p>
          <ul className="list-group list-group-flush">
            {examples.map((example, index) => (
              <li key={index} className="list-group-item">
                <div
                  className="d-flex justify-content-between align-items-center cursor-pointer"
                  onClick={() => toggleExample(index)}
                  style={{ cursor: "pointer" }}
                >
                  <div>
                    <strong>{example.english}</strong>
                    <span className=" ms-2">
                      ({Object.entries(example.transcriptions).map(([dict, transcription], i) => (
                        <span key={dict}>
                          {i > 0 && ", "}
                          <span style={{ color: `#${darkDictionaryColors[dict as DictionaryKey]}` }}>
                            {transcription}
                          </span>
                        </span>
                      ))})
                    </span>
                    <span className="ms-2">→</span>
                    <span className="ms-2 fw-bold">
                      {typeof example.newEnglish === "string" ? (
                        <span className="text-dark">{example.newEnglish}</span>
                      ) : (
                        Object.entries(example.newEnglish).map(([dict, translation], i) => (
                          <span key={dict}>
                            {i > 0 && ", "}
                            <span style={{ color: `#${darkDictionaryColors[dict as DictionaryKey]}` }}>
                              {translation}
                            </span>
                          </span>
                        ))
                      )}
                    </span>
                  </div>
                  <button className="btn btn-sm btn-outline-primary">
                    {expandedExample === index ? t.hideDetails : t.showDetails}
                  </button>
                </div>

                {expandedExample === index && (
                  <div className="mt-3">
                    <h6>{t.soundBreakdown}:</h6>
                    <table className="table table-sm">
                      <thead>
                        <tr>
                          <th>{t.phoneticSounds}</th>
                          <th>{t.newEnglishLetter}</th>
                        </tr>
                      </thead>
                      <tbody>
                        {example.breakdown.map((item, i) => {
                          // Handle both simple mapping and array of dictionary-specific mappings
                          if (Array.isArray(item)) {
                            // This is an array of dictionary-specific mappings
                            // Render all variants on the same line
                            return (
                              <tr key={i}>
                                <td>
                                  {item.map((variant, j) => (
                                    <span key={j} className="me-2">
                                      {variant.sounds.map((sound, k) => (
                                        <span
                                          key={k}
                                          className="badge rounded-pill me-1 font-monospace"
                                          style={{
                                            backgroundColor: `#${lightDictionaryColors[variant.dictionaryKey]}`,
                                            color: "#000000",
                                            padding: "6px 12px",
                                            marginBottom: "6px",
                                            fontSize: "1.2rem",
                                            fontWeight: "normal",
                                          }}
                                        >
                                          {sound}
                                        </span>
                                      ))}
                                    </span>
                                  ))}
                                </td>
                                <td>
                                  <strong style={{ fontSize: "1.5rem", display: "inline-block", padding: "4px 0" }}>
                                    {(() => {
                                      // Check if all mappings are the same
                                      const firstMapping = item[0].mapping;
                                      const allSameMapping = item.every(variant => variant.mapping === firstMapping);

                                      if (allSameMapping) {
                                        // If all mappings are the same, display as "any" in black
                                        return <span style={{ color: "#000000" }}>{firstMapping}</span>;
                                      } else {
                                        // If mappings differ, display each with its dictionary color
                                        return item.map((variant, j) => (
                                          <span key={j}>
                                            {j > 0 && ", "}
                                            <span style={{ color: `#${darkDictionaryColors[variant.dictionaryKey]}` }}>
                                              {variant.mapping}
                                            </span>
                                          </span>
                                        ));
                                      }
                                    })()}
                                  </strong>
                                </td>
                              </tr>
                            );
                          } else {
                            // This is a simple mapping (consider it as "any")
                            return (
                              <tr key={i}>
                                <td>
                                  {item.sounds.map((sound, j) => (
                                    <span
                                      key={j}
                                      className="badge rounded-pill me-2 font-monospace"
                                      style={{
                                        backgroundColor: `#${lightDictionaryColors["any"]}`,
                                        color: "#000000",
                                        border: "1px solid #CCCCCC",
                                        padding: "6px 12px",
                                        marginBottom: "6px",
                                        fontSize: "1.2rem",
                                        fontWeight: "normal",
                                      }}
                                    >
                                      {sound}
                                    </span>
                                  ))}
                                </td>
                                <td>
                                  <strong style={{ fontSize: "1.5rem", display: "inline-block", padding: "4px 0" }}>
                                    {item.mapping}
                                  </strong>
                                </td>
                              </tr>
                            );
                          }
                        })}
                      </tbody>
                    </table>
                  </div>
                )}
              </li>
            ))}
          </ul>
        </div>
      </div>
    </section>
  );
}
