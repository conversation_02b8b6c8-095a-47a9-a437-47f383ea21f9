import { useState } from "react";
import { Form, InputGroup, Dropdown } from "react-bootstrap";
import { Localization } from "@/utils/find-localization";
import { useLocale } from "next-intl";
import { Locale } from "@/app/types";

export interface LocalizedInputProps {
  id: string;
  label: string;
  placeholder: string;
  required?: boolean;
  value: Localization[];
  onChange: (value: Localization[]) => void;
}

const i18n = {
  en: {
    languages: {
      en: "English",
      ru: "Russian",
    },
    providedTranslations: "Provided translations:",
  },

  ru: {
    languages: {
      en: "Английский",
      ru: "Русский",
    },
    providedTranslations: "Указанные переводы:",
  },
};

export function LocalizedInput({ id, label, placeholder, required, value, onChange }: LocalizedInputProps) {
  const locale = useLocale() as Locale;
  const t = i18n[locale];

  const [selectedLanguage, setSelectedLanguage] = useState<Locale>("en");

  // Find the value for the currently selected language
  const getCurrentValue = () => {
    const localization = value.find(val => val.locale === selectedLanguage);
    return localization?.value || "";
  };

  // Update the value for the currently selected language
  const handleChange = (newValue: string) => {
    const updatedValues = [...value];
    const existingIndex = updatedValues.findIndex(val => val.locale === selectedLanguage);

    if (existingIndex >= 0) {
      updatedValues[existingIndex] = { locale: selectedLanguage as Locale, value: newValue };
    } else {
      updatedValues.push({ locale: selectedLanguage as Locale, value: newValue });
    }

    onChange(updatedValues);
  };

  // Switch the selected language
  const handleLanguageChange = (langCode: Locale) => {
    setSelectedLanguage(langCode);
  };

  // Get the display name of the current language
  const getLanguageDisplay = () => {
    return selectedLanguage.toUpperCase();
  };

  return (
    <Form.Group className="mb-3" controlId={id}>
      <Form.Label>
        {label} {required && <span className="text-danger">*</span>}
      </Form.Label>
      <InputGroup>
        <Dropdown>
          <Dropdown.Toggle
            variant="outline-secondary"
            id={`dropdown-${id}`}
            style={{ width: "60px", display: "flex", justifyContent: "space-between", alignItems: "center" }}
          >
            {getLanguageDisplay()}
          </Dropdown.Toggle>

          <Dropdown.Menu>
            <Dropdown.Item
              onClick={() => handleLanguageChange("en")}
              active={selectedLanguage === "en"}
            >
              {t.languages.en}
            </Dropdown.Item>

            <Dropdown.Item
              onClick={() => handleLanguageChange("ru")}
              active={selectedLanguage === "ru"}
            >
              {t.languages.ru}
            </Dropdown.Item>
          </Dropdown.Menu>
        </Dropdown>

        <Form.Control
          type="text"
          placeholder={placeholder}
          value={getCurrentValue()}
          onChange={(e) => handleChange(e.target.value)}
          required={required}
        />
      </InputGroup>

      {value.length > 0 && (
        <div className="mt-2 small text-muted">
          <div>{t.providedTranslations}</div>
          <ul className="list-unstyled mb-0 mt-1">
            {value.filter(Boolean).map(val => (
              <li key={val.locale} className="badge bg-light text-dark me-1">
                {t.languages[val.locale]}: {val.value}
              </li>
            ))}
          </ul>
        </div>
      )}
    </Form.Group>
  );
}
