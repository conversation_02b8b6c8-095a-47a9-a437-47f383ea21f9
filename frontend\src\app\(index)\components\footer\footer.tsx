import Link from "next/link";
import Image from "next/image";
import styles from "./footer.module.css";
import { Locale } from "@/app/types";

// import pikabuIcon from "./pikabu-icon.svg";
import boostyIcon from "./boosty-icon.svg";

const i18n = {
  ru: {
    logoAlt: "Логотип Цифрового Сообщества «Коммуна»",
    title: "О нас",
    about: "Цифровое Сообщество «Коммуна» - где традиции встречаются с инновациями. Мы строим жизнь в сообществе, которое поддерживает сотрудничество, рост и значимые связи.",
    social: {
      telegram: "https://t.me/ds_commune_ru",
    },
    links: {
      title: "Cсылк<PERSON>",
      news: "Новости",
      communes: "Сообщества",
      users: "Пользователи",
      newCalendar: "Новый календарь",
      newEnglish: "Новый английский",
      theLaw: "Право",
      rules: "Правила",
    },
    contactUs: {
      title: "Связаться с нами",
    },
    legal: {
      disclaimer: "Цифровое Сообщество «Коммуна». Все права защищены.",
    },
  },

  en: {
    logoAlt: "Digital Society «Commune» Logo",
    title: "About Us",
    about: "Digital Society «Commune» - where tradition meets innovation. We're building a vibrant community dedicated to fostering collaboration, shared growth, and meaningful connections.",
    social: {
      telegram: "https://t.me/ds_commune_en",
    },
    links: {
      title: "Quick Links",
      news: "News",
      communes: "Communes",
      users: "Users",
      newCalendar: "New Calendar",
      newEnglish: "New English",
      theLaw: "The Law",
      rules: "Rules",
    },
    contactUs: {
      title: "Contact Us",
    },
    legal: {
      disclaimer: "Digital Society «Commune». All rights reserved.",
    },
  },
};

type Props = {
  locale: Locale;
}

export function Footer({ locale }: Props) {
  const t = i18n[locale];

  const contactUsEmail = process.env.NEXT_PUBLIC_CONTACT_EMAIL || "<EMAIL>";

  return (
    <footer className={styles.footer}>
      <div className="container">
        <div className="row gy-4">
          <div className="col-lg-4 col-md-6">
            <div className={styles.aboutSection}>
              <Link href="/" className="d-inline-block mb-3">
                <Image
                  src="/images/full-v3-transparent.svg"
                  alt={t.logoAlt}
                  width={80}
                  height={80}
                  className={styles.footerLogo}
                />
              </Link>
              <h5 className="fw-bold mb-3">{t.title}</h5>
              <p className="text-muted">{t.about}</p>
              <div className={styles.socialIcons}>
                <a
                  href={t.social.telegram}
                  className={styles.socialIcon}
                  aria-label="Telegram"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <i className="bi bi-telegram" />
                </a>
                {/* <a
                  href="https://pikabu.ru/ds_commune"
                  className={styles.socialIcon}
                  aria-label="Pikabu"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Image src={pikabuIcon} alt="Pikabu" width={24} height={24} />
                </a> */}
                <a
                  href="https://github.com/ds-commune"
                  className={styles.socialIcon}
                  aria-label="GitHub"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <i className="bi bi-github" />
                </a>
                <a
                  href="https://boosty.to/ds.commune"
                  className={styles.socialIcon}
                  aria-label="Boosty"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Image src={boostyIcon} alt="Boosty" width={24} height={24} />
                </a>
              </div>
            </div>
          </div>
          <div className="col-lg-4 col-md-6">
            <h5 className="fw-bold mb-3">{t.links.title}</h5>
            <ul className={`list-unstyled ${styles.quickLinks}`}>
              <li>
                <Link href="/the-law">{t.links.theLaw}</Link>
              </li>
              <li>
                <Link href="/rules">{t.links.rules}</Link>
              </li>
              <li>
                <Link href="/new-english">{t.links.newEnglish}</Link>
              </li>
              <li>
                <Link href="/new-calendar">{t.links.newCalendar}</Link>
              </li>
              <li>
                <Link href="/news">{t.links.news}</Link>
              </li>
              <li>
                <Link href="/users">{t.links.users}</Link>
              </li>
              <li>
                <Link href="/communes">{t.links.communes}</Link>
              </li>
            </ul>
          </div>
          <div className="col-lg-4 col-md-12">
            <h5 className="fw-bold mb-3">{t.contactUs.title}</h5>
            <ul className={`list-unstyled ${styles.contactInfo}`}>
              <li>
                <i className="bi bi-envelope" />
                <a href={`mailto:${contactUsEmail}`} target="_blank" rel="noopener noreferrer">{contactUsEmail}</a>
              </li>
            </ul>
          </div>
        </div>

        <hr className={styles.divider} />

        <div className={styles.copyright}>
          <p>{new Date().getFullYear()} {t.legal.disclaimer}</p>
        </div>
      </div>
    </footer>
  );
}
