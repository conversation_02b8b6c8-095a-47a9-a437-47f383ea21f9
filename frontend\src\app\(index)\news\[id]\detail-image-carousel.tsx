"use client";

import { useState } from "react";
import { Carousel } from "react-bootstrap";
import styles from "./detail-image-carousel.module.css";
import { useLocale } from "next-intl";
import { Locale } from "@/app/types";

interface PostImage {
  id: string;
  url: string;
  source: string;
}

interface DetailImageCarouselProps {
  images: PostImage[];
}

const i18n = {
  en: {
    noImagesAvailable: "No images available",
  },
  ru: {
    noImagesAvailable: "Нет изображений",
  },
};

export function DetailImageCarousel({ images }: DetailImageCarouselProps) {
  const locale = useLocale() as Locale;
  const t = i18n[locale];

  const [index, setIndex] = useState(0);

  const handleSelect = (selectedIndex: number) => {
    setIndex(selectedIndex);
  };

  // If no images, show placeholder
  if (!images || images.length === 0) {
    return (
      <div
        className="bg-light text-center rounded"
        style={{ height: "300px", display: "flex", alignItems: "center", justifyContent: "center" }}
      >
        <span className="text-muted">{t.noImagesAvailable}</span>
      </div>
    );
  }

  return (
    <div className={styles.carouselContainer}>
      <Carousel
        activeIndex={index}
        onSelect={handleSelect}
        interval={null}
        indicators={true}
        controls={images.length > 1}
        className={styles.carousel}
      >
        {images.map((image, idx) => (
          <Carousel.Item key={image.id || idx} className={styles.carouselItem}>
            <div className={styles.imageContainer}>
              {/* eslint-disable-next-line @next/next/no-img-element */}
              <img
                src={`/api/images/${image.url}`}
                alt={`Post image ${idx + 1}`}
                className={styles.carouselImage}
              />
            </div>
          </Carousel.Item>
        ))}
      </Carousel>

      {images.length > 1 && (
        <div className={styles.indicators}>
          {images.map((_, idx) => (
            <button
              key={idx}
              className={`${styles.indicator} ${idx === index ? styles.activeIndicator : ""}`}
              onClick={() => setIndex(idx)}
              aria-label={`Slide ${idx + 1}`}
            />
          ))}
        </div>
      )}
    </div>
  );
}
